<?php
/**
 * نموذج الحسابات المحاسبية
 * Chart of Accounts Model
 */

class Account {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الحسابات
     */
    public function getAllAccounts($activeOnly = true, $parentId = null) {
        try {
            $query = "SELECT 
                        coa.account_id, coa.account_code, coa.account_name, 
                        coa.account_type, coa.account_level, coa.parent_account_id,
                        coa.is_main_account, coa.is_active, coa.created_at,
                        parent.account_name as parent_account_name,
                        COALESCE(SUM(at.debit_amount), 0) as total_debit,
                        COALESCE(SUM(at.credit_amount), 0) as total_credit,
                        COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as balance
                      FROM chart_of_accounts coa
                      LEFT JOIN chart_of_accounts parent ON coa.parent_account_id = parent.account_id
                      LEFT JOIN account_transactions at ON coa.account_id = at.account_id AND at.is_posted = 1";
            
            $params = [];
            $conditions = [];
            
            if ($activeOnly) {
                $conditions[] = "coa.is_active = 1";
            }
            
            if ($parentId !== null) {
                if ($parentId === 0) {
                    $conditions[] = "coa.parent_account_id IS NULL";
                } else {
                    $conditions[] = "coa.parent_account_id = ?";
                    $params[] = $parentId;
                }
            }
            
            if (!empty($conditions)) {
                $query .= " WHERE " . implode(' AND ', $conditions);
            }
            
            $query .= " GROUP BY coa.account_id, coa.account_code, coa.account_name, 
                               coa.account_type, coa.account_level, coa.parent_account_id,
                               coa.is_main_account, coa.is_active, coa.created_at, parent.account_name
                       ORDER BY coa.account_code";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all accounts error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على حساب بالمعرف
     */
    public function getAccountById($accountId) {
        try {
            $query = "SELECT 
                        coa.*, parent.account_name as parent_account_name,
                        COALESCE(SUM(at.debit_amount), 0) as total_debit,
                        COALESCE(SUM(at.credit_amount), 0) as total_credit,
                        COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as balance
                      FROM chart_of_accounts coa
                      LEFT JOIN chart_of_accounts parent ON coa.parent_account_id = parent.account_id
                      LEFT JOIN account_transactions at ON coa.account_id = at.account_id AND at.is_posted = 1
                      WHERE coa.account_id = ?
                      GROUP BY coa.account_id";
            
            return $this->db->selectOne($query, [$accountId]);
            
        } catch (Exception $e) {
            error_log("Get account by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء حساب جديد
     */
    public function createAccount($data) {
        try {
            // التحقق من عدم وجود كود الحساب
            if ($this->accountCodeExists($data['account_code'])) {
                throw new Exception('كود الحساب موجود بالفعل');
            }
            
            // تحديد مستوى الحساب
            $accountLevel = 1;
            if (!empty($data['parent_account_id'])) {
                $parentAccount = $this->getAccountById($data['parent_account_id']);
                if ($parentAccount) {
                    $accountLevel = $parentAccount['account_level'] + 1;
                }
            }
            
            $query = "INSERT INTO chart_of_accounts (
                        account_code, account_name, account_type, parent_account_id,
                        account_level, is_main_account, description, is_active, 
                        created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['account_code'],
                $data['account_name'],
                $data['account_type'],
                $data['parent_account_id'] ?? null,
                $accountLevel,
                $data['is_main_account'] ?? 0,
                $data['description'] ?? null,
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $accountId = $this->db->insert($query, $params);
            
            if ($accountId) {
                logActivity('إنشاء حساب', "تم إنشاء الحساب: {$data['account_name']}");
            }
            
            return $accountId;
            
        } catch (Exception $e) {
            error_log("Create account error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الحساب
     */
    public function updateAccount($accountId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['account_name'])) {
                $setParts[] = "account_name = ?";
                $params[] = $data['account_name'];
            }
            
            if (isset($data['account_type'])) {
                $setParts[] = "account_type = ?";
                $params[] = $data['account_type'];
            }
            
            if (isset($data['parent_account_id'])) {
                $setParts[] = "parent_account_id = ?";
                $params[] = $data['parent_account_id'];
                
                // تحديث مستوى الحساب
                $accountLevel = 1;
                if (!empty($data['parent_account_id'])) {
                    $parentAccount = $this->getAccountById($data['parent_account_id']);
                    if ($parentAccount) {
                        $accountLevel = $parentAccount['account_level'] + 1;
                    }
                }
                $setParts[] = "account_level = ?";
                $params[] = $accountLevel;
            }
            
            if (isset($data['is_main_account'])) {
                $setParts[] = "is_main_account = ?";
                $params[] = $data['is_main_account'];
            }
            
            if (isset($data['description'])) {
                $setParts[] = "description = ?";
                $params[] = $data['description'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $accountId;
            
            $query = "UPDATE chart_of_accounts SET " . implode(', ', $setParts) . " WHERE account_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث حساب', "تم تحديث الحساب ID: {$accountId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update account error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الحساب (إلغاء تفعيل)
     */
    public function deleteAccount($accountId) {
        try {
            // التحقق من عدم وجود حسابات فرعية
            $subAccountsCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_account_id = ? AND is_active = 1",
                [$accountId]
            );
            
            if ($subAccountsCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الحساب لوجود حسابات فرعية');
            }
            
            // التحقق من عدم وجود معاملات
            $transactionsCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM account_transactions WHERE account_id = ?",
                [$accountId]
            );
            
            if ($transactionsCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الحساب لوجود معاملات مرتبطة به');
            }
            
            $query = "UPDATE chart_of_accounts SET is_active = 0, updated_at = NOW() WHERE account_id = ?";
            $result = $this->db->update($query, [$accountId]);
            
            if ($result) {
                logActivity('حذف حساب', "تم حذف الحساب ID: {$accountId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete account error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود الحساب
     */
    private function accountCodeExists($accountCode, $excludeAccountId = null) {
        $query = "SELECT COUNT(*) as count FROM chart_of_accounts WHERE account_code = ?";
        $params = [$accountCode];
        
        if ($excludeAccountId) {
            $query .= " AND account_id != ?";
            $params[] = $excludeAccountId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على الحسابات الرئيسية
     */
    public function getMainAccounts($accountType = null) {
        try {
            $query = "SELECT account_id, account_code, account_name, account_type
                      FROM chart_of_accounts 
                      WHERE parent_account_id IS NULL AND is_active = 1";
            
            $params = [];
            
            if ($accountType) {
                $query .= " AND account_type = ?";
                $params[] = $accountType;
            }
            
            $query .= " ORDER BY account_code";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get main accounts error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * البحث في الحسابات
     */
    public function searchAccounts($searchTerm, $accountType = null) {
        try {
            $query = "SELECT 
                        account_id, account_code, account_name, account_type, account_level
                      FROM chart_of_accounts 
                      WHERE (account_name LIKE ? OR account_code LIKE ?) AND is_active = 1";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($accountType) {
                $query .= " AND account_type = ?";
                $params[] = $accountType;
            }
            
            $query .= " ORDER BY account_code LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search accounts error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * أنواع الحسابات
     */
    public function getAccountTypes() {
        return [
            'assets' => 'الأصول',
            'liabilities' => 'الخصوم',
            'equity' => 'حقوق الملكية',
            'revenue' => 'الإيرادات',
            'expenses' => 'المصروفات'
        ];
    }
    
    /**
     * الحصول على شجرة الحسابات
     */
    public function getAccountsTree($parentId = null, $level = 0) {
        $accounts = $this->getAllAccounts(true, $parentId);
        $tree = [];
        
        foreach ($accounts as $account) {
            $account['level'] = $level;
            $account['children'] = $this->getAccountsTree($account['account_id'], $level + 1);
            $tree[] = $account;
        }
        
        return $tree;
    }
}

?>

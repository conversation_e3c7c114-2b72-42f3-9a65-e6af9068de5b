<?php
/**
 * نموذج الأصناف
 * Item Model
 */

class Item {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الأصناف
     */
    public function getAllItems($activeOnly = true, $limit = null, $offset = 0) {
        try {
            $query = "SELECT 
                        i.item_id, i.item_code, i.item_name, i.item_description,
                        i.barcode, i.item_type, i.cost_method, i.min_stock_level,
                        i.max_stock_level, i.reorder_level, i.is_active,
                        i.has_serial_numbers, i.has_expiry_date, i.created_at,
                        c.category_name, u.unit_name as base_unit_name, u.unit_symbol
                      FROM items i
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      LEFT JOIN units u ON i.base_unit_id = u.unit_id";
            
            $params = [];
            
            if ($activeOnly) {
                $query .= " WHERE i.is_active = 1";
            }
            
            $query .= " ORDER BY i.item_name";
            
            if ($limit) {
                $query .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all items error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على صنف بالمعرف
     */
    public function getItemById($itemId) {
        try {
            $query = "SELECT 
                        i.*, c.category_name, u.unit_name as base_unit_name, u.unit_symbol
                      FROM items i
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      LEFT JOIN units u ON i.base_unit_id = u.unit_id
                      WHERE i.item_id = ?";
            
            return $this->db->selectOne($query, [$itemId]);
            
        } catch (Exception $e) {
            error_log("Get item by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على وحدات الصنف
     */
    public function getItemUnits($itemId) {
        try {
            $query = "SELECT 
                        iu.*, u.unit_name, u.unit_symbol, u.unit_type
                      FROM item_units iu
                      JOIN units u ON iu.unit_id = u.unit_id
                      WHERE iu.item_id = ?
                      ORDER BY iu.is_default DESC, u.unit_name";
            
            return $this->db->select($query, [$itemId]);
            
        } catch (Exception $e) {
            error_log("Get item units error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إنشاء صنف جديد
     */
    public function createItem($data) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من عدم وجود كود الصنف
            if ($this->itemCodeExists($data['item_code'])) {
                throw new Exception('كود الصنف موجود بالفعل');
            }
            
            // إدراج الصنف الأساسي
            $query = "INSERT INTO items (
                        item_code, item_name, item_description, category_id, base_unit_id,
                        barcode, item_type, cost_method, min_stock_level, max_stock_level,
                        reorder_level, shelf_life_days, is_active, has_serial_numbers,
                        has_expiry_date, notes, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['item_code'],
                $data['item_name'],
                $data['item_description'] ?? null,
                $data['category_id'] ?? null,
                $data['base_unit_id'],
                $data['barcode'] ?? null,
                $data['item_type'] ?? 'product',
                $data['cost_method'] ?? 'average',
                $data['min_stock_level'] ?? 0,
                $data['max_stock_level'] ?? 0,
                $data['reorder_level'] ?? 0,
                $data['shelf_life_days'] ?? null,
                $data['is_active'] ?? 1,
                $data['has_serial_numbers'] ?? 0,
                $data['has_expiry_date'] ?? 0,
                $data['notes'] ?? null,
                $_SESSION['user_id'] ?? null
            ];
            
            $itemId = $this->db->insert($query, $params);
            
            if (!$itemId) {
                throw new Exception('فشل في إنشاء الصنف');
            }
            
            // إضافة الوحدة الأساسية
            $this->addItemUnit($itemId, $data['base_unit_id'], 1.0, 
                             $data['purchase_price'] ?? 0, 
                             $data['sale_price'] ?? 0, 
                             $data['wholesale_price'] ?? 0, 
                             true);
            
            $this->db->commit();
            
            logActivity('إنشاء صنف', "تم إنشاء الصنف: {$data['item_name']}");
            
            return $itemId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create item error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الصنف
     */
    public function updateItem($itemId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['item_name'])) {
                $setParts[] = "item_name = ?";
                $params[] = $data['item_name'];
            }
            
            if (isset($data['item_description'])) {
                $setParts[] = "item_description = ?";
                $params[] = $data['item_description'];
            }
            
            if (isset($data['category_id'])) {
                $setParts[] = "category_id = ?";
                $params[] = $data['category_id'];
            }
            
            if (isset($data['barcode'])) {
                $setParts[] = "barcode = ?";
                $params[] = $data['barcode'];
            }
            
            if (isset($data['item_type'])) {
                $setParts[] = "item_type = ?";
                $params[] = $data['item_type'];
            }
            
            if (isset($data['cost_method'])) {
                $setParts[] = "cost_method = ?";
                $params[] = $data['cost_method'];
            }
            
            if (isset($data['min_stock_level'])) {
                $setParts[] = "min_stock_level = ?";
                $params[] = $data['min_stock_level'];
            }
            
            if (isset($data['max_stock_level'])) {
                $setParts[] = "max_stock_level = ?";
                $params[] = $data['max_stock_level'];
            }
            
            if (isset($data['reorder_level'])) {
                $setParts[] = "reorder_level = ?";
                $params[] = $data['reorder_level'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (isset($data['has_serial_numbers'])) {
                $setParts[] = "has_serial_numbers = ?";
                $params[] = $data['has_serial_numbers'];
            }
            
            if (isset($data['has_expiry_date'])) {
                $setParts[] = "has_expiry_date = ?";
                $params[] = $data['has_expiry_date'];
            }
            
            if (isset($data['notes'])) {
                $setParts[] = "notes = ?";
                $params[] = $data['notes'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $itemId;
            
            $query = "UPDATE items SET " . implode(', ', $setParts) . " WHERE item_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث صنف', "تم تحديث الصنف ID: {$itemId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update item error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الصنف (إلغاء تفعيل)
     */
    public function deleteItem($itemId) {
        try {
            $query = "UPDATE items SET is_active = 0, updated_at = NOW() WHERE item_id = ?";
            $result = $this->db->update($query, [$itemId]);
            
            if ($result) {
                logActivity('حذف صنف', "تم حذف الصنف ID: {$itemId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete item error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إضافة وحدة للصنف
     */
    public function addItemUnit($itemId, $unitId, $conversionFactor, $purchasePrice = 0, $salePrice = 0, $wholesalePrice = 0, $isDefault = false) {
        try {
            $query = "INSERT INTO item_units (
                        item_id, unit_id, conversion_factor, purchase_price, sale_price,
                        wholesale_price, is_default, is_purchasable, is_saleable, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, 1, ?, NOW())";
            
            $params = [
                $itemId, $unitId, $conversionFactor, $purchasePrice, $salePrice,
                $wholesalePrice, $isDefault ? 1 : 0, $_SESSION['user_id'] ?? null
            ];
            
            return $this->db->insert($query, $params);
            
        } catch (Exception $e) {
            error_log("Add item unit error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود الصنف
     */
    private function itemCodeExists($itemCode, $excludeItemId = null) {
        $query = "SELECT COUNT(*) as count FROM items WHERE item_code = ?";
        $params = [$itemCode];
        
        if ($excludeItemId) {
            $query .= " AND item_id != ?";
            $params[] = $excludeItemId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * البحث في الأصناف
     */
    public function searchItems($searchTerm, $categoryId = null, $activeOnly = true) {
        try {
            $query = "SELECT 
                        i.item_id, i.item_code, i.item_name, i.item_description,
                        i.barcode, c.category_name, u.unit_name as base_unit_name
                      FROM items i
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      LEFT JOIN units u ON i.base_unit_id = u.unit_id
                      WHERE (i.item_name LIKE ? OR i.item_code LIKE ? OR i.barcode LIKE ?)";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($categoryId) {
                $query .= " AND i.category_id = ?";
                $params[] = $categoryId;
            }
            
            if ($activeOnly) {
                $query .= " AND i.is_active = 1";
            }
            
            $query .= " ORDER BY i.item_name LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search items error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * عدد الأصناف
     */
    public function getItemsCount($activeOnly = true) {
        try {
            $query = "SELECT COUNT(*) as count FROM items";
            $params = [];
            
            if ($activeOnly) {
                $query .= " WHERE is_active = 1";
            }
            
            $result = $this->db->selectOne($query, $params);
            return $result['count'];
            
        } catch (Exception $e) {
            error_log("Get items count error: " . $e->getMessage());
            return 0;
        }
    }
}

?>

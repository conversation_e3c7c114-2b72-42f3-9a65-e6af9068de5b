<?php
/**
 * صفحة استعادة النسخ الاحتياطية
 * Restore Backup Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_backup');

// إنشاء مثيل نموذج النسخ الاحتياطي
$backupModel = new Backup();

// معالجة العمليات
$action = $_GET['action'] ?? 'upload';
$error = '';
$success = '';

// معالجة رفع ملف النسخة الاحتياطية
if ($action === 'upload' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] === UPLOAD_ERR_OK) {
            $uploadedFile = $_FILES['backup_file'];
            $fileName = $uploadedFile['name'];
            $tmpName = $uploadedFile['tmp_name'];
            $fileSize = $uploadedFile['size'];
            
            // التحقق من نوع الملف
            $allowedExtensions = ['sql', 'gz', 'zip'];
            $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            
            if (!in_array($fileExtension, $allowedExtensions)) {
                $error = 'نوع الملف غير مدعوم. الأنواع المدعومة: SQL, GZ, ZIP';
            } elseif ($fileSize > 100 * 1024 * 1024) { // 100 MB
                $error = 'حجم الملف كبير جداً. الحد الأقصى: 100 ميجابايت';
            } else {
                // نقل الملف إلى مجلد النسخ الاحتياطي
                $uploadDir = 'backups/restore/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                $newFileName = 'restore_' . date('Y-m-d_H-i-s') . '_' . $fileName;
                $uploadPath = $uploadDir . $newFileName;
                
                if (move_uploaded_file($tmpName, $uploadPath)) {
                    $_SESSION['restore_file'] = $uploadPath;
                    $_SESSION['restore_filename'] = $fileName;
                    $_SESSION['restore_size'] = $fileSize;
                    
                    logActivity('رفع ملف استعادة', "تم رفع ملف النسخة الاحتياطية: {$fileName}");
                    
                    redirect('restore.php?action=confirm');
                } else {
                    $error = 'فشل في رفع الملف';
                }
            }
        } else {
            $error = 'يرجى اختيار ملف النسخة الاحتياطية';
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// معالجة تأكيد الاستعادة
if ($action === 'restore' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $restoreFile = $_SESSION['restore_file'] ?? '';
        
        if (!empty($restoreFile) && file_exists($restoreFile)) {
            try {
                $result = restoreDatabase($restoreFile);
                
                if ($result['success']) {
                    // حذف الملف المؤقت
                    unlink($restoreFile);
                    unset($_SESSION['restore_file'], $_SESSION['restore_filename'], $_SESSION['restore_size']);
                    
                    logActivity('استعادة نسخة احتياطية', "تم استعادة النسخة الاحتياطية بنجاح");
                    
                    $success = 'تم استعادة النسخة الاحتياطية بنجاح';
                } else {
                    $error = $result['error'] ?? 'فشل في استعادة النسخة الاحتياطية';
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        } else {
            $error = 'ملف النسخة الاحتياطية غير موجود';
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// معالجة إلغاء الاستعادة
if ($action === 'cancel') {
    $restoreFile = $_SESSION['restore_file'] ?? '';
    if (!empty($restoreFile) && file_exists($restoreFile)) {
        unlink($restoreFile);
    }
    unset($_SESSION['restore_file'], $_SESSION['restore_filename'], $_SESSION['restore_size']);
    redirect('restore.php');
}

$pageTitle = 'استعادة النسخ الاحتياطية';

/**
 * دالة استعادة قاعدة البيانات
 */
function restoreDatabase($filePath) {
    try {
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        // فك ضغط الملف إذا كان مضغوطاً
        if ($fileExtension === 'gz') {
            $sqlContent = gzdecode(file_get_contents($filePath));
        } elseif ($fileExtension === 'sql') {
            $sqlContent = file_get_contents($filePath);
        } else {
            throw new Exception('نوع الملف غير مدعوم للاستعادة');
        }
        
        if (empty($sqlContent)) {
            throw new Exception('الملف فارغ أو تالف');
        }
        
        // الاتصال بقاعدة البيانات
        $host = DB_HOST;
        $username = DB_USER;
        $password = DB_PASS;
        $database = DB_NAME;
        
        $mysqli = new mysqli($host, $username, $password, $database);
        
        if ($mysqli->connect_error) {
            throw new Exception('فشل في الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
        }
        
        // تعطيل فحص المفاتيح الخارجية مؤقتاً
        $mysqli->query('SET FOREIGN_KEY_CHECKS = 0');
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sqlContent);
        $executedQueries = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                if (!$mysqli->query($query)) {
                    throw new Exception('خطأ في تنفيذ الاستعلام: ' . $mysqli->error);
                }
                $executedQueries++;
            }
        }
        
        // إعادة تفعيل فحص المفاتيح الخارجية
        $mysqli->query('SET FOREIGN_KEY_CHECKS = 1');
        
        $mysqli->close();
        
        return [
            'success' => true,
            'executed_queries' => $executedQueries
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        
        .upload-area.dragover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="backup.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للنسخ الاحتياطي</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-upload ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle ml-2"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'upload'): ?>
            <!-- رفع ملف النسخة الاحتياطية -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-upload ml-2"></i>
                        رفع ملف النسخة الاحتياطية
                    </h3>
                </div>
                
                <div class="p-6">
                    <form method="POST" action="restore.php?action=upload" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- منطقة رفع الملف -->
                        <div class="upload-area p-8 text-center">
                            <div class="mb-4">
                                <i class="fas fa-cloud-upload-alt text-gray-400 text-6xl"></i>
                            </div>
                            <div class="mb-4">
                                <label for="backup_file" class="cursor-pointer">
                                    <span class="text-lg font-medium text-gray-700">اختر ملف النسخة الاحتياطية</span>
                                    <span class="block text-sm text-gray-500 mt-1">أو اسحب الملف وأفلته هنا</span>
                                </label>
                                <input 
                                    type="file" 
                                    id="backup_file" 
                                    name="backup_file" 
                                    accept=".sql,.gz,.zip"
                                    class="hidden"
                                    required
                                >
                            </div>
                            <div class="text-sm text-gray-500">
                                الأنواع المدعومة: SQL, GZ, ZIP | الحد الأقصى: 100 ميجابايت
                            </div>
                        </div>
                        
                        <!-- معلومات الملف المختار -->
                        <div id="file-info" class="mt-4 hidden">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-file text-blue-500 ml-2"></i>
                                    <div>
                                        <div id="file-name" class="font-medium text-blue-900"></div>
                                        <div id="file-size" class="text-sm text-blue-600"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تحذير مهم -->
                        <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
                            <div class="flex">
                                <i class="fas fa-exclamation-triangle text-red-400 ml-2 mt-1"></i>
                                <div class="text-sm text-red-700">
                                    <strong>تحذير مهم:</strong>
                                    <ul class="list-disc list-inside mt-2 space-y-1">
                                        <li>ستؤدي عملية الاستعادة إلى استبدال جميع البيانات الحالية</li>
                                        <li>تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة</li>
                                        <li>لا يمكن التراجع عن هذه العملية بعد تنفيذها</li>
                                        <li>تأكد من أن الملف المرفوع صحيح وغير تالف</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="mt-6 flex justify-end space-x-4 space-x-reverse">
                            <a href="backup.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-upload ml-2"></i>
                                رفع الملف
                            </button>
                        </div>
                        
                    </form>
                </div>
            </div>
            
        <?php elseif ($action === 'confirm'): ?>
            <!-- تأكيد الاستعادة -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        تأكيد استعادة النسخة الاحتياطية
                    </h3>
                </div>
                
                <div class="p-6">
                    <!-- معلومات الملف -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h4 class="font-medium text-blue-900 mb-2">معلومات الملف:</h4>
                        <div class="space-y-1 text-sm text-blue-700">
                            <div><strong>اسم الملف:</strong> <?php echo htmlspecialchars($_SESSION['restore_filename'] ?? ''); ?></div>
                            <div><strong>حجم الملف:</strong> <?php echo formatFileSize($_SESSION['restore_size'] ?? 0); ?></div>
                            <div><strong>تاريخ الرفع:</strong> <?php echo date('Y-m-d H:i:s'); ?></div>
                        </div>
                    </div>
                    
                    <!-- تحذير نهائي -->
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-red-400 ml-2 mt-1"></i>
                            <div class="text-sm text-red-700">
                                <strong>تحذير نهائي:</strong>
                                <p class="mt-2">
                                    أنت على وشك استعادة النسخة الاحتياطية. هذا سيؤدي إلى:
                                </p>
                                <ul class="list-disc list-inside mt-2 space-y-1">
                                    <li>حذف جميع البيانات الحالية في قاعدة البيانات</li>
                                    <li>استبدالها بالبيانات من النسخة الاحتياطية</li>
                                    <li>فقدان أي تغييرات تمت بعد تاريخ النسخة الاحتياطية</li>
                                </ul>
                                <p class="mt-2 font-medium">
                                    هل أنت متأكد من أنك تريد المتابعة؟
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="flex justify-end space-x-4 space-x-reverse">
                        <a href="restore.php?action=cancel" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <form method="POST" action="restore.php?action=restore" class="inline">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" onclick="return confirm('هل أنت متأكد تماماً من استعادة النسخة الاحتياطية؟ لا يمكن التراجع عن هذه العملية!')" 
                                    class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-exclamation-triangle ml-2"></i>
                                نعم، استعد النسخة الاحتياطية
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
        <?php endif; ?>
        
    </div>

    <!-- JavaScript -->
    <script>
        // معالجة رفع الملف
        const fileInput = document.getElementById('backup_file');
        const uploadArea = document.querySelector('.upload-area');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        
        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });
        
        // تغيير الملف
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const file = this.files[0];
                showFileInfo(file);
            }
        });
        
        // السحب والإفلات
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0]);
            }
        });
        
        // عرض معلومات الملف
        function showFileInfo(file) {
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('hidden');
        }
        
        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            
            const units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const base = Math.log(bytes) / Math.log(1024);
            const index = Math.floor(base);
            
            return Math.round(Math.pow(1024, base - index) * 100) / 100 + ' ' + units[index];
        }
    </script>

</body>
</html>

<?php
/**
 * إعداد جدول الأدوار
 * Setup Roles Table
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();

// السماح للمدير فقط بإعداد الأدوار
if (($_SESSION['role'] ?? '') !== 'admin') {
    setAlert('ليس لديك صلاحية لإعداد الأدوار', 'error');
    redirect('dashboard.php');
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_roles'])) {
    try {
        $db = Database::getInstance();

        // إنشاء جدول الأدوار
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS roles (
                role_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الدور الفريد',
                role_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'اسم الدور',
                role_description TEXT COMMENT 'وصف الدور',
                permissions JSON COMMENT 'صلاحيات الدور',
                is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الدور نشط',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
                created_by INT COMMENT 'معرف المستخدم الذي أنشأ الدور',

                INDEX idx_role_name (role_name),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأدوار والصلاحيات'
        ";

        $db->execute($createTableQuery);

        // إدراج الأدوار الأساسية
        $roles = [
            [
                'role_name' => 'مدير النظام',
                'role_description' => 'صلاحيات كاملة على جميع وحدات النظام',
                'permissions' => json_encode([
                    "users" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "roles" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "accounts" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "inventory" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "reports" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "settings" => ["create" => true, "read" => true, "update" => true, "delete" => true]
                ])
            ],
            [
                'role_name' => 'محاسب',
                'role_description' => 'صلاحيات المحاسبة والتقارير المالية',
                'permissions' => json_encode([
                    "accounts" => ["create" => true, "read" => true, "update" => true, "delete" => false],
                    "reports" => ["create" => false, "read" => true, "update" => false, "delete" => false],
                    "inventory" => ["create" => false, "read" => true, "update" => false, "delete" => false]
                ])
            ],
            [
                'role_name' => 'أمين المخزن',
                'role_description' => 'صلاحيات إدارة المخزون والأصناف',
                'permissions' => json_encode([
                    "inventory" => ["create" => true, "read" => true, "update" => true, "delete" => true],
                    "reports" => ["create" => false, "read" => true, "update" => false, "delete" => false]
                ])
            ],
            [
                'role_name' => 'مندوب مبيعات',
                'role_description' => 'صلاحيات المبيعات والعملاء',
                'permissions' => json_encode([
                    "sales" => ["create" => true, "read" => true, "update" => true, "delete" => false],
                    "customers" => ["create" => true, "read" => true, "update" => true, "delete" => false],
                    "inventory" => ["create" => false, "read" => true, "update" => false, "delete" => false]
                ])
            ],
            [
                'role_name' => 'مستخدم عادي',
                'role_description' => 'صلاحيات أساسية للاستعلام والعرض',
                'permissions' => json_encode([
                    "reports" => ["create" => false, "read" => true, "update" => false, "delete" => false],
                    "inventory" => ["create" => false, "read" => true, "update" => false, "delete" => false]
                ])
            ]
        ];

        $insertQuery = "INSERT IGNORE INTO roles (role_name, role_description, permissions, is_active, created_by) VALUES (?, ?, ?, TRUE, 1)";

        $successCount = 1; // للجدول
        foreach ($roles as $role) {
            try {
                $db->execute($insertQuery, [
                    $role['role_name'],
                    $role['role_description'],
                    $role['permissions']
                ]);
                $successCount++;
            } catch (Exception $e) {
                error_log("Insert role error: " . $e->getMessage() . " - Role: " . $role['role_name']);
            }
        }

        // إضافة حقل role_id للمستخدمين إذا لم يكن موجوداً
        try {
            $db->execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS role_id INT COMMENT 'معرف الدور'");
            $successCount++;
        } catch (Exception $e) {
            // تجاهل الخطأ إذا كان الحقل موجوداً
        }

        $message = "تم إعداد جدول الأدوار بنجاح! تم إنشاء الجدول و" . (count($roles)) . " أدوار أساسية.";
        $messageType = 'success';

    } catch (Exception $e) {
        $message = "خطأ في إعداد جدول الأدوار: " . $e->getMessage();
        $messageType = 'error';
        error_log("Setup roles error: " . $e->getMessage());
    }
}

// التحقق من وجود جدول الأدوار
$rolesTableExists = false;
try {
    $db = Database::getInstance();
    $result = $db->select("SHOW TABLES LIKE 'roles'");
    $rolesTableExists = $result && count($result) > 0;
} catch (Exception $e) {
    // تجاهل الخطأ
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جدول الأدوار - نظام المحاسبة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto">
            <!-- العنوان -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-cogs ml-2"></i>
                    إعداد جدول الأدوار
                </h1>
                <p class="text-gray-600">إنشاء جدول الأدوار والصلاحيات في قاعدة البيانات</p>
            </div>

            <!-- رسائل النظام -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : ($messageType === 'error' ? 'bg-red-100 border border-red-400 text-red-700' : 'bg-yellow-100 border border-yellow-400 text-yellow-700'); ?>">
                    <div class="flex items-center">
                        <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : ($messageType === 'error' ? 'fa-exclamation-circle' : 'fa-exclamation-triangle'); ?> ml-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- حالة جدول الأدوار -->
            <div class="bg-white shadow-lg rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-database ml-2"></i>
                        حالة جدول الأدوار
                    </h3>
                </div>
                <div class="p-6">
                    <?php if ($rolesTableExists): ?>
                        <div class="flex items-center text-green-600">
                            <i class="fas fa-check-circle text-2xl ml-3"></i>
                            <div>
                                <h4 class="font-medium">جدول الأدوار موجود</h4>
                                <p class="text-sm text-gray-600">جدول الأدوار موجود في قاعدة البيانات ويمكن استخدامه.</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="roles.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-arrow-left ml-2"></i>
                                الذهاب إلى إدارة الأدوار
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center text-red-600">
                            <i class="fas fa-exclamation-circle text-2xl ml-3"></i>
                            <div>
                                <h4 class="font-medium">جدول الأدوار غير موجود</h4>
                                <p class="text-sm text-gray-600">يجب إنشاء جدول الأدوار أولاً لتتمكن من استخدام نظام الأدوار والصلاحيات.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- نموذج الإعداد -->
            <?php if (!$rolesTableExists): ?>
                <div class="bg-white shadow-lg rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-play-circle ml-2"></i>
                            إنشاء جدول الأدوار
                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-6">
                            سيتم إنشاء جدول الأدوار مع الأدوار الأساسية التالية:
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-crown text-yellow-500 ml-2"></i>
                                    مدير النظام
                                </h4>
                                <p class="text-sm text-gray-600">صلاحيات كاملة على جميع وحدات النظام</p>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-calculator text-blue-500 ml-2"></i>
                                    محاسب
                                </h4>
                                <p class="text-sm text-gray-600">صلاحيات المحاسبة والتقارير المالية</p>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-warehouse text-green-500 ml-2"></i>
                                    أمين المخزن
                                </h4>
                                <p class="text-sm text-gray-600">صلاحيات إدارة المخزون والأصناف</p>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-handshake text-purple-500 ml-2"></i>
                                    مندوب مبيعات
                                </h4>
                                <p class="text-sm text-gray-600">صلاحيات المبيعات والعملاء</p>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4 md:col-span-2">
                                <h4 class="font-medium text-gray-900 mb-2">
                                    <i class="fas fa-user text-gray-500 ml-2"></i>
                                    مستخدم عادي
                                </h4>
                                <p class="text-sm text-gray-600">صلاحيات أساسية للاستعلام والعرض</p>
                            </div>
                        </div>
                        
                        <form method="POST" class="text-center">
                            <button type="submit" name="setup_roles" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                                <i class="fas fa-rocket ml-2"></i>
                                إنشاء جدول الأدوار والبيانات الأساسية
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>

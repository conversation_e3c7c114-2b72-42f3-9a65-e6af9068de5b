<?php
/**
 * نموذج الأدوار المتقدم
 * Advanced Roles Model
 */

class AdvancedRole {
    private $db;
    
    // أنواع الصلاحيات
    const PERMISSION_TYPES = [
        'module' => 'وحدة',
        'action' => 'عملية', 
        'field' => 'حقل',
        'data' => 'بيانات'
    ];
    
    // مستويات الوصول
    const ACCESS_LEVELS = [
        'none' => 'لا يوجد',
        'read' => 'قراءة',
        'write' => 'كتابة',
        'delete' => 'حذف',
        'admin' => 'إدارة كاملة'
    ];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * إنشاء دور جديد
     */
    public function createRole($data) {
        try {
            $query = "INSERT INTO advanced_roles (
                        role_name, role_description, role_type, 
                        is_system_role, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['role_name'],
                $data['role_description'] ?? '',
                $data['role_type'] ?? 'custom',
                $data['is_system_role'] ?? 0,
                $data['is_active'] ?? 1,
                $_SESSION['user_id']
            ];
            
            $roleId = $this->db->insert($query, $params);
            
            if ($roleId) {
                logActivity('إنشاء دور', "تم إنشاء دور جديد: {$data['role_name']}");
            }
            
            return $roleId;
            
        } catch (Exception $e) {
            error_log("Create role error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على دور بالمعرف
     */
    public function getRoleById($roleId) {
        try {
            $query = "SELECT ar.*, u.full_name as created_by_name
                     FROM advanced_roles ar
                     LEFT JOIN users u ON ar.created_by = u.user_id
                     WHERE ar.role_id = ?";
            
            return $this->db->selectOne($query, [$roleId]);
            
        } catch (Exception $e) {
            error_log("Get role by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * الحصول على جميع الأدوار
     */
    public function getAllRoles($includeInactive = false) {
        try {
            $query = "SELECT ar.*, u.full_name as created_by_name,
                            COUNT(uru.user_id) as users_count
                     FROM advanced_roles ar
                     LEFT JOIN users u ON ar.created_by = u.user_id
                     LEFT JOIN user_role_assignments ura ON ar.role_id = ura.role_id
                     LEFT JOIN users uru ON ura.user_id = uru.user_id AND uru.is_active = 1";
            
            if (!$includeInactive) {
                $query .= " WHERE ar.is_active = 1";
            }
            
            $query .= " GROUP BY ar.role_id
                       ORDER BY ar.is_system_role DESC, ar.role_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all roles error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث دور
     */
    public function updateRole($roleId, $data) {
        try {
            // التحقق من أن الدور ليس دور نظام
            $role = $this->getRoleById($roleId);
            if (!$role || $role['is_system_role']) {
                return false;
            }
            
            $query = "UPDATE advanced_roles SET 
                        role_name = ?, role_description = ?, 
                        is_active = ?, updated_by = ?, updated_at = NOW()
                      WHERE role_id = ? AND is_system_role = 0";
            
            $params = [
                $data['role_name'],
                $data['role_description'] ?? '',
                $data['is_active'] ?? 1,
                $_SESSION['user_id'],
                $roleId
            ];
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث دور', "تم تحديث الدور: {$data['role_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update role error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف دور
     */
    public function deleteRole($roleId) {
        try {
            // التحقق من أن الدور ليس دور نظام
            $role = $this->getRoleById($roleId);
            if (!$role || $role['is_system_role']) {
                return false;
            }
            
            // التحقق من عدم وجود مستخدمين مرتبطين بالدور
            $usersCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM user_role_assignments WHERE role_id = ?",
                [$roleId]
            );
            
            if ($usersCount['count'] > 0) {
                return false; // لا يمكن حذف دور مرتبط بمستخدمين
            }
            
            // حذف صلاحيات الدور
            $this->db->delete("DELETE FROM role_permissions WHERE role_id = ?", [$roleId]);
            
            // حذف الدور
            $result = $this->db->delete("DELETE FROM advanced_roles WHERE role_id = ? AND is_system_role = 0", [$roleId]);
            
            if ($result) {
                logActivity('حذف دور', "تم حذف الدور: {$role['role_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete role error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تعيين صلاحيات للدور
     */
    public function assignPermissions($roleId, $permissions) {
        try {
            // حذف الصلاحيات الحالية
            $this->db->delete("DELETE FROM role_permissions WHERE role_id = ?", [$roleId]);
            
            // إضافة الصلاحيات الجديدة
            foreach ($permissions as $permission) {
                $query = "INSERT INTO role_permissions (
                            role_id, permission_type, permission_key, 
                            access_level, field_restrictions, data_restrictions,
                            created_by, created_at
                          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $params = [
                    $roleId,
                    $permission['permission_type'],
                    $permission['permission_key'],
                    $permission['access_level'],
                    $permission['field_restrictions'] ?? null,
                    $permission['data_restrictions'] ?? null,
                    $_SESSION['user_id']
                ];
                
                $this->db->insert($query, $params);
            }
            
            $role = $this->getRoleById($roleId);
            logActivity('تعيين صلاحيات', "تم تعيين صلاحيات للدور: {$role['role_name']}");
            
            return true;
            
        } catch (Exception $e) {
            error_log("Assign permissions error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على صلاحيات الدور
     */
    public function getRolePermissions($roleId) {
        try {
            $query = "SELECT * FROM role_permissions WHERE role_id = ? ORDER BY permission_type, permission_key";
            return $this->db->select($query, [$roleId]);
            
        } catch (Exception $e) {
            error_log("Get role permissions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تعيين دور لمستخدم
     */
    public function assignRoleToUser($userId, $roleId, $restrictions = null) {
        try {
            // التحقق من عدم وجود تعيين سابق
            $existing = $this->db->selectOne(
                "SELECT * FROM user_role_assignments WHERE user_id = ? AND role_id = ?",
                [$userId, $roleId]
            );
            
            if ($existing) {
                // تحديث التعيين الموجود
                $query = "UPDATE user_role_assignments SET 
                            restrictions = ?, updated_by = ?, updated_at = NOW()
                          WHERE user_id = ? AND role_id = ?";
                
                $result = $this->db->update($query, [
                    $restrictions ? json_encode($restrictions) : null,
                    $_SESSION['user_id'],
                    $userId,
                    $roleId
                ]);
            } else {
                // إنشاء تعيين جديد
                $query = "INSERT INTO user_role_assignments (
                            user_id, role_id, restrictions, 
                            assigned_by, assigned_at
                          ) VALUES (?, ?, ?, ?, NOW())";
                
                $result = $this->db->insert($query, [
                    $userId,
                    $roleId,
                    $restrictions ? json_encode($restrictions) : null,
                    $_SESSION['user_id']
                ]);
            }
            
            if ($result) {
                $user = $this->db->selectOne("SELECT full_name FROM users WHERE user_id = ?", [$userId]);
                $role = $this->getRoleById($roleId);
                logActivity('تعيين دور', "تم تعيين الدور '{$role['role_name']}' للمستخدم: {$user['full_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Assign role to user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إلغاء تعيين دور من مستخدم
     */
    public function unassignRoleFromUser($userId, $roleId) {
        try {
            $result = $this->db->delete(
                "DELETE FROM user_role_assignments WHERE user_id = ? AND role_id = ?",
                [$userId, $roleId]
            );
            
            if ($result) {
                $user = $this->db->selectOne("SELECT full_name FROM users WHERE user_id = ?", [$userId]);
                $role = $this->getRoleById($roleId);
                logActivity('إلغاء تعيين دور', "تم إلغاء تعيين الدور '{$role['role_name']}' من المستخدم: {$user['full_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Unassign role from user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على أدوار المستخدم
     */
    public function getUserRoles($userId) {
        try {
            $query = "SELECT ar.*, ura.restrictions, ura.assigned_at
                     FROM user_role_assignments ura
                     JOIN advanced_roles ar ON ura.role_id = ar.role_id
                     WHERE ura.user_id = ? AND ar.is_active = 1
                     ORDER BY ar.role_name";
            
            return $this->db->select($query, [$userId]);
            
        } catch (Exception $e) {
            error_log("Get user roles error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * التحقق من صلاحية متقدمة
     */
    public function checkAdvancedPermission($userId, $permissionType, $permissionKey, $requiredLevel = 'read', $context = []) {
        try {
            // الحصول على أدوار المستخدم
            $userRoles = $this->getUserRoles($userId);
            
            foreach ($userRoles as $role) {
                // الحصول على صلاحيات الدور
                $permissions = $this->getRolePermissions($role['role_id']);
                
                foreach ($permissions as $permission) {
                    if ($permission['permission_type'] === $permissionType && 
                        $permission['permission_key'] === $permissionKey) {
                        
                        // التحقق من مستوى الوصول
                        if ($this->hasAccessLevel($permission['access_level'], $requiredLevel)) {
                            
                            // التحقق من قيود الحقول
                            if ($permission['field_restrictions'] && isset($context['field'])) {
                                $fieldRestrictions = json_decode($permission['field_restrictions'], true);
                                if (!in_array($context['field'], $fieldRestrictions)) {
                                    continue;
                                }
                            }
                            
                            // التحقق من قيود البيانات
                            if ($permission['data_restrictions'] && isset($context['data'])) {
                                $dataRestrictions = json_decode($permission['data_restrictions'], true);
                                if (!$this->checkDataRestrictions($dataRestrictions, $context['data'])) {
                                    continue;
                                }
                            }
                            
                            // التحقق من قيود الدور
                            if ($role['restrictions']) {
                                $roleRestrictions = json_decode($role['restrictions'], true);
                                if (!$this->checkRoleRestrictions($roleRestrictions, $context)) {
                                    continue;
                                }
                            }
                            
                            return true;
                        }
                    }
                }
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Check advanced permission error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من مستوى الوصول
     */
    private function hasAccessLevel($userLevel, $requiredLevel) {
        $levels = ['none', 'read', 'write', 'delete', 'admin'];
        $userIndex = array_search($userLevel, $levels);
        $requiredIndex = array_search($requiredLevel, $levels);
        
        return $userIndex !== false && $requiredIndex !== false && $userIndex >= $requiredIndex;
    }
    
    /**
     * التحقق من قيود البيانات
     */
    private function checkDataRestrictions($restrictions, $data) {
        foreach ($restrictions as $field => $allowedValues) {
            if (isset($data[$field])) {
                if (is_array($allowedValues)) {
                    if (!in_array($data[$field], $allowedValues)) {
                        return false;
                    }
                } else {
                    if ($data[$field] != $allowedValues) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * التحقق من قيود الدور
     */
    private function checkRoleRestrictions($restrictions, $context) {
        // يمكن تخصيص هذه الدالة حسب نوع القيود المطلوبة
        // مثال: قيود زمنية، قيود جغرافية، قيود على قيم معينة
        
        if (isset($restrictions['time_restrictions'])) {
            $currentTime = date('H:i');
            $allowedStart = $restrictions['time_restrictions']['start'] ?? '00:00';
            $allowedEnd = $restrictions['time_restrictions']['end'] ?? '23:59';
            
            if ($currentTime < $allowedStart || $currentTime > $allowedEnd) {
                return false;
            }
        }
        
        if (isset($restrictions['ip_restrictions'])) {
            $userIP = $_SERVER['REMOTE_ADDR'] ?? '';
            $allowedIPs = $restrictions['ip_restrictions'];
            
            if (!in_array($userIP, $allowedIPs)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * إنشاء الأدوار الافتراضية
     */
    public function createDefaultRoles() {
        try {
            $defaultRoles = [
                [
                    'role_name' => 'مدير النظام',
                    'role_description' => 'صلاحيات كاملة على جميع وحدات النظام',
                    'role_type' => 'system',
                    'is_system_role' => 1,
                    'permissions' => $this->getAdminPermissions()
                ],
                [
                    'role_name' => 'محاسب',
                    'role_description' => 'صلاحيات المحاسبة والتقارير المالية',
                    'role_type' => 'functional',
                    'is_system_role' => 1,
                    'permissions' => $this->getAccountantPermissions()
                ],
                [
                    'role_name' => 'أمين المخزن',
                    'role_description' => 'صلاحيات إدارة المخزون والأصناف',
                    'role_type' => 'functional',
                    'is_system_role' => 1,
                    'permissions' => $this->getWarehousePermissions()
                ],
                [
                    'role_name' => 'مندوب مبيعات',
                    'role_description' => 'صلاحيات المبيعات والعملاء',
                    'role_type' => 'functional',
                    'is_system_role' => 1,
                    'permissions' => $this->getSalesPermissions()
                ],
                [
                    'role_name' => 'مستخدم عادي',
                    'role_description' => 'صلاحيات أساسية للاستعلام والعرض',
                    'role_type' => 'basic',
                    'is_system_role' => 1,
                    'permissions' => $this->getBasicPermissions()
                ]
            ];
            
            foreach ($defaultRoles as $roleData) {
                // التحقق من عدم وجود الدور
                $existing = $this->db->selectOne(
                    "SELECT role_id FROM advanced_roles WHERE role_name = ?",
                    [$roleData['role_name']]
                );
                
                if (!$existing) {
                    $permissions = $roleData['permissions'];
                    unset($roleData['permissions']);
                    
                    $roleId = $this->createRole($roleData);
                    if ($roleId) {
                        $this->assignPermissions($roleId, $permissions);
                    }
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Create default roles error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * صلاحيات مدير النظام
     */
    private function getAdminPermissions() {
        return [
            ['permission_type' => 'module', 'permission_key' => '*', 'access_level' => 'admin'],
            ['permission_type' => 'action', 'permission_key' => '*', 'access_level' => 'admin'],
            ['permission_type' => 'field', 'permission_key' => '*', 'access_level' => 'admin'],
            ['permission_type' => 'data', 'permission_key' => '*', 'access_level' => 'admin']
        ];
    }
    
    /**
     * صلاحيات المحاسب
     */
    private function getAccountantPermissions() {
        return [
            ['permission_type' => 'module', 'permission_key' => 'accounting', 'access_level' => 'admin'],
            ['permission_type' => 'module', 'permission_key' => 'invoices', 'access_level' => 'write'],
            ['permission_type' => 'module', 'permission_key' => 'reports', 'access_level' => 'read'],
            ['permission_type' => 'module', 'permission_key' => 'customers', 'access_level' => 'read'],
            ['permission_type' => 'module', 'permission_key' => 'suppliers', 'access_level' => 'read'],
            ['permission_type' => 'action', 'permission_key' => 'financial_reports', 'access_level' => 'read'],
            ['permission_type' => 'field', 'permission_key' => 'invoice_amounts', 'access_level' => 'write']
        ];
    }
    
    /**
     * صلاحيات أمين المخزن
     */
    private function getWarehousePermissions() {
        return [
            ['permission_type' => 'module', 'permission_key' => 'inventory', 'access_level' => 'admin'],
            ['permission_type' => 'module', 'permission_key' => 'items', 'access_level' => 'write'],
            ['permission_type' => 'module', 'permission_key' => 'warehouses', 'access_level' => 'write'],
            ['permission_type' => 'action', 'permission_key' => 'stock_movements', 'access_level' => 'write'],
            ['permission_type' => 'field', 'permission_key' => 'stock_quantities', 'access_level' => 'write']
        ];
    }
    
    /**
     * صلاحيات مندوب المبيعات
     */
    private function getSalesPermissions() {
        return [
            ['permission_type' => 'module', 'permission_key' => 'customers', 'access_level' => 'write'],
            ['permission_type' => 'module', 'permission_key' => 'invoices', 'access_level' => 'write', 
             'data_restrictions' => json_encode(['invoice_type' => 'sales'])],
            ['permission_type' => 'action', 'permission_key' => 'create_sales_invoice', 'access_level' => 'write'],
            ['permission_type' => 'field', 'permission_key' => 'customer_info', 'access_level' => 'write']
        ];
    }
    
    /**
     * صلاحيات المستخدم العادي
     */
    private function getBasicPermissions() {
        return [
            ['permission_type' => 'module', 'permission_key' => 'dashboard', 'access_level' => 'read'],
            ['permission_type' => 'module', 'permission_key' => 'reports', 'access_level' => 'read'],
            ['permission_type' => 'action', 'permission_key' => 'view_basic_reports', 'access_level' => 'read']
        ];
    }
}

?>

<?php
/**
 * الدوال المساعدة العامة
 * Helper Functions
 */

/**
 * بدء الجلسة بأمان
 */
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_name(SESSION_NAME);
        session_start();
        
        // تجديد معرف الجلسة لمنع session fixation
        if (!isset($_SESSION['initiated'])) {
            session_regenerate_id(true);
            $_SESSION['initiated'] = true;
        }
        
        // فحص انتهاء صلاحية الجلسة
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
            session_unset();
            session_destroy();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
    }
    return true;
}

/**
 * فحص تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * فحص الصلاحيات
 */
function hasPermission($permission) {
    if (!isLoggedIn()) {
        return false;
    }

    // المدير له جميع الصلاحيات
    if ($_SESSION['role'] === 'admin') {
        return true;
    }

    // فحص الصلاحيات المحددة
    $userPermissions = $_SESSION['permissions'] ?? [];

    // إذا كانت الصلاحيات مصفوفة
    if (is_array($userPermissions)) {
        return in_array($permission, $userPermissions);
    }

    // إذا كانت الصلاحيات JSON object
    if (is_object($userPermissions)) {
        $parts = explode('_', $permission);
        if (count($parts) >= 2) {
            $module = $parts[0];
            $action = $parts[1];
            return isset($userPermissions->$module->$action) && $userPermissions->$module->$action;
        }
    }

    // صلاحيات افتراضية حسب الدور
    $defaultPermissions = [
        'admin' => ['users_manage', 'roles_manage', 'items_manage', 'warehouses_manage', 'inventory_manage', 'accounts_manage', 'transactions_manage', 'reports_view', 'system_admin', 'customers_manage', 'invoices_manage'],
        'accountant' => ['accounts_manage', 'transactions_manage', 'reports_view'],
        'warehouse_manager' => ['items_manage', 'warehouses_manage', 'inventory_manage'],
        'sales' => ['items_view', 'sales_manage', 'customers_manage'],
        'user' => ['items_view', 'reports_view']
    ];

    $userRole = $_SESSION['role'] ?? 'user';
    $rolePermissions = $defaultPermissions[$userRole] ?? [];

    return in_array($permission, $rolePermissions);
}

/**
 * التحقق من الصلاحيات المتقدمة
 */
function hasAdvancedPermission($permissionType, $permissionKey, $requiredLevel = 'read', $context = []) {
    if (!isLoggedIn()) {
        return false;
    }

    static $advancedRoleModel = null;

    if ($advancedRoleModel === null) {
        $advancedRoleModel = new AdvancedRole();
    }

    return $advancedRoleModel->checkAdvancedPermission(
        $_SESSION['user_id'],
        $permissionType,
        $permissionKey,
        $requiredLevel,
        $context
    );
}

/**
 * التحقق من صلاحية الوحدة
 */
function hasModulePermission($module, $level = 'read') {
    return hasAdvancedPermission('module', $module, $level);
}

/**
 * التحقق من صلاحية العملية
 */
function hasActionPermission($action, $level = 'write') {
    return hasAdvancedPermission('action', $action, $level);
}

/**
 * التحقق من صلاحية الحقل
 */
function hasFieldPermission($field, $level = 'read') {
    return hasAdvancedPermission('field', $field, $level);
}

/**
 * التحقق من صلاحية البيانات
 */
function hasDataPermission($dataKey, $data, $level = 'read') {
    return hasAdvancedPermission('data', $dataKey, $level, ['data' => $data]);
}

/**
 * فلترة الحقول حسب الصلاحيات
 */
function filterFieldsByPermissions($fields, $requiredLevel = 'read') {
    $allowedFields = [];

    foreach ($fields as $field) {
        if (hasFieldPermission($field, $requiredLevel)) {
            $allowedFields[] = $field;
        }
    }

    return $allowedFields;
}

/**
 * التحقق من صلاحية تحرير الحقل
 */
function canEditField($field, $context = []) {
    return hasFieldPermission($field, 'write') &&
           hasAdvancedPermission('field', $field, 'write', $context);
}

/**
 * التحقق من صلاحية حذف البيانات
 */
function canDeleteData($dataType, $data = []) {
    return hasDataPermission($dataType, $data, 'delete');
}

/**
 * الحصول على قيود المستخدم
 */
function getUserRestrictions($userId = null) {
    if (!$userId) {
        $userId = $_SESSION['user_id'] ?? null;
    }

    if (!$userId) {
        return [];
    }

    static $advancedRoleModel = null;

    if ($advancedRoleModel === null) {
        $advancedRoleModel = new AdvancedRole();
    }

    $userRoles = $advancedRoleModel->getUserRoles($userId);
    $restrictions = [];

    foreach ($userRoles as $role) {
        if ($role['restrictions']) {
            $roleRestrictions = json_decode($role['restrictions'], true);
            $restrictions = array_merge($restrictions, $roleRestrictions);
        }
    }

    return $restrictions;
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * تخزين كلمة المرور (نص عادي - غير آمن)
 * ⚠️ تحذير: هذا غير آمن ولا يُنصح به في البيئات الإنتاجية
 */
function hashPassword($password) {
    return $password; // تخزين كنص عادي
}

/**
 * التحقق من كلمة المرور (مقارنة نص عادي)
 * ⚠️ تحذير: هذا غير آمن ولا يُنصح به في البيئات الإنتاجية
 */
function verifyPassword($password, $storedPassword) {
    return $password === $storedPassword; // مقارنة مباشرة
}

/**
 * إنشاء رمز CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * التحقق من رمز CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * تنسيق المبلغ المالي
 */
function formatMoney($amount, $currency = null) {
    static $settingModel = null;

    if ($settingModel === null) {
        $settingModel = new Setting();
    }

    // الحصول على إعدادات العملة
    $currencySettings = $settingModel->getCurrencySettings();

    $symbol = $currency ?? $currencySettings['symbol'] ?? 'ريال';
    $position = $currencySettings['position'] ?? 'after';
    $decimalPlaces = (int)($currencySettings['decimal_places'] ?? 2);
    $thousandsSeparator = $currencySettings['thousands_separator'] ?? ',';
    $decimalSeparator = $currencySettings['decimal_separator'] ?? '.';

    // تنسيق المبلغ
    $formattedAmount = number_format($amount, $decimalPlaces, $decimalSeparator, $thousandsSeparator);

    // تطبيق موضع رمز العملة
    if ($position === 'before') {
        return $symbol . ' ' . $formattedAmount;
    } else {
        return $formattedAmount . ' ' . $symbol;
    }
}

/**
 * تنسيق الرقم
 */
function formatNumber($number, $decimals = 2) {
    return number_format($number, $decimals);
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 بايت';

    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
    $base = log($bytes, 1024);
    $index = floor($base);

    return round(pow(1024, $base - $index), 2) . ' ' . $units[$index];
}

/**
 * الحصول على إعداد من النظام
 */
function getSetting($key, $default = null) {
    static $settingModel = null;

    if ($settingModel === null) {
        $settingModel = new Setting();
    }

    return $settingModel->get($key, $default);
}

/**
 * تعيين إعداد في النظام
 */
function setSetting($key, $value, $description = null) {
    static $settingModel = null;

    if ($settingModel === null) {
        $settingModel = new Setting();
    }

    return $settingModel->set($key, $value, $description);
}

/**
 * الحصول على اسم الشركة
 */
function getCompanyName() {
    return getSetting('company_name', 'اسم الشركة');
}

/**
 * الحصول على معلومات الشركة
 */
function getCompanyInfo() {
    static $settingModel = null;

    if ($settingModel === null) {
        $settingModel = new Setting();
    }

    return $settingModel->getCompanySettings();
}

/**
 * إنشاء رابط طباعة للفاتورة
 */
function generateInvoicePrintUrl($invoiceId, $templateId = null, $autoPrint = false) {
    $url = "print_invoice.php?id={$invoiceId}";

    if ($templateId) {
        $url .= "&template_id={$templateId}";
    }

    if ($autoPrint) {
        $url .= "&auto_print=1";
    }

    return $url;
}

/**
 * إنشاء رابط طباعة للتقرير
 */
function generateReportPrintUrl($reportType, $reportTitle, $reportContent, $dateFrom = null, $dateTo = null, $templateId = null, $autoPrint = false) {
    $url = "print_report.php?type={$reportType}&title=" . urlencode($reportTitle) . "&content=" . base64_encode($reportContent);

    if ($dateFrom) {
        $url .= "&date_from={$dateFrom}";
    }

    if ($dateTo) {
        $url .= "&date_to={$dateTo}";
    }

    if ($templateId) {
        $url .= "&template_id={$templateId}";
    }

    if ($autoPrint) {
        $url .= "&auto_print=1";
    }

    return $url;
}

/**
 * الحصول على قالب الطباعة الافتراضي
 */
function getDefaultPrintTemplate($templateType) {
    static $templateModel = null;

    if ($templateModel === null) {
        $templateModel = new PrintTemplate();
    }

    return $templateModel->getDefaultTemplate($templateType);
}

/**
 * معالجة قالب الطباعة
 */
function processPrintTemplate($templateContent, $variables) {
    static $templateModel = null;

    if ($templateModel === null) {
        $templateModel = new PrintTemplate();
    }

    return $templateModel->processTemplate($templateContent, $variables);
}

/**
 * تحويل HTML إلى PDF (يتطلب مكتبة خارجية)
 */
function generatePDF($htmlContent, $filename = 'document.pdf', $options = []) {
    // هذه الدالة تحتاج لمكتبة PDF مثل TCPDF أو DomPDF
    // يمكن تطبيقها لاحقاً حسب الحاجة

    // مثال باستخدام DomPDF:
    /*
    require_once 'vendor/autoload.php';

    $dompdf = new \Dompdf\Dompdf();
    $dompdf->loadHtml($htmlContent);
    $dompdf->setPaper($options['paper_size'] ?? 'A4', $options['orientation'] ?? 'portrait');
    $dompdf->render();

    if ($options['download'] ?? false) {
        $dompdf->stream($filename);
    } else {
        return $dompdf->output();
    }
    */

    return false; // غير مطبق حالياً
}

/**
 * إنشاء رسالة تنبيه
 */
function setAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * عرض رسالة التنبيه
 */
function showAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        
        $alertClass = '';
        switch ($alert['type']) {
            case 'success':
                $alertClass = 'bg-green-100 border-green-500 text-green-700';
                break;
            case 'error':
                $alertClass = 'bg-red-100 border-red-500 text-red-700';
                break;
            case 'warning':
                $alertClass = 'bg-yellow-100 border-yellow-500 text-yellow-700';
                break;
            default:
                $alertClass = 'bg-blue-100 border-blue-500 text-blue-700';
        }
        
        echo "<div class='border-r-4 {$alertClass} p-4 mb-4 rounded' role='alert'>";
        echo "<p class='font-medium'>" . htmlspecialchars($alert['message']) . "</p>";
        echo "</div>";
    }
}

/**
 * تسجيل الأنشطة
 */
function logActivity($action, $details = '') {
    if (!isLoggedIn()) return;
    
    try {
        $db = Database::getInstance();
        $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
                  VALUES (?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $_SESSION['user_id'],
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $db->insert($query, $params);
    } catch (Exception $e) {
        // تسجيل الخطأ دون إيقاف التطبيق
        error_log("Activity log failed: " . $e->getMessage());
    }
}

/**
 * تحويل النص إلى slug
 */
function createSlug($text) {
    $text = trim($text);
    $text = mb_strtolower($text, 'UTF-8');
    $text = preg_replace('/[^\p{L}\p{N}\s-]/u', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

/**
 * فحص نوع الملف المرفوع
 */
function validateFileType($filename, $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * تحويل البايتات إلى حجم قابل للقراءة
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * إنشاء كود عشوائي
 */
function generateRandomCode($length = 8, $prefix = '') {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $prefix . $code;
}

/**
 * فحص قوة كلمة المرور
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    if (strlen($password) >= 8) $score++;
    else $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    
    if (preg_match('/[a-z]/', $password)) $score++;
    else $feedback[] = 'يجب أن تحتوي على حرف صغير';
    
    if (preg_match('/[A-Z]/', $password)) $score++;
    else $feedback[] = 'يجب أن تحتوي على حرف كبير';
    
    if (preg_match('/[0-9]/', $password)) $score++;
    else $feedback[] = 'يجب أن تحتوي على رقم';
    
    if (preg_match('/[^a-zA-Z0-9]/', $password)) $score++;
    else $feedback[] = 'يجب أن تحتوي على رمز خاص';
    
    return [
        'score' => $score,
        'strength' => $score < 3 ? 'ضعيفة' : ($score < 4 ? 'متوسطة' : 'قوية'),
        'feedback' => $feedback
    ];
}

?>

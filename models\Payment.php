<?php
/**
 * نموذج المدفوعات
 * Payment Model
 */

class Payment {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * إنشاء دفعة جديدة
     */
    public function createPayment($data) {
        try {
            $query = "INSERT INTO payments (
                        customer_id, invoice_id, payment_reference, 
                        payment_date, amount, payment_method, 
                        notes, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['customer_id'],
                $data['invoice_id'] ?? null,
                $data['payment_reference'],
                $data['payment_date'],
                $data['amount'],
                $data['payment_method'],
                $data['notes'] ?? null,
                $_SESSION['user_id']
            ];
            
            $paymentId = $this->db->insert($query, $params);
            
            if ($paymentId) {
                // تحديث حالة الدفع للفاتورة إذا كانت مرتبطة بفاتورة
                if (!empty($data['invoice_id'])) {
                    $this->updateInvoicePaymentStatus($data['invoice_id']);
                }
                
                // تسجيل النشاط
                logActivity('إنشاء دفعة', "تم إنشاء دفعة جديدة برقم مرجع: {$data['payment_reference']}");
                
                return $paymentId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Create payment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على دفعة بالمعرف
     */
    public function getPaymentById($paymentId) {
        try {
            $query = "SELECT p.*, c.customer_name, i.invoice_number, u.full_name as created_by_name
                     FROM payments p
                     LEFT JOIN customers c ON p.customer_id = c.customer_id
                     LEFT JOIN invoices i ON p.invoice_id = i.invoice_id
                     LEFT JOIN users u ON p.created_by = u.user_id
                     WHERE p.payment_id = ?";
            
            return $this->db->selectOne($query, [$paymentId]);
            
        } catch (Exception $e) {
            error_log("Get payment by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * الحصول على جميع المدفوعات
     */
    public function getAllPayments($filters = []) {
        try {
            $query = "SELECT p.*, c.customer_name, i.invoice_number, u.full_name as created_by_name
                     FROM payments p
                     LEFT JOIN customers c ON p.customer_id = c.customer_id
                     LEFT JOIN invoices i ON p.invoice_id = i.invoice_id
                     LEFT JOIN users u ON p.created_by = u.user_id
                     WHERE 1=1";
            
            $params = [];
            
            // فلترة حسب العميل
            if (!empty($filters['customer_id'])) {
                $query .= " AND p.customer_id = ?";
                $params[] = $filters['customer_id'];
            }
            
            // فلترة حسب الفاتورة
            if (!empty($filters['invoice_id'])) {
                $query .= " AND p.invoice_id = ?";
                $params[] = $filters['invoice_id'];
            }
            
            // فلترة حسب طريقة الدفع
            if (!empty($filters['payment_method'])) {
                $query .= " AND p.payment_method = ?";
                $params[] = $filters['payment_method'];
            }
            
            // فلترة حسب التاريخ من
            if (!empty($filters['date_from'])) {
                $query .= " AND p.payment_date >= ?";
                $params[] = $filters['date_from'];
            }
            
            // فلترة حسب التاريخ إلى
            if (!empty($filters['date_to'])) {
                $query .= " AND p.payment_date <= ?";
                $params[] = $filters['date_to'];
            }
            
            $query .= " ORDER BY p.payment_date DESC, p.payment_id DESC";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all payments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مدفوعات العميل
     */
    public function getCustomerPayments($customerId) {
        try {
            $query = "SELECT p.*, i.invoice_number, u.full_name as created_by_name
                     FROM payments p
                     LEFT JOIN invoices i ON p.invoice_id = i.invoice_id
                     LEFT JOIN users u ON p.created_by = u.user_id
                     WHERE p.customer_id = ?
                     ORDER BY p.payment_date DESC";
            
            return $this->db->select($query, [$customerId]);
            
        } catch (Exception $e) {
            error_log("Get customer payments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مدفوعات الفاتورة
     */
    public function getInvoicePayments($invoiceId) {
        try {
            $query = "SELECT p.*, c.customer_name, u.full_name as created_by_name
                     FROM payments p
                     LEFT JOIN customers c ON p.customer_id = c.customer_id
                     LEFT JOIN users u ON p.created_by = u.user_id
                     WHERE p.invoice_id = ?
                     ORDER BY p.payment_date DESC";
            
            return $this->db->select($query, [$invoiceId]);
            
        } catch (Exception $e) {
            error_log("Get invoice payments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث دفعة
     */
    public function updatePayment($paymentId, $data) {
        try {
            $query = "UPDATE payments SET 
                        customer_id = ?, payment_reference = ?, payment_date = ?, 
                        amount = ?, payment_method = ?, notes = ?, 
                        updated_by = ?, updated_at = NOW()
                      WHERE payment_id = ?";
            
            $params = [
                $data['customer_id'],
                $data['payment_reference'],
                $data['payment_date'],
                $data['amount'],
                $data['payment_method'],
                $data['notes'] ?? null,
                $_SESSION['user_id'],
                $paymentId
            ];
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                // تحديث حالة الدفع للفاتورة إذا كانت مرتبطة بفاتورة
                $payment = $this->getPaymentById($paymentId);
                if ($payment && !empty($payment['invoice_id'])) {
                    $this->updateInvoicePaymentStatus($payment['invoice_id']);
                }
                
                logActivity('تحديث دفعة', "تم تحديث الدفعة برقم مرجع: {$data['payment_reference']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update payment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف دفعة
     */
    public function deletePayment($paymentId) {
        try {
            // الحصول على بيانات الدفعة قبل الحذف
            $payment = $this->getPaymentById($paymentId);
            
            if (!$payment) {
                return false;
            }
            
            $query = "DELETE FROM payments WHERE payment_id = ?";
            $result = $this->db->delete($query, [$paymentId]);
            
            if ($result) {
                // تحديث حالة الدفع للفاتورة إذا كانت مرتبطة بفاتورة
                if (!empty($payment['invoice_id'])) {
                    $this->updateInvoicePaymentStatus($payment['invoice_id']);
                }
                
                logActivity('حذف دفعة', "تم حذف الدفعة برقم مرجع: {$payment['payment_reference']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete payment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديث حالة الدفع للفاتورة
     */
    private function updateInvoicePaymentStatus($invoiceId) {
        try {
            // الحصول على إجمالي مبلغ الفاتورة
            $invoice = $this->db->selectOne(
                "SELECT total_amount FROM invoices WHERE invoice_id = ?",
                [$invoiceId]
            );
            
            if (!$invoice) {
                return false;
            }
            
            // حساب إجمالي المدفوعات
            $totalPaid = $this->db->selectOne(
                "SELECT COALESCE(SUM(amount), 0) as total_paid FROM payments WHERE invoice_id = ?",
                [$invoiceId]
            );
            
            $totalPaidAmount = $totalPaid['total_paid'] ?? 0;
            $totalAmount = $invoice['total_amount'];
            
            // تحديد حالة الدفع
            $paymentStatus = 'unpaid';
            if ($totalPaidAmount >= $totalAmount) {
                $paymentStatus = 'paid';
            } elseif ($totalPaidAmount > 0) {
                $paymentStatus = 'partial';
            }
            
            // تحديث الفاتورة
            $this->db->update(
                "UPDATE invoices SET payment_status = ?, paid_amount = ? WHERE invoice_id = ?",
                [$paymentStatus, $totalPaidAmount, $invoiceId]
            );
            
            return true;
            
        } catch (Exception $e) {
            error_log("Update invoice payment status error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات المدفوعات
     */
    public function getPaymentStats($dateFrom = null, $dateTo = null) {
        try {
            $dateCondition = "";
            $params = [];
            
            if ($dateFrom && $dateTo) {
                $dateCondition = "WHERE payment_date BETWEEN ? AND ?";
                $params = [$dateFrom, $dateTo];
            } elseif ($dateFrom) {
                $dateCondition = "WHERE payment_date >= ?";
                $params = [$dateFrom];
            } elseif ($dateTo) {
                $dateCondition = "WHERE payment_date <= ?";
                $params = [$dateTo];
            }
            
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_payments,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(AVG(amount), 0) as average_amount,
                    COUNT(CASE WHEN payment_method = 'cash' THEN 1 END) as cash_payments,
                    COUNT(CASE WHEN payment_method = 'bank' THEN 1 END) as bank_payments,
                    COUNT(CASE WHEN payment_method = 'check' THEN 1 END) as check_payments,
                    COALESCE(SUM(CASE WHEN payment_method = 'cash' THEN amount ELSE 0 END), 0) as cash_amount,
                    COALESCE(SUM(CASE WHEN payment_method = 'bank' THEN amount ELSE 0 END), 0) as bank_amount,
                    COALESCE(SUM(CASE WHEN payment_method = 'check' THEN amount ELSE 0 END), 0) as check_amount
                FROM payments 
                {$dateCondition}
            ", $params);
            
            return $stats ?: [
                'total_payments' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'cash_payments' => 0,
                'bank_payments' => 0,
                'check_payments' => 0,
                'cash_amount' => 0,
                'bank_amount' => 0,
                'check_amount' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get payment stats error: " . $e->getMessage());
            return [
                'total_payments' => 0,
                'total_amount' => 0,
                'average_amount' => 0,
                'cash_payments' => 0,
                'bank_payments' => 0,
                'check_payments' => 0,
                'cash_amount' => 0,
                'bank_amount' => 0,
                'check_amount' => 0
            ];
        }
    }
    
    /**
     * توليد رقم مرجع دفعة جديد
     */
    public function generatePaymentReference() {
        $prefix = 'PAY';
        $year = date('Y');
        $month = date('m');
        
        // الحصول على آخر رقم مرجع
        $lastPayment = $this->db->selectOne(
            "SELECT payment_reference FROM payments 
             WHERE payment_reference LIKE ? 
             ORDER BY payment_id DESC LIMIT 1",
            ["{$prefix}-{$year}{$month}-%"]
        );
        
        if ($lastPayment) {
            // استخراج الرقم التسلسلي
            $parts = explode('-', $lastPayment['payment_reference']);
            $lastNumber = intval(end($parts));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return sprintf("%s-%s%s-%04d", $prefix, $year, $month, $newNumber);
    }
}

?>

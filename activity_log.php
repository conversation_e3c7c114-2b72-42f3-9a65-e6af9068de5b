<?php
/**
 * صفحة سجل الأنشطة
 * Activity Log Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('activity_log');

// إنشاء مثيلات النماذج
$activityLogModel = new ActivityLog();
$userModel = new User();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';

// معالجة التصدير
if ($action === 'export') {
    $filters = [
        'user_id' => !empty($_GET['user_id']) ? (int)$_GET['user_id'] : null,
        'action' => sanitizeInput($_GET['filter_action'] ?? ''),
        'table_name' => sanitizeInput($_GET['table_name'] ?? ''),
        'date_from' => sanitizeInput($_GET['date_from'] ?? ''),
        'date_to' => sanitizeInput($_GET['date_to'] ?? ''),
        'search' => sanitizeInput($_GET['search'] ?? '')
    ];
    
    $filepath = $activityLogModel->exportToCSV($filters);
    
    if ($filepath && file_exists($filepath)) {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . basename($filepath) . '"');
        header('Content-Length: ' . filesize($filepath));
        readfile($filepath);
        unlink($filepath); // حذف الملف بعد التحميل
        exit;
    } else {
        setAlert('فشل في تصدير البيانات', 'error');
        redirect('activity_log.php');
    }
}

// معالجة تنظيف السجلات القديمة
if ($action === 'clean' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $daysToKeep = (int)($_POST['days_to_keep'] ?? 90);
        
        if ($daysToKeep < 30) {
            setAlert('يجب الاحتفاظ بالسجلات لمدة 30 يوم على الأقل', 'error');
        } else {
            $deletedCount = $activityLogModel->cleanOldLogs($daysToKeep);
            
            if ($deletedCount !== false) {
                setAlert("تم حذف {$deletedCount} سجل قديم بنجاح", 'success');
            } else {
                setAlert('فشل في تنظيف السجلات القديمة', 'error');
            }
        }
    } else {
        setAlert('طلب غير صالح', 'error');
    }
    
    redirect('activity_log.php');
}

// إعداد الفلترة والبحث
$filters = [
    'user_id' => !empty($_GET['user_id']) ? (int)$_GET['user_id'] : null,
    'action' => sanitizeInput($_GET['filter_action'] ?? ''),
    'table_name' => sanitizeInput($_GET['table_name'] ?? ''),
    'date_from' => sanitizeInput($_GET['date_from'] ?? ''),
    'date_to' => sanitizeInput($_GET['date_to'] ?? ''),
    'search' => sanitizeInput($_GET['search'] ?? ''),
    'limit' => 50,
    'offset' => ((int)($_GET['page'] ?? 1) - 1) * 50
];

// الحصول على البيانات
$activities = $activityLogModel->getActivityLog($filters);
$totalActivities = $activityLogModel->getActivityLogCount($filters);
$activityStats = $activityLogModel->getActivityStats();

// الحصول على قوائم المساعدة
$users = $userModel->getAllUsers(true);
$availableActions = $activityLogModel->getAvailableActions();
$availableTables = $activityLogModel->getAvailableTables();

// حساب الصفحات
$currentPage = (int)($_GET['page'] ?? 1);
$totalPages = ceil($totalActivities / 50);

$pageTitle = 'سجل الأنشطة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض الأنشطة */
        .activity-item {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }
        
        .activity-login { border-right-color: #10b981; }
        .activity-create { border-right-color: #3b82f6; }
        .activity-update { border-right-color: #f59e0b; }
        .activity-delete { border-right-color: #ef4444; }
        .activity-other { border-right-color: #6b7280; }
        
        .activity-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            color: white;
        }
        
        .icon-login { background-color: #10b981; }
        .icon-create { background-color: #3b82f6; }
        .icon-update { background-color: #f59e0b; }
        .icon-delete { background-color: #ef4444; }
        .icon-other { background-color: #6b7280; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-history ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- إجمالي الأنشطة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الأنشطة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($activityStats['general']['total_activities']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أنشطة اليوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-day text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">أنشطة اليوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($activityStats['general']['today_activities']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أنشطة الأسبوع -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-week text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">أنشطة الأسبوع</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($activityStats['general']['week_activities']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المستخدمين النشطين -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">المستخدمين النشطين</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($activityStats['general']['active_users']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

        <!-- سجل الأنشطة -->
        <div class="bg-white shadow-lg rounded-lg">

            <!-- رأس السجل -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-history ml-2"></i>
                    سجل الأنشطة (<?php echo number_format($totalActivities); ?> نشاط)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="activity_log.php?action=export&<?php echo http_build_query(array_filter($filters)); ?>"
                       class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-download ml-2"></i>
                        تصدير CSV
                    </a>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <form method="GET" action="activity_log.php" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">

                    <!-- البحث -->
                    <div class="lg:col-span-2">
                        <input
                            type="text"
                            name="search"
                            value="<?php echo htmlspecialchars($filters['search']); ?>"
                            placeholder="البحث في الأنشطة..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- المستخدم -->
                    <div>
                        <select name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع المستخدمين</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['user_id']; ?>"
                                        <?php echo $filters['user_id'] == $user['user_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- من تاريخ -->
                    <div>
                        <input
                            type="date"
                            name="date_from"
                            value="<?php echo htmlspecialchars($filters['date_from']); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- إلى تاريخ -->
                    <div>
                        <input
                            type="date"
                            name="date_to"
                            value="<?php echo htmlspecialchars($filters['date_to']); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- أزرار البحث -->
                    <div class="lg:col-span-5 flex space-x-2 space-x-reverse">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <a href="activity_log.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            مسح الفلاتر
                        </a>
                    </div>

                </form>
            </div>

            <!-- قائمة الأنشطة -->
            <div class="p-6">
                <?php if (empty($activities)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-history text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أنشطة</h3>
                        <p class="text-gray-500">لم يتم العثور على أي أنشطة مطابقة للمعايير المحددة</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($activities as $activity): ?>
                            <?php
                            // تحديد نوع النشاط للتصميم
                            $activityType = 'other';
                            $iconClass = 'fas fa-info-circle';

                            if (strpos($activity['action'], 'دخول') !== false || strpos($activity['action'], 'خروج') !== false) {
                                $activityType = 'login';
                                $iconClass = 'fas fa-sign-in-alt';
                            } elseif (strpos($activity['action'], 'إنشاء') !== false || strpos($activity['action'], 'إضافة') !== false) {
                                $activityType = 'create';
                                $iconClass = 'fas fa-plus';
                            } elseif (strpos($activity['action'], 'تحديث') !== false || strpos($activity['action'], 'تعديل') !== false) {
                                $activityType = 'update';
                                $iconClass = 'fas fa-edit';
                            } elseif (strpos($activity['action'], 'حذف') !== false) {
                                $activityType = 'delete';
                                $iconClass = 'fas fa-trash';
                            }
                            ?>

                            <div class="activity-item activity-<?php echo $activityType; ?> bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-start space-x-4 space-x-reverse">

                                    <!-- أيقونة النشاط -->
                                    <div class="activity-icon icon-<?php echo $activityType; ?>">
                                        <i class="<?php echo $iconClass; ?>"></i>
                                    </div>

                                    <!-- تفاصيل النشاط -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <h4 class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($activity['action']); ?>
                                                </h4>
                                                <?php if (!empty($activity['table_name'])): ?>
                                                    <span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                                        <?php echo htmlspecialchars($activity['table_name']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php echo formatDate($activity['created_at']); ?>
                                                <span class="mr-2"><?php echo date('H:i', strtotime($activity['created_at'])); ?></span>
                                            </div>
                                        </div>

                                        <p class="mt-1 text-sm text-gray-600">
                                            <?php echo htmlspecialchars($activity['description']); ?>
                                        </p>

                                        <div class="mt-2 flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                                            <div class="flex items-center">
                                                <i class="fas fa-user ml-1"></i>
                                                <?php echo htmlspecialchars($activity['full_name'] ?? $activity['username'] ?? 'غير معروف'); ?>
                                            </div>
                                            <?php if (!empty($activity['ip_address'])): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-globe ml-1"></i>
                                                    <?php echo htmlspecialchars($activity['ip_address']); ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($activity['record_id'])): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-hashtag ml-1"></i>
                                                    ID: <?php echo htmlspecialchars($activity['record_id']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        <?php endforeach; ?>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    <?php if ($totalPages > 1): ?>
                        <div class="mt-8 flex justify-center">
                            <nav class="flex space-x-2 space-x-reverse">

                                <!-- الصفحة السابقة -->
                                <?php if ($currentPage > 1): ?>
                                    <a href="activity_log.php?page=<?php echo $currentPage - 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <i class="fas fa-chevron-right ml-1"></i>
                                        السابق
                                    </a>
                                <?php endif; ?>

                                <!-- أرقام الصفحات -->
                                <?php
                                $startPage = max(1, $currentPage - 2);
                                $endPage = min($totalPages, $currentPage + 2);
                                ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                    <?php if ($i == $currentPage): ?>
                                        <span class="px-3 py-2 text-sm bg-blue-500 text-white rounded-lg">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="activity_log.php?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <!-- الصفحة التالية -->
                                <?php if ($currentPage < $totalPages): ?>
                                    <a href="activity_log.php?page=<?php echo $currentPage + 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        التالي
                                        <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                <?php endif; ?>

                            </nav>
                        </div>
                    <?php endif; ?>

                <?php endif; ?>
            </div>

        </div>

    </div>

</body>
</html>

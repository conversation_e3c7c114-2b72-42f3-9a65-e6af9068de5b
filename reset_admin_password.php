<?php
/**
 * سكريبت إعادة تعيين كلمة مرور المدير
 * Reset Admin Password Script
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';
$passwordReset = false;

// التحقق من وجود مدير في النظام
$db = Database::getInstance();
$admin = $db->selectOne("SELECT * FROM users WHERE role = 'admin' LIMIT 1");

if (!$admin) {
    $message = 'لا يوجد مستخدم مدير في النظام';
    $messageType = 'error';
}

// معالجة إعادة تعيين كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password']) && $admin) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // التحقق من البيانات
    if (empty($newPassword)) {
        $message = 'كلمة المرور مطلوبة';
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = 'كلمة المرور وتأكيدها غير متطابقتين';
        $messageType = 'error';
    } elseif (strlen($newPassword) < 6) {
        $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        $messageType = 'error';
    } else {
        // تحديث كلمة المرور
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $query = "UPDATE users SET password = ?, updated_at = NOW() WHERE user_id = ?";
        $result = $db->update($query, [$hashedPassword, $admin['user_id']]);
        
        if ($result) {
            $message = 'تم إعادة تعيين كلمة المرور بنجاح';
            $messageType = 'success';
            $passwordReset = true;
            
            // تسجيل النشاط
            $activityQuery = "INSERT INTO activity_log (
                                user_id, action, description, ip_address, created_at
                              ) VALUES (?, 'إعادة تعيين كلمة مرور', ?, ?, NOW())";
            
            $db->insert($activityQuery, [
                $admin['user_id'],
                "تم إعادة تعيين كلمة مرور المدير: {$admin['username']}",
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        } else {
            $message = 'فشل في إعادة تعيين كلمة المرور';
            $messageType = 'error';
        }
    }
}

$pageTitle = 'إعادة تعيين كلمة مرور المدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center h-16">
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-key ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-md mx-auto py-12">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($passwordReset): ?>
            <!-- نجح إعادة تعيين كلمة المرور -->
            <div class="bg-white shadow-lg rounded-lg p-8 text-center">
                <i class="fas fa-check-circle text-green-500 text-6xl mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">تم إعادة تعيين كلمة المرور!</h2>
                <p class="text-gray-600 mb-6">يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة</p>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-6 text-right">
                    <h3 class="font-semibold text-gray-900 mb-2">بيانات تسجيل الدخول:</h3>
                    <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($admin['username']); ?></p>
                    <p><strong>كلمة المرور:</strong> كلمة المرور الجديدة التي أدخلتها</p>
                </div>
                
                <a href="login.php" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </a>
            </div>
            
        <?php elseif (!$admin): ?>
            <!-- لا يوجد مدير -->
            <div class="bg-white shadow-lg rounded-lg p-8 text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-6xl mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">لا يوجد مستخدم مدير</h2>
                <p class="text-gray-600 mb-6">يجب إنشاء مستخدم مدير أولاً</p>
                <a href="create_admin.php" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                    <i class="fas fa-user-plus ml-2"></i>
                    إنشاء مستخدم مدير
                </a>
            </div>
            
        <?php else: ?>
            <!-- نموذج إعادة تعيين كلمة المرور -->
            <div class="bg-white shadow-lg rounded-lg p-8">
                <div class="text-center mb-6">
                    <i class="fas fa-key text-blue-500 text-6xl mb-4"></i>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">إعادة تعيين كلمة المرور</h2>
                    <p class="text-gray-600">قم بإعادة تعيين كلمة مرور المدير</p>
                </div>
                
                <!-- معلومات المدير الحالي -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-gray-900 mb-2">بيانات المدير:</h3>
                    <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($admin['username']); ?></p>
                    <p><strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($admin['full_name']); ?></p>
                    <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($admin['email']); ?></p>
                </div>
                
                <form method="POST" action="">
                    
                    <!-- كلمة المرور الجديدة -->
                    <div class="mb-4">
                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور الجديدة <span class="text-red-500">*</span>
                        </label>
                        <input type="password" id="new_password" name="new_password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">يجب أن تكون 6 أحرف على الأقل</p>
                    </div>
                    
                    <!-- تأكيد كلمة المرور -->
                    <div class="mb-6">
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            تأكيد كلمة المرور <span class="text-red-500">*</span>
                        </label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- زر إعادة التعيين -->
                    <button type="submit" name="reset_password" value="1" 
                            class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors duration-200">
                        <i class="fas fa-key ml-2"></i>
                        إعادة تعيين كلمة المرور
                    </button>
                    
                </form>
                
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-yellow-400 ml-2"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">تنبيه مهم</h4>
                            <p class="text-sm text-yellow-700 mt-1">
                                سيتم تغيير كلمة المرور الحالية نهائياً. تأكد من حفظ كلمة المرور الجديدة في مكان آمن.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <a href="login.php" class="text-blue-600 hover:text-blue-800 text-sm">
                        <i class="fas fa-arrow-right ml-1"></i>
                        العودة لصفحة تسجيل الدخول
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
    </div>

</body>
</html>

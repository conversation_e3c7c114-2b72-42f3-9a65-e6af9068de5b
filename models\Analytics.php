<?php
/**
 * نموذج الإحصائيات والتحليلات
 * Analytics Model
 */

class Analytics {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على إحصائيات المبيعات
     */
    public function getSalesStats($period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period);
            
            $query = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(total_amount) as total_sales,
                        AVG(total_amount) as average_sale,
                        SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
                        SUM(CASE WHEN payment_status = 'pending' THEN total_amount ELSE 0 END) as pending_amount,
                        SUM(CASE WHEN payment_status = 'overdue' THEN total_amount ELSE 0 END) as overdue_amount
                      FROM invoices 
                      WHERE invoice_type = 'sales' AND status != 'cancelled' {$dateCondition}";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get sales stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المشتريات
     */
    public function getPurchaseStats($period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period);
            
            $query = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(total_amount) as total_purchases,
                        AVG(total_amount) as average_purchase,
                        SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as paid_amount,
                        SUM(CASE WHEN payment_status = 'pending' THEN total_amount ELSE 0 END) as pending_amount
                      FROM invoices 
                      WHERE invoice_type = 'purchase' AND status != 'cancelled' {$dateCondition}";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get purchase stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المخزون
     */
    public function getInventoryStats() {
        try {
            $query = "SELECT 
                        COUNT(*) as total_items,
                        SUM(CASE WHEN current_stock > 0 THEN 1 ELSE 0 END) as items_in_stock,
                        SUM(CASE WHEN current_stock <= minimum_stock THEN 1 ELSE 0 END) as low_stock_items,
                        SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as out_of_stock_items,
                        SUM(current_stock * cost_price) as total_inventory_value
                      FROM items";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get inventory stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات العملاء
     */
    public function getCustomerStats($period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period);
            
            $query = "SELECT 
                        COUNT(DISTINCT c.customer_id) as total_customers,
                        COUNT(DISTINCT CASE WHEN i.invoice_id IS NOT NULL THEN c.customer_id END) as active_customers,
                        SUM(c.current_balance) as total_receivables
                      FROM customers c
                      LEFT JOIN invoices i ON c.customer_id = i.customer_id AND i.invoice_type = 'sales' {$dateCondition}";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get customer stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الموردين
     */
    public function getSupplierStats($period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period);
            
            $query = "SELECT 
                        COUNT(DISTINCT s.supplier_id) as total_suppliers,
                        COUNT(DISTINCT CASE WHEN i.invoice_id IS NOT NULL THEN s.supplier_id END) as active_suppliers,
                        SUM(s.current_balance) as total_payables
                      FROM suppliers s
                      LEFT JOIN invoices i ON s.supplier_id = i.supplier_id AND i.invoice_type = 'purchase' {$dateCondition}";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get supplier stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على أفضل الأصناف مبيعاً
     */
    public function getTopSellingItems($limit = 10, $period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period, 'i');
            
            $query = "SELECT 
                        it.item_name,
                        it.item_code,
                        SUM(ii.quantity) as total_quantity,
                        SUM(ii.total_price) as total_sales,
                        COUNT(DISTINCT i.invoice_id) as invoice_count
                      FROM invoice_items ii
                      JOIN invoices i ON ii.invoice_id = i.invoice_id
                      JOIN items it ON ii.item_id = it.item_id
                      WHERE i.invoice_type = 'sales' AND i.status != 'cancelled' {$dateCondition}
                      GROUP BY it.item_id, it.item_name, it.item_code
                      ORDER BY total_sales DESC
                      LIMIT {$limit}";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get top selling items error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على أفضل العملاء
     */
    public function getTopCustomers($limit = 10, $period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period, 'i');
            
            $query = "SELECT 
                        c.customer_name,
                        c.customer_code,
                        SUM(i.total_amount) as total_purchases,
                        COUNT(i.invoice_id) as invoice_count,
                        AVG(i.total_amount) as average_purchase
                      FROM customers c
                      JOIN invoices i ON c.customer_id = i.customer_id
                      WHERE i.invoice_type = 'sales' AND i.status != 'cancelled' {$dateCondition}
                      GROUP BY c.customer_id, c.customer_name, c.customer_code
                      ORDER BY total_purchases DESC
                      LIMIT {$limit}";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get top customers error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على المبيعات اليومية
     */
    public function getDailySales($days = 30) {
        try {
            $query = "SELECT 
                        DATE(invoice_date) as sale_date,
                        COUNT(*) as invoice_count,
                        SUM(total_amount) as daily_sales
                      FROM invoices 
                      WHERE invoice_type = 'sales' 
                        AND status != 'cancelled'
                        AND invoice_date >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)
                      GROUP BY DATE(invoice_date)
                      ORDER BY sale_date DESC";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get daily sales error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على المبيعات الشهرية
     */
    public function getMonthlySales($months = 12) {
        try {
            $query = "SELECT 
                        YEAR(invoice_date) as sale_year,
                        MONTH(invoice_date) as sale_month,
                        MONTHNAME(invoice_date) as month_name,
                        COUNT(*) as invoice_count,
                        SUM(total_amount) as monthly_sales
                      FROM invoices 
                      WHERE invoice_type = 'sales' 
                        AND status != 'cancelled'
                        AND invoice_date >= DATE_SUB(CURDATE(), INTERVAL {$months} MONTH)
                      GROUP BY YEAR(invoice_date), MONTH(invoice_date)
                      ORDER BY sale_year DESC, sale_month DESC";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get monthly sales error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الأصناف منخفضة المخزون
     */
    public function getLowStockItems($limit = 20) {
        try {
            $query = "SELECT 
                        item_name,
                        item_code,
                        current_stock,
                        minimum_stock,
                        unit_name,
                        (minimum_stock - current_stock) as shortage_quantity
                      FROM items 
                      WHERE current_stock <= minimum_stock
                      ORDER BY (current_stock / NULLIF(minimum_stock, 0)) ASC
                      LIMIT {$limit}";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get low stock items error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الفواتير المستحقة
     */
    public function getOverdueInvoices($limit = 20) {
        try {
            $query = "SELECT 
                        i.invoice_number,
                        i.invoice_date,
                        i.due_date,
                        i.total_amount,
                        c.customer_name,
                        DATEDIFF(CURDATE(), i.due_date) as days_overdue
                      FROM invoices i
                      LEFT JOIN customers c ON i.customer_id = c.customer_id
                      WHERE i.invoice_type = 'sales' 
                        AND i.payment_status = 'pending'
                        AND i.due_date < CURDATE()
                        AND i.status != 'cancelled'
                      ORDER BY days_overdue DESC
                      LIMIT {$limit}";
            
            return $this->db->select($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get overdue invoices error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على ملخص الأرباح
     */
    public function getProfitSummary($period = 'month') {
        try {
            $dateCondition = $this->getDateCondition($period, 'i');
            
            $query = "SELECT 
                        SUM(CASE WHEN i.invoice_type = 'sales' THEN i.total_amount ELSE 0 END) as total_sales,
                        SUM(CASE WHEN i.invoice_type = 'purchase' THEN i.total_amount ELSE 0 END) as total_purchases,
                        SUM(CASE WHEN i.invoice_type = 'sales' THEN ii.quantity * it.cost_price ELSE 0 END) as cost_of_goods_sold,
                        (SUM(CASE WHEN i.invoice_type = 'sales' THEN i.total_amount ELSE 0 END) - 
                         SUM(CASE WHEN i.invoice_type = 'sales' THEN ii.quantity * it.cost_price ELSE 0 END)) as gross_profit
                      FROM invoices i
                      LEFT JOIN invoice_items ii ON i.invoice_id = ii.invoice_id
                      LEFT JOIN items it ON ii.item_id = it.item_id
                      WHERE i.status != 'cancelled' {$dateCondition}";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get profit summary error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على شرط التاريخ حسب الفترة
     */
    private function getDateCondition($period, $tableAlias = '') {
        $prefix = $tableAlias ? $tableAlias . '.' : '';
        
        switch ($period) {
            case 'today':
                return "AND DATE({$prefix}invoice_date) = CURDATE()";
            case 'week':
                return "AND {$prefix}invoice_date >= DATE_SUB(CURDATE(), INTERVAL 1 WEEK)";
            case 'month':
                return "AND {$prefix}invoice_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
            case 'quarter':
                return "AND {$prefix}invoice_date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)";
            case 'year':
                return "AND {$prefix}invoice_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)";
            default:
                return "AND {$prefix}invoice_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)";
        }
    }
    
    /**
     * الحصول على إحصائيات شاملة
     */
    public function getComprehensiveStats($period = 'month') {
        return [
            'sales' => $this->getSalesStats($period),
            'purchases' => $this->getPurchaseStats($period),
            'inventory' => $this->getInventoryStats(),
            'customers' => $this->getCustomerStats($period),
            'suppliers' => $this->getSupplierStats($period),
            'profit' => $this->getProfitSummary($period),
            'top_items' => $this->getTopSellingItems(5, $period),
            'top_customers' => $this->getTopCustomers(5, $period),
            'low_stock' => $this->getLowStockItems(10),
            'overdue' => $this->getOverdueInvoices(10),
            'daily_sales' => $this->getDailySales(7),
            'monthly_sales' => $this->getMonthlySales(6)
        ];
    }
}

?>

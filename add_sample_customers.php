<?php
/**
 * إضافة عملاء تجريبيين
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_customers'])) {
    try {
        $db = Database::getInstance();
        
        // إضافة عملاء تجريبيين
        $sampleCustomers = [
            [
                'customer_code' => 'CUST001',
                'customer_name' => 'شركة الرياض للتجارة',
                'customer_type' => 'customer',
                'phone' => '0112345678',
                'email' => '<EMAIL>',
                'address' => 'شارع الملك فهد، الرياض',
                'city' => 'الرياض',
                'credit_limit' => 50000.00,
                'payment_terms' => 30,
                'is_active' => 1
            ],
            [
                'customer_code' => 'CUST002',
                'customer_name' => 'مؤسسة جدة للمقاولات',
                'customer_type' => 'customer',
                'phone' => '0123456789',
                'email' => '<EMAIL>',
                'address' => 'شارع الأمير سلطان، جدة',
                'city' => 'جدة',
                'credit_limit' => 75000.00,
                'payment_terms' => 45,
                'is_active' => 1
            ],
            [
                'customer_code' => 'SUPP001',
                'customer_name' => 'شركة النخيل للمواد الغذائية',
                'customer_type' => 'supplier',
                'phone' => '0167890123',
                'email' => '<EMAIL>',
                'address' => 'المنطقة الصناعية، الرياض',
                'city' => 'الرياض',
                'credit_limit' => 0.00,
                'payment_terms' => 30,
                'is_active' => 1
            ],
            [
                'customer_code' => 'SUPP002',
                'customer_name' => 'مؤسسة البحر الأحمر للاستيراد',
                'customer_type' => 'supplier',
                'phone' => '0178901234',
                'email' => '<EMAIL>',
                'address' => 'ميناء جدة، جدة',
                'city' => 'جدة',
                'credit_limit' => 0.00,
                'payment_terms' => 45,
                'is_active' => 1
            ],
            [
                'customer_code' => 'BOTH001',
                'customer_name' => 'مجموعة الوطن التجارية',
                'customer_type' => 'both',
                'phone' => '0112223333',
                'email' => '<EMAIL>',
                'address' => 'برج الوطن، الرياض',
                'city' => 'الرياض',
                'credit_limit' => 200000.00,
                'payment_terms' => 30,
                'is_active' => 1
            ]
        ];
        
        $insertedCount = 0;
        
        foreach ($sampleCustomers as $customer) {
            try {
                // التحقق من عدم وجود العميل
                $existing = $db->selectOne("SELECT customer_id FROM customers WHERE customer_code = ?", [$customer['customer_code']]);
                
                if (!$existing) {
                    $query = "INSERT INTO customers (
                        customer_code, customer_name, customer_type, phone, email,
                        address, city, credit_limit, payment_terms, is_active, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $params = [
                        $customer['customer_code'],
                        $customer['customer_name'],
                        $customer['customer_type'],
                        $customer['phone'],
                        $customer['email'],
                        $customer['address'],
                        $customer['city'],
                        $customer['credit_limit'],
                        $customer['payment_terms'],
                        $customer['is_active'],
                        $_SESSION['user_id'] ?? 1
                    ];
                    
                    $db->insert($query, $params);
                    $insertedCount++;
                }
            } catch (Exception $e) {
                // تجاهل الأخطاء الفردية
            }
        }
        
        $message = "تم إضافة $insertedCount عميل/مورد جديد بنجاح!";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// عرض العملاء الحاليين
try {
    $db = Database::getInstance();
    $customers = $db->select("SELECT customer_code, customer_name, customer_type FROM customers ORDER BY customer_type, customer_name");
} catch (Exception $e) {
    $customers = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عملاء تجريبيين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-user-plus mr-2"></i>
                إضافة عملاء تجريبيين
            </h1>

            <?php if ($message): ?>
                <div class="mb-4 p-4 rounded <?php echo $messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- العملاء الحاليين -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">العملاء الحاليين (<?php echo count($customers); ?>):</h3>
                
                <?php if (!empty($customers)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الكود</th>
                                    <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">الاسم</th>
                                    <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">النوع</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($customers as $customer): ?>
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900"><?php echo htmlspecialchars($customer['customer_code']); ?></td>
                                        <td class="px-4 py-2 text-sm text-gray-900"><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                                        <td class="px-4 py-2 text-sm">
                                            <span class="px-2 py-1 text-xs rounded-full <?php 
                                                echo $customer['customer_type'] === 'customer' ? 'bg-green-100 text-green-800' : 
                                                    ($customer['customer_type'] === 'supplier' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'); 
                                            ?>">
                                                <?php 
                                                echo $customer['customer_type'] === 'customer' ? 'عميل' : 
                                                    ($customer['customer_type'] === 'supplier' ? 'مورد' : 'عميل ومورد'); 
                                                ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 text-center py-4">لا توجد عملاء حالياً</p>
                <?php endif; ?>
            </div>

            <!-- نموذج الإضافة -->
            <form method="POST" class="mb-6">
                <button 
                    type="submit" 
                    name="add_customers"
                    class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded transition duration-200"
                >
                    <i class="fas fa-plus mr-2"></i>
                    إضافة عملاء وموردين تجريبيين
                </button>
            </form>

            <!-- روابط التنقل -->
            <div class="text-center space-x-4">
                <a href="invoices.php?action=add&type=sales" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-file-invoice mr-2"></i>
                    فاتورة مبيعات
                </a>
                <a href="invoices.php?action=add&type=purchase" class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-file-invoice mr-2"></i>
                    فاتورة مشتريات
                </a>
                <a href="customers.php" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-users mr-2"></i>
                    إدارة العملاء
                </a>
            </div>
        </div>
    </div>
</body>
</html>

<?php
/**
 * نموذج قوالب الطباعة
 * Print Templates Model
 */

class PrintTemplate {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * إنشاء قالب طباعة جديد
     */
    public function createTemplate($data) {
        try {
            $query = "INSERT INTO print_templates (
                        template_name, template_type, template_content, 
                        template_css, paper_size, orientation, 
                        margins, header_height, footer_height,
                        is_default, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['template_name'],
                $data['template_type'],
                $data['template_content'],
                $data['template_css'] ?? '',
                $data['paper_size'] ?? 'A4',
                $data['orientation'] ?? 'portrait',
                $data['margins'] ?? '20mm',
                $data['header_height'] ?? '50mm',
                $data['footer_height'] ?? '30mm',
                $data['is_default'] ?? 0,
                $data['is_active'] ?? 1,
                $_SESSION['user_id']
            ];
            
            $templateId = $this->db->insert($query, $params);
            
            if ($templateId && !empty($data['is_default'])) {
                // إلغاء تعيين القوالب الأخرى كافتراضية لنفس النوع
                $this->db->update(
                    "UPDATE print_templates SET is_default = 0 WHERE template_type = ? AND template_id != ?",
                    [$data['template_type'], $templateId]
                );
            }
            
            if ($templateId) {
                logActivity('إنشاء قالب طباعة', "تم إنشاء قالب طباعة جديد: {$data['template_name']}");
            }
            
            return $templateId;
            
        } catch (Exception $e) {
            error_log("Create print template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قالب طباعة بالمعرف
     */
    public function getTemplateById($templateId) {
        try {
            $query = "SELECT pt.*, u.full_name as created_by_name
                     FROM print_templates pt
                     LEFT JOIN users u ON pt.created_by = u.user_id
                     WHERE pt.template_id = ?";
            
            return $this->db->selectOne($query, [$templateId]);
            
        } catch (Exception $e) {
            error_log("Get template by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * الحصول على القالب الافتراضي لنوع معين
     */
    public function getDefaultTemplate($templateType) {
        try {
            $query = "SELECT * FROM print_templates 
                     WHERE template_type = ? AND is_default = 1 AND is_active = 1
                     ORDER BY created_at DESC LIMIT 1";
            
            $template = $this->db->selectOne($query, [$templateType]);
            
            // إذا لم يوجد قالب افتراضي، احصل على أول قالب نشط
            if (!$template) {
                $template = $this->db->selectOne(
                    "SELECT * FROM print_templates 
                     WHERE template_type = ? AND is_active = 1
                     ORDER BY created_at DESC LIMIT 1",
                    [$templateType]
                );
            }
            
            return $template;
            
        } catch (Exception $e) {
            error_log("Get default template error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * الحصول على جميع القوالب
     */
    public function getAllTemplates($templateType = null) {
        try {
            $query = "SELECT pt.*, u.full_name as created_by_name
                     FROM print_templates pt
                     LEFT JOIN users u ON pt.created_by = u.user_id";
            
            $params = [];
            
            if ($templateType) {
                $query .= " WHERE pt.template_type = ?";
                $params[] = $templateType;
            }
            
            $query .= " ORDER BY pt.template_type, pt.is_default DESC, pt.template_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all templates error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث قالب طباعة
     */
    public function updateTemplate($templateId, $data) {
        try {
            $query = "UPDATE print_templates SET 
                        template_name = ?, template_content = ?, template_css = ?,
                        paper_size = ?, orientation = ?, margins = ?,
                        header_height = ?, footer_height = ?, is_default = ?,
                        is_active = ?, updated_by = ?, updated_at = NOW()
                      WHERE template_id = ?";
            
            $params = [
                $data['template_name'],
                $data['template_content'],
                $data['template_css'] ?? '',
                $data['paper_size'] ?? 'A4',
                $data['orientation'] ?? 'portrait',
                $data['margins'] ?? '20mm',
                $data['header_height'] ?? '50mm',
                $data['footer_height'] ?? '30mm',
                $data['is_default'] ?? 0,
                $data['is_active'] ?? 1,
                $_SESSION['user_id'],
                $templateId
            ];
            
            $result = $this->db->update($query, $params);
            
            if ($result && !empty($data['is_default'])) {
                // الحصول على نوع القالب
                $template = $this->getTemplateById($templateId);
                if ($template) {
                    // إلغاء تعيين القوالب الأخرى كافتراضية لنفس النوع
                    $this->db->update(
                        "UPDATE print_templates SET is_default = 0 WHERE template_type = ? AND template_id != ?",
                        [$template['template_type'], $templateId]
                    );
                }
            }
            
            if ($result) {
                logActivity('تحديث قالب طباعة', "تم تحديث قالب الطباعة: {$data['template_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف قالب طباعة
     */
    public function deleteTemplate($templateId) {
        try {
            // الحصول على بيانات القالب قبل الحذف
            $template = $this->getTemplateById($templateId);
            
            if (!$template) {
                return false;
            }
            
            $query = "DELETE FROM print_templates WHERE template_id = ?";
            $result = $this->db->delete($query, [$templateId]);
            
            if ($result) {
                logActivity('حذف قالب طباعة', "تم حذف قالب الطباعة: {$template['template_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تعيين قالب كافتراضي
     */
    public function setAsDefault($templateId) {
        try {
            $template = $this->getTemplateById($templateId);
            
            if (!$template) {
                return false;
            }
            
            // إلغاء تعيين القوالب الأخرى كافتراضية لنفس النوع
            $this->db->update(
                "UPDATE print_templates SET is_default = 0 WHERE template_type = ?",
                [$template['template_type']]
            );
            
            // تعيين القالب الحالي كافتراضي
            $result = $this->db->update(
                "UPDATE print_templates SET is_default = 1 WHERE template_id = ?",
                [$templateId]
            );
            
            if ($result) {
                logActivity('تعيين قالب افتراضي', "تم تعيين قالب الطباعة كافتراضي: {$template['template_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Set as default template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * معالجة متغيرات القالب
     */
    public function processTemplate($templateContent, $variables) {
        try {
            $processedContent = $templateContent;
            
            foreach ($variables as $key => $value) {
                // معالجة المتغيرات البسيطة
                $processedContent = str_replace('{{' . $key . '}}', $value, $processedContent);
                
                // معالجة المتغيرات المشروطة
                $processedContent = preg_replace_callback(
                    '/\{\{if\s+' . preg_quote($key) . '\}\}(.*?)\{\{\/if\}\}/s',
                    function($matches) use ($value) {
                        return !empty($value) ? $matches[1] : '';
                    },
                    $processedContent
                );
            }
            
            // معالجة الحلقات (للجداول)
            $processedContent = preg_replace_callback(
                '/\{\{foreach\s+(\w+)\}\}(.*?)\{\{\/foreach\}\}/s',
                function($matches) use ($variables) {
                    $arrayKey = $matches[1];
                    $template = $matches[2];
                    $result = '';
                    
                    if (isset($variables[$arrayKey]) && is_array($variables[$arrayKey])) {
                        foreach ($variables[$arrayKey] as $item) {
                            $itemContent = $template;
                            foreach ($item as $itemKey => $itemValue) {
                                $itemContent = str_replace('{{' . $itemKey . '}}', $itemValue, $itemContent);
                            }
                            $result .= $itemContent;
                        }
                    }
                    
                    return $result;
                },
                $processedContent
            );
            
            // إزالة المتغيرات غير المعالجة
            $processedContent = preg_replace('/\{\{[^}]+\}\}/', '', $processedContent);
            
            return $processedContent;
            
        } catch (Exception $e) {
            error_log("Process template error: " . $e->getMessage());
            return $templateContent;
        }
    }
    
    /**
     * إنشاء قوالب افتراضية
     */
    public function createDefaultTemplates() {
        try {
            $defaultTemplates = [
                [
                    'template_name' => 'قالب فاتورة افتراضي',
                    'template_type' => 'invoice',
                    'template_content' => $this->getDefaultInvoiceTemplate(),
                    'template_css' => $this->getDefaultInvoiceCSS(),
                    'is_default' => 1
                ],
                [
                    'template_name' => 'قالب تقرير افتراضي',
                    'template_type' => 'report',
                    'template_content' => $this->getDefaultReportTemplate(),
                    'template_css' => $this->getDefaultReportCSS(),
                    'is_default' => 1
                ]
            ];
            
            foreach ($defaultTemplates as $template) {
                // التحقق من عدم وجود قالب افتراضي لهذا النوع
                $existing = $this->getDefaultTemplate($template['template_type']);
                if (!$existing) {
                    $this->createTemplate($template);
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Create default templates error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قالب الفاتورة الافتراضي
     */
    private function getDefaultInvoiceTemplate() {
        return '
        <div class="invoice-header">
            <div class="company-info">
                <h1>{{company_name}}</h1>
                <p>{{company_address}}</p>
                <p>هاتف: {{company_phone}} | بريد إلكتروني: {{company_email}}</p>
                {{if company_tax_number}}<p>الرقم الضريبي: {{company_tax_number}}</p>{{/if}}
            </div>
            <div class="invoice-info">
                <h2>فاتورة {{invoice_type_label}}</h2>
                <p><strong>رقم الفاتورة:</strong> {{invoice_number}}</p>
                <p><strong>التاريخ:</strong> {{invoice_date}}</p>
                {{if due_date}}<p><strong>تاريخ الاستحقاق:</strong> {{due_date}}</p>{{/if}}
            </div>
        </div>
        
        <div class="customer-info">
            <h3>معلومات العميل</h3>
            <p><strong>{{customer_name}}</strong></p>
            {{if customer_address}}<p>{{customer_address}}</p>{{/if}}
            {{if customer_phone}}<p>هاتف: {{customer_phone}}</p>{{/if}}
            {{if customer_email}}<p>بريد إلكتروني: {{customer_email}}</p>{{/if}}
        </div>
        
        <table class="invoice-items">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {{foreach items}}
                <tr>
                    <td>{{item_name}}</td>
                    <td>{{quantity}}</td>
                    <td>{{unit_name}}</td>
                    <td>{{unit_price}}</td>
                    <td>{{total_price}}</td>
                </tr>
                {{/foreach}}
            </tbody>
        </table>
        
        <div class="invoice-totals">
            <div class="totals-row">
                <span>المجموع الفرعي:</span>
                <span>{{subtotal}}</span>
            </div>
            {{if tax_amount}}
            <div class="totals-row">
                <span>الضريبة ({{tax_rate}}%):</span>
                <span>{{tax_amount}}</span>
            </div>
            {{/if}}
            {{if discount_amount}}
            <div class="totals-row">
                <span>الخصم:</span>
                <span>{{discount_amount}}</span>
            </div>
            {{/if}}
            <div class="totals-row total">
                <span>الإجمالي النهائي:</span>
                <span>{{total_amount}}</span>
            </div>
        </div>
        
        {{if notes}}
        <div class="invoice-notes">
            <h4>ملاحظات:</h4>
            <p>{{notes}}</p>
        </div>
        {{/if}}
        
        <div class="invoice-footer">
            <p>{{invoice_terms}}</p>
            <p>{{invoice_footer}}</p>
        </div>
        ';
    }
    
    /**
     * الحصول على CSS الفاتورة الافتراضي
     */
    private function getDefaultInvoiceCSS() {
        return '
        body { font-family: "Tajawal", Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; }
        .invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .company-info h1 { margin: 0; color: #333; font-size: 24px; }
        .company-info p { margin: 5px 0; color: #666; }
        .invoice-info { text-align: left; }
        .invoice-info h2 { margin: 0; color: #333; font-size: 20px; }
        .customer-info { margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px; }
        .customer-info h3 { margin-top: 0; color: #333; }
        .invoice-items { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .invoice-items th, .invoice-items td { border: 1px solid #ddd; padding: 10px; text-align: center; }
        .invoice-items th { background: #f5f5f5; font-weight: bold; }
        .invoice-totals { margin: 20px 0; text-align: left; }
        .totals-row { display: flex; justify-content: space-between; margin: 5px 0; padding: 5px 0; }
        .totals-row.total { border-top: 2px solid #333; font-weight: bold; font-size: 18px; }
        .invoice-notes { margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 5px; }
        .invoice-footer { margin-top: 30px; text-align: center; color: #666; font-size: 12px; }
        @media print { body { margin: 0; padding: 10px; } }
        ';
    }
    
    /**
     * الحصول على قالب التقرير الافتراضي
     */
    private function getDefaultReportTemplate() {
        return '
        <div class="report-header">
            <div class="company-info">
                <h1>{{company_name}}</h1>
                <p>{{company_address}}</p>
            </div>
            <div class="report-info">
                <h2>{{report_title}}</h2>
                <p><strong>تاريخ التقرير:</strong> {{report_date}}</p>
                {{if date_from}}<p><strong>من:</strong> {{date_from}} <strong>إلى:</strong> {{date_to}}</p>{{/if}}
            </div>
        </div>
        
        <div class="report-content">
            {{report_content}}
        </div>
        
        <div class="report-footer">
            <p>تم إنشاء التقرير في: {{generated_at}}</p>
        </div>
        ';
    }
    
    /**
     * الحصول على CSS التقرير الافتراضي
     */
    private function getDefaultReportCSS() {
        return '
        body { font-family: "Tajawal", Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; }
        .report-header { display: flex; justify-content: space-between; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .company-info h1 { margin: 0; color: #333; font-size: 24px; }
        .report-info { text-align: left; }
        .report-info h2 { margin: 0; color: #333; font-size: 20px; }
        .report-content { margin: 20px 0; }
        .report-footer { margin-top: 30px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #ddd; padding-top: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background: #f5f5f5; font-weight: bold; }
        @media print { body { margin: 0; padding: 10px; } }
        ';
    }
}

?>';
    }
}

?>

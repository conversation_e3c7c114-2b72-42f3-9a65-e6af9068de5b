<?php
/**
 * نموذج المخزون وحركاته
 * Inventory Model
 */

class Inventory {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على أرصدة المخزون
     */
    public function getInventoryBalances($warehouseId = null, $itemId = null, $lowStockOnly = false) {
        try {
            $query = "SELECT 
                        ib.*, i.item_name, i.item_code, i.min_stock_level, i.max_stock_level,
                        u.unit_name, u.unit_symbol, w.warehouse_name, c.category_name
                      FROM inventory_balances ib
                      JOIN items i ON ib.item_id = i.item_id
                      JOIN units u ON ib.unit_id = u.unit_id
                      JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      WHERE i.is_active = 1 AND w.is_active = 1";
            
            $params = [];
            
            if ($warehouseId) {
                $query .= " AND ib.warehouse_id = ?";
                $params[] = $warehouseId;
            }
            
            if ($itemId) {
                $query .= " AND ib.item_id = ?";
                $params[] = $itemId;
            }
            
            if ($lowStockOnly) {
                $query .= " AND ib.quantity <= i.min_stock_level";
            }
            
            $query .= " ORDER BY i.item_name, w.warehouse_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get inventory balances error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على حركات المخزون
     */
    public function getInventoryMovements($filters = [], $limit = 50, $offset = 0) {
        try {
            $query = "SELECT
                        im.*, i.item_name, i.item_code, u.unit_name, u.unit_symbol,
                        w.warehouse_name, fw.warehouse_name as from_warehouse_name,
                        tw.warehouse_name as to_warehouse_name, us.full_name as created_by_name
                      FROM inventory_movements im
                      JOIN items i ON im.item_id = i.item_id
                      LEFT JOIN units u ON im.unit_id = u.unit_id
                      JOIN warehouses w ON im.warehouse_id = w.warehouse_id
                      LEFT JOIN warehouses fw ON im.from_warehouse_id = fw.warehouse_id
                      LEFT JOIN warehouses tw ON im.to_warehouse_id = tw.warehouse_id
                      LEFT JOIN users us ON im.created_by = us.user_id
                      WHERE 1=1";

            $params = [];

            // تطبيق الفلاتر
            if (!empty($filters['movement_type'])) {
                $query .= " AND im.movement_type = ?";
                $params[] = $filters['movement_type'];
            }

            if (!empty($filters['warehouse_id'])) {
                $query .= " AND im.warehouse_id = ?";
                $params[] = $filters['warehouse_id'];
            }

            if (!empty($filters['item_id'])) {
                $query .= " AND im.item_id = ?";
                $params[] = $filters['item_id'];
            }

            if (!empty($filters['date_from'])) {
                $query .= " AND im.movement_date >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $query .= " AND im.movement_date <= ?";
                $params[] = $filters['date_to'];
            }

            if (!empty($filters['is_posted'])) {
                $query .= " AND im.is_posted = ?";
                $params[] = $filters['is_posted'];
            }

            if (!empty($filters['search'])) {
                $query .= " AND (i.item_name LIKE ? OR i.item_code LIKE ? OR im.reference_number LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $query .= " ORDER BY im.movement_date DESC, im.created_at DESC";

            if ($limit > 0) {
                $query .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }

            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get inventory movements error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * حساب عدد حركات المخزون
     */
    public function countInventoryMovements($filters = []) {
        try {
            $query = "SELECT COUNT(*) as total
                      FROM inventory_movements im
                      JOIN items i ON im.item_id = i.item_id
                      WHERE 1=1";

            $params = [];

            // تطبيق نفس الفلاتر
            if (!empty($filters['movement_type'])) {
                $query .= " AND im.movement_type = ?";
                $params[] = $filters['movement_type'];
            }

            if (!empty($filters['warehouse_id'])) {
                $query .= " AND im.warehouse_id = ?";
                $params[] = $filters['warehouse_id'];
            }

            if (!empty($filters['item_id'])) {
                $query .= " AND im.item_id = ?";
                $params[] = $filters['item_id'];
            }

            if (!empty($filters['date_from'])) {
                $query .= " AND im.movement_date >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $query .= " AND im.movement_date <= ?";
                $params[] = $filters['date_to'];
            }

            if (!empty($filters['is_posted'])) {
                $query .= " AND im.is_posted = ?";
                $params[] = $filters['is_posted'];
            }

            if (!empty($filters['search'])) {
                $query .= " AND (i.item_name LIKE ? OR i.item_code LIKE ? OR im.reference_number LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $result = $this->db->selectOne($query, $params);
            return $result ? $result['total'] : 0;

        } catch (Exception $e) {
            error_log("Count inventory movements error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * حساب عدد حركات المخزون
     */
    public function countInventoryMovements($filters = []) {
        try {
            $query = "SELECT COUNT(*) as total
                      FROM inventory_movements im
                      JOIN items i ON im.item_id = i.item_id
                      LEFT JOIN units u ON im.unit_id = u.unit_id
                      JOIN warehouses w ON im.warehouse_id = w.warehouse_id
                      WHERE 1=1";

            $params = [];

            // تطبيق نفس الفلاتر
            if (!empty($filters['movement_type'])) {
                $query .= " AND im.movement_type = ?";
                $params[] = $filters['movement_type'];
            }

            if (!empty($filters['warehouse_id'])) {
                $query .= " AND im.warehouse_id = ?";
                $params[] = $filters['warehouse_id'];
            }

            if (!empty($filters['item_id'])) {
                $query .= " AND im.item_id = ?";
                $params[] = $filters['item_id'];
            }

            if (!empty($filters['date_from'])) {
                $query .= " AND im.movement_date >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $query .= " AND im.movement_date <= ?";
                $params[] = $filters['date_to'];
            }

            if (!empty($filters['is_posted'])) {
                $query .= " AND im.is_posted = ?";
                $params[] = $filters['is_posted'];
            }

            if (!empty($filters['search'])) {
                $query .= " AND (i.item_name LIKE ? OR i.item_code LIKE ? OR im.reference_number LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $result = $this->db->selectOne($query, $params);
            return $result ? $result['total'] : 0;

        } catch (Exception $e) {
            error_log("Count inventory movements error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * إضافة حركة مخزون
     */
    public function addInventoryMovement($data) {
        try {
            $this->db->beginTransaction();
            
            // إدراج الحركة
            $query = "INSERT INTO inventory_movements (
                        movement_number, movement_date, movement_type, item_id, warehouse_id,
                        unit_id, quantity, unit_cost, reference_type, reference_id,
                        reference_number, from_warehouse_id, to_warehouse_id, notes,
                        batch_number, expiry_date, serial_numbers, is_posted, posted_at, created_by
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['movement_number'] ?? $this->generateMovementNumber($data['movement_type']),
                $data['movement_date'] ?? date('Y-m-d'),
                $data['movement_type'],
                $data['item_id'],
                $data['warehouse_id'],
                $data['unit_id'],
                $data['quantity'],
                $data['unit_cost'] ?? 0,
                $data['reference_type'] ?? null,
                $data['reference_id'] ?? null,
                $data['reference_number'] ?? null,
                $data['from_warehouse_id'] ?? null,
                $data['to_warehouse_id'] ?? null,
                $data['notes'] ?? null,
                $data['batch_number'] ?? null,
                $data['expiry_date'] ?? null,
                !empty($data['serial_numbers']) ? json_encode($data['serial_numbers']) : null,
                $data['is_posted'] ?? 1,
                $data['is_posted'] ?? 1 ? date('Y-m-d H:i:s') : null,
                $_SESSION['user_id'] ?? null
            ];
            
            $movementId = $this->db->insert($query, $params);
            
            if (!$movementId) {
                throw new Exception('فشل في إضافة حركة المخزون');
            }
            
            // تحديث أرصدة المخزون إذا كانت الحركة مرحلة
            if ($data['is_posted'] ?? 1) {
                $this->updateInventoryBalance($data);
                
                // في حالة التحويل، تحديث المخزن المحول إليه أيضاً
                if ($data['movement_type'] === 'transfer' && !empty($data['to_warehouse_id'])) {
                    $transferData = $data;
                    $transferData['warehouse_id'] = $data['to_warehouse_id'];
                    $transferData['movement_type'] = 'in';
                    $this->updateInventoryBalance($transferData);
                }
            }
            
            $this->db->commit();
            
            logActivity('إضافة حركة مخزون', "تم إضافة حركة مخزون: {$data['movement_type']} - الكمية: {$data['quantity']}");
            
            return $movementId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Add inventory movement error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث رصيد المخزون
     */
    private function updateInventoryBalance($data) {
        try {
            // الحصول على الرصيد الحالي
            $currentBalance = $this->db->selectOne("
                SELECT * FROM inventory_balances 
                WHERE item_id = ? AND warehouse_id = ? AND unit_id = ?
            ", [$data['item_id'], $data['warehouse_id'], $data['unit_id']]);
            
            $newQuantity = 0;
            $newAverageCost = $data['unit_cost'] ?? 0;
            
            if ($currentBalance) {
                // حساب الكمية الجديدة
                if (in_array($data['movement_type'], ['in', 'opening_balance', 'adjustment'])) {
                    $newQuantity = $currentBalance['quantity'] + $data['quantity'];
                    
                    // حساب متوسط التكلفة المرجح
                    if ($newQuantity > 0 && $data['unit_cost'] > 0) {
                        $totalValue = ($currentBalance['quantity'] * $currentBalance['average_cost']) + 
                                     ($data['quantity'] * $data['unit_cost']);
                        $newAverageCost = $totalValue / $newQuantity;
                    } else {
                        $newAverageCost = $currentBalance['average_cost'];
                    }
                } else {
                    $newQuantity = $currentBalance['quantity'] - $data['quantity'];
                    $newAverageCost = $currentBalance['average_cost'];
                }
                
                // تحديث الرصيد الموجود
                $this->db->update("
                    UPDATE inventory_balances 
                    SET quantity = ?, average_cost = ?, last_cost = ?, 
                        last_movement_date = NOW(), last_updated = NOW()
                    WHERE item_id = ? AND warehouse_id = ? AND unit_id = ?
                ", [
                    $newQuantity, $newAverageCost, $data['unit_cost'] ?? $currentBalance['last_cost'],
                    $data['item_id'], $data['warehouse_id'], $data['unit_id']
                ]);
            } else {
                // إنشاء رصيد جديد
                if (in_array($data['movement_type'], ['in', 'opening_balance', 'adjustment'])) {
                    $newQuantity = $data['quantity'];
                    $newAverageCost = $data['unit_cost'] ?? 0;
                    
                    $this->db->insert("
                        INSERT INTO inventory_balances 
                        (item_id, warehouse_id, unit_id, quantity, reserved_quantity, 
                         average_cost, last_cost, last_movement_date, last_updated)
                        VALUES (?, ?, ?, ?, 0, ?, ?, NOW(), NOW())
                    ", [
                        $data['item_id'], $data['warehouse_id'], $data['unit_id'],
                        $newQuantity, $newAverageCost, $newAverageCost
                    ]);
                }
            }
            
        } catch (Exception $e) {
            error_log("Update inventory balance error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إنتاج رقم حركة تلقائي
     */
    private function generateMovementNumber($movementType) {
        $prefix = [
            'in' => 'IN',
            'out' => 'OUT',
            'transfer' => 'TR',
            'adjustment' => 'ADJ',
            'opening_balance' => 'OP'
        ];
        
        $typePrefix = $prefix[$movementType] ?? 'MOV';
        $date = date('Ymd');
        
        // الحصول على آخر رقم لهذا النوع واليوم
        $lastNumber = $this->db->selectOne("
            SELECT movement_number FROM inventory_movements 
            WHERE movement_number LIKE ? 
            ORDER BY movement_id DESC LIMIT 1
        ", ["{$typePrefix}-{$date}-%"]);
        
        $sequence = 1;
        if ($lastNumber) {
            $parts = explode('-', $lastNumber['movement_number']);
            if (count($parts) >= 3) {
                $sequence = intval($parts[2]) + 1;
            }
        }
        
        return sprintf("%s-%s-%04d", $typePrefix, $date, $sequence);
    }
    
    /**
     * البحث في المخزون
     */
    public function searchInventory($searchTerm, $warehouseId = null) {
        try {
            $query = "SELECT 
                        ib.*, i.item_name, i.item_code, u.unit_name, u.unit_symbol,
                        w.warehouse_name, c.category_name
                      FROM inventory_balances ib
                      JOIN items i ON ib.item_id = i.item_id
                      JOIN units u ON ib.unit_id = u.unit_id
                      JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      WHERE (i.item_name LIKE ? OR i.item_code LIKE ?)
                      AND i.is_active = 1 AND w.is_active = 1";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($warehouseId) {
                $query .= " AND ib.warehouse_id = ?";
                $params[] = $warehouseId;
            }
            
            $query .= " ORDER BY i.item_name LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search inventory error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المخزون العامة
     */
    public function getInventoryStats() {
        try {
            return $this->db->selectOne("
                SELECT 
                    COUNT(DISTINCT ib.item_id) as total_items,
                    COUNT(DISTINCT ib.warehouse_id) as total_warehouses,
                    COALESCE(SUM(ib.total_value), 0) as total_value,
                    COUNT(CASE WHEN ib.quantity <= i.min_stock_level THEN 1 END) as low_stock_items,
                    COUNT(CASE WHEN ib.quantity = 0 THEN 1 END) as out_of_stock_items
                FROM inventory_balances ib
                JOIN items i ON ib.item_id = i.item_id
                JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
                WHERE i.is_active = 1 AND w.is_active = 1
            ") ?: [
                'total_items' => 0,
                'total_warehouses' => 0,
                'total_value' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get inventory stats error: " . $e->getMessage());
            return [
                'total_items' => 0,
                'total_warehouses' => 0,
                'total_value' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0
            ];
        }
    }
    
    /**
     * أنواع حركات المخزون
     */
    public function getMovementTypes() {
        return [
            'in' => 'إدخال',
            'out' => 'إخراج',
            'transfer' => 'تحويل',
            'adjustment' => 'تسوية',
            'opening_balance' => 'رصيد افتتاحي'
        ];
    }
    
    /**
     * أنواع المراجع
     */
    public function getReferenceTypes() {
        return [
            'purchase' => 'مشتريات',
            'sale' => 'مبيعات',
            'transfer' => 'تحويل',
            'adjustment' => 'تسوية',
            'opening' => 'رصيد افتتاحي',
            'return' => 'مرتجع'
        ];
    }

    /**
     * الحصول على قائمة المستودعات النشطة
     */
    public function getWarehouses() {
        try {
            $query = "SELECT warehouse_id, warehouse_name, warehouse_code
                      FROM warehouses
                      WHERE is_active = 1
                      ORDER BY warehouse_name";

            return $this->db->select($query);

        } catch (Exception $e) {
            error_log("Get warehouses error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على قائمة الأصناف النشطة
     */
    public function getItems() {
        try {
            $query = "SELECT i.item_id, i.item_name, i.item_code, u.unit_name, u.unit_symbol
                      FROM items i
                      LEFT JOIN units u ON i.base_unit_id = u.unit_id
                      WHERE i.is_active = 1
                      ORDER BY i.item_name";

            return $this->db->select($query);

        } catch (Exception $e) {
            error_log("Get items error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على عدد حركات المخزون مع الفلاتر
     */
    public function getInventoryMovementsCount($filters = []) {
        try {
            $query = "SELECT COUNT(*) as total
                      FROM inventory_movements im
                      JOIN items i ON im.item_id = i.item_id
                      JOIN warehouses w ON im.warehouse_id = w.warehouse_id
                      WHERE 1=1";

            $params = [];

            // تطبيق الفلاتر
            if (!empty($filters['movement_type'])) {
                $query .= " AND im.movement_type = ?";
                $params[] = $filters['movement_type'];
            }

            if (!empty($filters['warehouse_id'])) {
                $query .= " AND im.warehouse_id = ?";
                $params[] = $filters['warehouse_id'];
            }

            if (!empty($filters['item_id'])) {
                $query .= " AND im.item_id = ?";
                $params[] = $filters['item_id'];
            }

            if (!empty($filters['date_from'])) {
                $query .= " AND im.movement_date >= ?";
                $params[] = $filters['date_from'];
            }

            if (!empty($filters['date_to'])) {
                $query .= " AND im.movement_date <= ?";
                $params[] = $filters['date_to'];
            }

            if ($filters['is_posted'] !== '') {
                $query .= " AND im.is_posted = ?";
                $params[] = intval($filters['is_posted']);
            }

            if (!empty($filters['search'])) {
                $query .= " AND (im.movement_number LIKE ? OR im.reference_number LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $result = $this->db->selectOne($query, $params);
            return $result['total'] ?? 0;

        } catch (Exception $e) {
            error_log("Get inventory movements count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على حركة مخزون بالمعرف
     */
    public function getInventoryMovementById($movementId) {
        try {
            $query = "SELECT
                        im.*, i.item_name, i.item_code, u.unit_name, u.unit_symbol,
                        w.warehouse_name, fw.warehouse_name as from_warehouse_name,
                        tw.warehouse_name as to_warehouse_name, us.full_name as created_by_name
                      FROM inventory_movements im
                      JOIN items i ON im.item_id = i.item_id
                      LEFT JOIN units u ON im.unit_id = u.unit_id
                      JOIN warehouses w ON im.warehouse_id = w.warehouse_id
                      LEFT JOIN warehouses fw ON im.from_warehouse_id = fw.warehouse_id
                      LEFT JOIN warehouses tw ON im.to_warehouse_id = tw.warehouse_id
                      LEFT JOIN users us ON im.created_by = us.user_id
                      WHERE im.movement_id = ?";

            return $this->db->selectOne($query, [$movementId]);

        } catch (Exception $e) {
            error_log("Get inventory movement by ID error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * ترحيل حركة المخزون
     */
    public function postInventoryMovement($movementId) {
        try {
            $this->db->beginTransaction();

            // التحقق من أن الحركة غير مرحلة
            $movement = $this->getInventoryMovementById($movementId);
            if (!$movement) {
                throw new Exception("حركة المخزون غير موجودة");
            }

            if ($movement['is_posted']) {
                throw new Exception("حركة المخزون مرحلة مسبقاً");
            }

            // تحديث حالة الحركة إلى مرحلة
            $query = "UPDATE inventory_movements
                      SET is_posted = TRUE, posted_at = NOW()
                      WHERE movement_id = ?";

            $result = $this->db->update($query, [$movementId]);

            if ($result) {
                // تحديث أرصدة المخزون
                $this->updateInventoryBalance($movement);

                $this->db->commit();
                return true;
            } else {
                $this->db->rollback();
                return false;
            }

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Post inventory movement error: " . $e->getMessage());
            throw new Exception("فشل في ترحيل حركة المخزون: " . $e->getMessage());
        }
    }

    /**
     * تحديث رصيد المخزون بناءً على الحركة
     */
    private function updateInventoryBalance($movement) {
        try {
            // التحقق من وجود رصيد للصنف في المخزن
            $query = "SELECT * FROM inventory_balances
                      WHERE item_id = ? AND warehouse_id = ?";

            $balance = $this->db->selectOne($query, [$movement['item_id'], $movement['warehouse_id']]);

            if ($balance) {
                // تحديث الرصيد الموجود
                $newQuantity = $balance['quantity'];

                if (in_array($movement['movement_type'], ['in', 'opening_balance'])) {
                    $newQuantity += $movement['quantity'];
                } elseif (in_array($movement['movement_type'], ['out'])) {
                    $newQuantity -= $movement['quantity'];
                }

                $updateQuery = "UPDATE inventory_balances
                               SET quantity = ?, last_updated = NOW()
                               WHERE item_id = ? AND warehouse_id = ?";

                $this->db->update($updateQuery, [$newQuantity, $movement['item_id'], $movement['warehouse_id']]);

            } else {
                // إنشاء رصيد جديد
                $quantity = 0;

                if (in_array($movement['movement_type'], ['in', 'opening_balance'])) {
                    $quantity = $movement['quantity'];
                } elseif (in_array($movement['movement_type'], ['out'])) {
                    $quantity = -$movement['quantity'];
                }

                $insertQuery = "INSERT INTO inventory_balances
                               (item_id, warehouse_id, unit_id, quantity, average_cost, last_cost, last_updated)
                               VALUES (?, ?, ?, ?, ?, ?, NOW())";

                $this->db->insert($insertQuery, [
                    $movement['item_id'],
                    $movement['warehouse_id'],
                    $movement['unit_id'],
                    $quantity,
                    $movement['unit_cost'],
                    $movement['unit_cost']
                ]);
            }

        } catch (Exception $e) {
            error_log("Update inventory balance error: " . $e->getMessage());
            throw new Exception("فشل في تحديث رصيد المخزون");
        }
    }
}

?>

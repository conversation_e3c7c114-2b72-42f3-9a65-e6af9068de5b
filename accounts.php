<?php
/**
 * صفحة شجرة الحسابات
 * Chart of Accounts Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('accounts_manage');

// إنشاء مثيلات النماذج
$accountModel = new Account();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$accountId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $accountId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($accountModel->deleteAccount($accountId)) {
                    setAlert('تم حذف الحساب بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف الحساب', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('accounts.php');
    }
}

// معالجة إضافة/تعديل الحساب
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'account_code' => sanitizeInput($_POST['account_code'] ?? ''),
            'account_name' => sanitizeInput($_POST['account_name'] ?? ''),
            'account_type' => sanitizeInput($_POST['account_type'] ?? ''),
            'parent_account_id' => !empty($_POST['parent_account_id']) ? (int)$_POST['parent_account_id'] : null,
            'is_main_account' => isset($_POST['is_main_account']) ? 1 : 0,
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // التحقق من البيانات المطلوبة
        if (empty($data['account_code']) || empty($data['account_name']) || empty($data['account_type'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                if ($action === 'add') {
                    $newAccountId = $accountModel->createAccount($data);
                    if ($newAccountId) {
                        setAlert('تم إضافة الحساب بنجاح', 'success');
                        redirect('accounts.php');
                    } else {
                        $error = 'فشل في إضافة الحساب';
                    }
                } else {
                    if ($accountModel->updateAccount($accountId, $data)) {
                        setAlert('تم تحديث الحساب بنجاح', 'success');
                        redirect('accounts.php');
                    } else {
                        $error = 'فشل في تحديث الحساب';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentAccount = null;
if ($action === 'edit' && $accountId) {
    $currentAccount = $accountModel->getAccountById($accountId);
    if (!$currentAccount) {
        setAlert('الحساب غير موجود', 'error');
        redirect('accounts.php');
    }
}

// الحصول على قائمة الحسابات للعرض
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$typeFilter = sanitizeInput($_GET['type'] ?? '');

if (!empty($searchTerm)) {
    $accounts = $accountModel->searchAccounts($searchTerm, $typeFilter);
} else {
    $accounts = $accountModel->getAllAccounts(true);
    
    // فلترة حسب النوع إذا تم تحديده
    if (!empty($typeFilter)) {
        $accounts = array_filter($accounts, function($account) use ($typeFilter) {
            return $account['account_type'] === $typeFilter;
        });
    }
}

// الحصول على الحسابات الرئيسية للاختيار
$mainAccounts = $accountModel->getMainAccounts();
$accountTypes = $accountModel->getAccountTypes();

$pageTitle = $action === 'add' ? 'إضافة حساب جديد' : 
            ($action === 'edit' ? 'تعديل الحساب' : 'شجرة الحسابات');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض الشجرة */
        .account-tree {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        .account-level-0 { padding-right: 0; font-weight: 700; }
        .account-level-1 { padding-right: 1.5rem; font-weight: 600; }
        .account-level-2 { padding-right: 3rem; font-weight: 500; }
        .account-level-3 { padding-right: 4.5rem; font-weight: 400; }
        .account-level-4 { padding-right: 6rem; font-weight: 300; }
        
        .account-type-assets { border-right: 4px solid #3b82f6; }
        .account-type-liabilities { border-right: 4px solid #ef4444; }
        .account-type-equity { border-right: 4px solid #10b981; }
        .account-type-revenue { border-right: 4px solid #f59e0b; }
        .account-type-expenses { border-right: 4px solid #8b5cf6; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-chart-line ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- شجرة الحسابات -->
            <div class="bg-white shadow-lg rounded-lg">
                
                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-sitemap ml-2"></i>
                        شجرة الحسابات (<?php echo count($accounts); ?> حساب)
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="transactions.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-exchange-alt ml-2"></i>
                            القيود المحاسبية
                        </a>
                        <a href="accounts.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة حساب جديد
                        </a>
                    </div>
                </div>
                
                <!-- شريط البحث والفلترة -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <form method="GET" action="accounts.php" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input 
                                type="text" 
                                name="search" 
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="البحث في الحسابات (الاسم، الكود)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <div class="min-w-48">
                            <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($accountTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>" 
                                            <?php echo $typeFilter === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm) || !empty($typeFilter)): ?>
                            <a href="accounts.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- جدول الحسابات -->
                <div class="overflow-x-auto">
                    <?php if (empty($accounts)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-chart-line text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حسابات</h3>
                            <p class="text-gray-500 mb-4">لم يتم العثور على أي حسابات مطابقة للبحث</p>
                            <a href="accounts.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول حساب
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200 account-tree">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود الحساب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الحساب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستوى</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرصيد</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($accounts as $account): ?>
                                    <tr class="hover:bg-gray-50 account-type-<?php echo $account['account_type']; ?>">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($account['account_code']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap account-level-<?php echo min($account['account_level'], 4); ?>">
                                            <div class="text-sm text-gray-900">
                                                <?php echo htmlspecialchars($account['account_name']); ?>
                                            </div>
                                            <?php if (!empty($account['parent_account_name'])): ?>
                                                <div class="text-xs text-gray-500">
                                                    تابع لـ: <?php echo htmlspecialchars($account['parent_account_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $typeColors = [
                                                'assets' => 'bg-blue-100 text-blue-800',
                                                'liabilities' => 'bg-red-100 text-red-800',
                                                'equity' => 'bg-green-100 text-green-800',
                                                'revenue' => 'bg-yellow-100 text-yellow-800',
                                                'expenses' => 'bg-purple-100 text-purple-800'
                                            ];
                                            ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeColors[$account['account_type']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                                <?php echo $accountTypes[$account['account_type']] ?? $account['account_type']; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            المستوى <?php echo $account['account_level']; ?>
                                            <?php if ($account['is_main_account']): ?>
                                                <span class="text-blue-600 font-medium">(رئيسي)</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if ($account['balance'] != 0): ?>
                                                <div class="font-medium <?php echo $account['balance'] > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                    <?php echo formatMoney(abs($account['balance'])); ?>
                                                    <span class="text-xs">
                                                        (<?php echo $account['balance'] > 0 ? 'مدين' : 'دائن'; ?>)
                                                    </span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($account['is_active']): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="transactions.php?account=<?php echo $account['account_id']; ?>"
                                                   class="text-purple-600 hover:text-purple-900 transition-colors duration-200"
                                                   title="عرض المعاملات">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <a href="accounts.php?action=edit&id=<?php echo $account['account_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="accounts.php?action=delete&id=<?php echo $account['account_id']; ?>"
                                                   onclick="return confirm('هل أنت متأكد من حذف الحساب \'<?php echo htmlspecialchars($account['account_name'], ENT_QUOTES); ?>\'؟')"
                                                   class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                   title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>

            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- نموذج إضافة/تعديل الحساب -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> ml-2"></i>
                        <?php echo $action === 'add' ? 'إضافة حساب جديد' : 'تعديل الحساب'; ?>
                    </h3>
                </div>

                <form method="POST" action="" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- كود الحساب -->
                        <div>
                            <label for="account_code" class="block text-sm font-medium text-gray-700 mb-2">
                                كود الحساب <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="account_code"
                                name="account_code"
                                value="<?php echo htmlspecialchars($currentAccount['account_code'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                <?php echo $action === 'edit' ? 'readonly' : ''; ?>
                                placeholder="مثال: 1001"
                            >
                            <p class="text-xs text-gray-500 mt-1">كود فريد للحساب (أرقام فقط)</p>
                        </div>

                        <!-- اسم الحساب -->
                        <div>
                            <label for="account_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الحساب <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="account_name"
                                name="account_name"
                                value="<?php echo htmlspecialchars($currentAccount['account_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                placeholder="مثال: النقدية بالصندوق"
                            >
                        </div>

                        <!-- نوع الحساب -->
                        <div>
                            <label for="account_type" class="block text-sm font-medium text-gray-700 mb-2">
                                نوع الحساب <span class="text-red-500">*</span>
                            </label>
                            <select
                                id="account_type"
                                name="account_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                                <option value="">اختر نوع الحساب</option>
                                <?php foreach ($accountTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>"
                                            <?php echo ($currentAccount['account_type'] ?? '') === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- الحساب الأب -->
                        <div>
                            <label for="parent_account_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الحساب الأب
                            </label>
                            <select
                                id="parent_account_id"
                                name="parent_account_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">حساب رئيسي (بدون أب)</option>
                                <?php foreach ($mainAccounts as $mainAccount): ?>
                                    <option value="<?php echo $mainAccount['account_id']; ?>"
                                            <?php echo ($currentAccount['parent_account_id'] ?? '') == $mainAccount['account_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($mainAccount['account_code'] . ' - ' . $mainAccount['account_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">اختر الحساب الأب إذا كان هذا حساب فرعي</p>
                        </div>

                    </div>

                    <!-- الوصف -->
                    <div class="mt-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            وصف الحساب
                        </label>
                        <textarea
                            id="description"
                            name="description"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="وصف تفصيلي للحساب وطبيعة استخدامه"
                        ><?php echo htmlspecialchars($currentAccount['description'] ?? ''); ?></textarea>
                    </div>

                    <!-- الخيارات الإضافية -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">خيارات إضافية</h4>
                        <div class="space-y-3">

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_main_account"
                                    name="is_main_account"
                                    value="1"
                                    <?php echo ($currentAccount['is_main_account'] ?? 0) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="is_main_account" class="mr-2 block text-sm text-gray-900">
                                    حساب رئيسي
                                </label>
                                <span class="text-xs text-gray-500">(يمكن أن يحتوي على حسابات فرعية)</span>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    value="1"
                                    <?php echo ($currentAccount['is_active'] ?? 1) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    الحساب نشط
                                </label>
                            </div>

                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-8 flex justify-end space-x-4 space-x-reverse">
                        <a href="accounts.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            <?php echo $action === 'add' ? 'إضافة الحساب' : 'حفظ التغييرات'; ?>
                        </button>
                    </div>

                </form>
            </div>

        <?php endif; ?>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر النماذج */
        input:focus, select:focus, textarea:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين مظهر الجداول */
        table tr:hover {
            background-color: #f9fafb;
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تحسين عرض الشجرة */
        .account-tree tbody tr {
            border-right: 4px solid transparent;
        }

        .account-tree tbody tr:hover {
            background-color: #f9fafb;
        }
    </style>

</body>
</html>

<?php
/**
 * سكريبت بسيط لتحويل كلمات المرور إلى نص عادي
 * Simple Password Converter Script
 * 
 * ⚠️ تحذير أمني: هذا غير آمن ولا يُنصح به في البيئات الإنتاجية
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['convert'])) {
        $defaultPassword = sanitizeInput($_POST['default_password'] ?? '');
        
        if (empty($defaultPassword)) {
            $message = 'يرجى إدخال كلمة مرور افتراضية';
            $messageType = 'error';
        } else {
            // فحص هيكل الجدول
            $columns = $db->select("SHOW COLUMNS FROM users");
            $hasPasswordHash = false;
            $hasPassword = false;
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'password_hash') $hasPasswordHash = true;
                if ($column['Field'] === 'password') $hasPassword = true;
            }
            
            if ($hasPasswordHash && !$hasPassword) {
                // إضافة عمود password
                $db->execute("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''");
                
                // تحديث كلمات المرور
                $db->update("UPDATE users SET password = ?", [$defaultPassword]);
                
                // حذف العمود القديم
                $db->execute("ALTER TABLE users DROP COLUMN password_hash");
                
                $message = "تم التحويل بنجاح! كلمة المرور الافتراضية: {$defaultPassword}";
                $messageType = 'success';
                
            } elseif ($hasPassword && $hasPasswordHash) {
                // تحديث كلمات المرور وحذف العمود القديم
                $db->update("UPDATE users SET password = ?", [$defaultPassword]);
                $db->execute("ALTER TABLE users DROP COLUMN password_hash");
                
                $message = "تم التحديث بنجاح!";
                $messageType = 'success';
                
            } elseif ($hasPassword && !$hasPasswordHash) {
                // تحديث كلمات المرور فقط
                $db->update("UPDATE users SET password = ?", [$defaultPassword]);
                
                $message = "تم تحديث كلمات المرور بنجاح!";
                $messageType = 'success';
                
            } else {
                $message = 'هيكل الجدول غير متوقع';
                $messageType = 'error';
            }
        }
    }
    
    // الحصول على المستخدمين
    $users = $db->select("SELECT user_id, username, full_name, role FROM users ORDER BY username");
    
} catch (Exception $e) {
    $message = 'خطأ: ' . $e->getMessage();
    $messageType = 'error';
    $users = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحويل كلمات المرور - نسخة مبسطة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        
        <!-- تحذير أمني -->
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <strong>تحذير أمني:</strong> هذا السكريبت يحول كلمات المرور إلى نص عادي (غير آمن)
            </div>
        </div>

        <!-- البطاقة الرئيسية -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-key mr-2"></i>
                تحويل كلمات المرور - نسخة مبسطة
            </h1>

            <?php if ($message): ?>
                <div class="mb-4 p-4 rounded <?php echo $messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج التحويل -->
            <form method="POST" class="mb-6">
                <div class="mb-4">
                    <label for="default_password" class="block text-gray-700 text-sm font-bold mb-2">
                        كلمة المرور الافتراضية:
                    </label>
                    <input 
                        type="text" 
                        id="default_password" 
                        name="default_password" 
                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500"
                        placeholder="أدخل كلمة المرور الافتراضية"
                        required
                        dir="ltr"
                    >
                </div>
                
                <button 
                    type="submit" 
                    name="convert"
                    class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-200"
                    onclick="return confirm('هل أنت متأكد من التحويل؟')"
                >
                    <i class="fas fa-exchange-alt mr-2"></i>
                    تحويل كلمات المرور
                </button>
            </form>

            <!-- قائمة المستخدمين -->
            <?php if (!empty($users)): ?>
                <div class="border-t pt-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">
                        المستخدمون (<?php echo count($users); ?>):
                    </h3>
                    <div class="space-y-2">
                        <?php foreach ($users as $user): ?>
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <div>
                                    <span class="font-medium"><?php echo htmlspecialchars($user['username']); ?></span>
                                    <span class="text-gray-600 text-sm">- <?php echo htmlspecialchars($user['full_name']); ?></span>
                                </div>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    <?php echo $user['role']; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- روابط التنقل -->
            <div class="mt-6 text-center space-x-4">
                <a href="index.php" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-home mr-2"></i>
                    الرئيسية
                </a>
                <a href="login.php" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    تسجيل الدخول
                </a>
            </div>
        </div>
    </div>
</body>
</html>

<?php
/**
 * اختبار سريع لجداول الفواتير
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار سريع لجداول الفواتير</h1>";

try {
    $db = Database::getInstance();
    
    // فحص وجود الجداول
    echo "<h2>1. فحص وجود الجداول:</h2>";
    
    $invoicesTable = $db->select("SHOW TABLES LIKE 'invoices'");
    $itemsTable = $db->select("SHOW TABLES LIKE 'invoice_items'");
    $customersTable = $db->select("SHOW TABLES LIKE 'customers'");
    
    echo "<ul>";
    echo "<li style='color: " . (!empty($invoicesTable) ? 'green' : 'red') . ";'>";
    echo (!empty($invoicesTable) ? '✅' : '❌') . " جدول الفواتير الرئيسي</li>";
    
    echo "<li style='color: " . (!empty($itemsTable) ? 'green' : 'red') . ";'>";
    echo (!empty($itemsTable) ? '✅' : '❌') . " جدول عناصر الفواتير</li>";
    
    echo "<li style='color: " . (!empty($customersTable) ? 'green' : 'red') . ";'>";
    echo (!empty($customersTable) ? '✅' : '❌') . " جدول العملاء (مطلوب للفواتير)</li>";
    echo "</ul>";
    
    if (empty($invoicesTable) || empty($itemsTable)) {
        echo "<p><a href='setup_invoices.php' style='color: blue;'>اضغط هنا لإنشاء جداول الفواتير</a></p>";
    }
    
    if (empty($customersTable)) {
        echo "<p><a href='setup_customers.php' style='color: blue;'>اضغط هنا لإنشاء جدول العملاء</a></p>";
    }
    
    // إذا كانت الجداول موجودة، اعرض المعلومات
    if (!empty($invoicesTable) && !empty($itemsTable) && !empty($customersTable)) {
        
        // عدد السجلات
        echo "<h2>2. عدد السجلات:</h2>";
        $invoicesCount = $db->selectOne("SELECT COUNT(*) as count FROM invoices");
        $itemsCount = $db->selectOne("SELECT COUNT(*) as count FROM invoice_items");
        $customersCount = $db->selectOne("SELECT COUNT(*) as count FROM customers");
        
        echo "<ul>";
        echo "<li>عدد الفواتير: " . $invoicesCount['count'] . "</li>";
        echo "<li>عدد عناصر الفواتير: " . $itemsCount['count'] . "</li>";
        echo "<li>عدد العملاء: " . $customersCount['count'] . "</li>";
        echo "</ul>";
        
        // عرض بعض البيانات
        if ($invoicesCount['count'] > 0) {
            echo "<h2>3. الفواتير الموجودة:</h2>";
            $invoices = $db->select("
                SELECT i.invoice_number, i.invoice_type, i.total_amount, i.status, 
                       c.customer_name, i.invoice_date
                FROM invoices i 
                LEFT JOIN customers c ON i.customer_id = c.customer_id 
                ORDER BY i.invoice_date DESC 
                LIMIT 5
            ");
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>رقم الفاتورة</th><th>النوع</th><th>العميل</th><th>المبلغ</th><th>الحالة</th><th>التاريخ</th></tr>";
            foreach ($invoices as $invoice) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($invoice['invoice_number']) . "</td>";
                echo "<td>" . ($invoice['invoice_type'] === 'sales' ? 'مبيعات' : 'مشتريات') . "</td>";
                echo "<td>" . htmlspecialchars($invoice['customer_name'] ?? 'غير محدد') . "</td>";
                echo "<td>" . number_format($invoice['total_amount'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($invoice['status']) . "</td>";
                echo "<td>" . $invoice['invoice_date'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // اختبار نموذج الفاتورة
        echo "<h2>4. اختبار نموذج الفاتورة:</h2>";
        try {
            $invoiceModel = new Invoice();
            echo "<p style='color: green;'>✅ نموذج الفاتورة تم تحميله بنجاح</p>";
            
            $invoices = $invoiceModel->getAllInvoices();
            echo "<p>عدد الفواتير من النموذج: " . count($invoices) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في نموذج الفاتورة: " . $e->getMessage() . "</p>";
        }
        
        // اختبار نموذج العميل
        echo "<h2>5. اختبار نموذج العميل:</h2>";
        try {
            $customerModel = new Customer();
            echo "<p style='color: green;'>✅ نموذج العميل تم تحميله بنجاح</p>";
            
            $customers = $customerModel->getAllCustomers();
            echo "<p>عدد العملاء من النموذج: " . count($customers) . "</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في نموذج العميل: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p>";
echo "<a href='setup_invoices.php'>إعداد جداول الفواتير</a> | ";
echo "<a href='setup_customers.php'>إعداد جدول العملاء</a> | ";
echo "<a href='invoices.php'>إدارة الفواتير</a> | ";
echo "<a href='customers.php'>إدارة العملاء</a> | ";
echo "<a href='index.php'>الرئيسية</a>";
echo "</p>";
?>

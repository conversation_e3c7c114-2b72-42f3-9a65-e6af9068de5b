<?php
/**
 * نموذج المعاملات المحاسبية
 * Accounting Transactions Model
 */

class Transaction {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع المعاملات
     */
    public function getAllTransactions($dateFrom = null, $dateTo = null, $accountId = null, $limit = 100, $offset = 0) {
        try {
            $query = "SELECT 
                        at.transaction_id, at.voucher_number, at.transaction_date,
                        at.account_id, at.debit_amount, at.credit_amount,
                        at.description, at.reference_type, at.reference_id,
                        at.reference_number, at.is_posted, at.posted_at,
                        at.created_at, coa.account_name, coa.account_code,
                        u.full_name as created_by_name
                      FROM account_transactions at
                      JOIN chart_of_accounts coa ON at.account_id = coa.account_id
                      LEFT JOIN users u ON at.created_by = u.user_id
                      WHERE 1=1";
            
            $params = [];
            
            if ($dateFrom) {
                $query .= " AND at.transaction_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $query .= " AND at.transaction_date <= ?";
                $params[] = $dateTo;
            }
            
            if ($accountId) {
                $query .= " AND at.account_id = ?";
                $params[] = $accountId;
            }
            
            $query .= " ORDER BY at.transaction_date DESC, at.voucher_number DESC";
            
            if ($limit) {
                $query .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all transactions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على معاملة بالمعرف
     */
    public function getTransactionById($transactionId) {
        try {
            $query = "SELECT 
                        at.*, coa.account_name, coa.account_code,
                        u.full_name as created_by_name
                      FROM account_transactions at
                      JOIN chart_of_accounts coa ON at.account_id = coa.account_id
                      LEFT JOIN users u ON at.created_by = u.user_id
                      WHERE at.transaction_id = ?";
            
            return $this->db->selectOne($query, [$transactionId]);
            
        } catch (Exception $e) {
            error_log("Get transaction by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على معاملات القيد الواحد
     */
    public function getVoucherTransactions($voucherNumber) {
        try {
            $query = "SELECT 
                        at.*, coa.account_name, coa.account_code
                      FROM account_transactions at
                      JOIN chart_of_accounts coa ON at.account_id = coa.account_id
                      WHERE at.voucher_number = ?
                      ORDER BY at.debit_amount DESC, at.credit_amount DESC";
            
            return $this->db->select($query, [$voucherNumber]);
            
        } catch (Exception $e) {
            error_log("Get voucher transactions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إنشاء قيد محاسبي
     */
    public function createJournalEntry($voucherData, $transactions) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من توازن القيد
            $totalDebit = 0;
            $totalCredit = 0;
            
            foreach ($transactions as $transaction) {
                $totalDebit += $transaction['debit_amount'] ?? 0;
                $totalCredit += $transaction['credit_amount'] ?? 0;
            }
            
            if (abs($totalDebit - $totalCredit) > 0.01) {
                throw new Exception('القيد غير متوازن. مجموع المدين يجب أن يساوي مجموع الدائن');
            }
            
            // إنتاج رقم القيد إذا لم يكن موجوداً
            $voucherNumber = $voucherData['voucher_number'] ?? $this->generateVoucherNumber();
            
            // إدراج المعاملات
            foreach ($transactions as $transaction) {
                $query = "INSERT INTO account_transactions (
                            voucher_number, transaction_date, account_id, debit_amount,
                            credit_amount, description, reference_type, reference_id,
                            reference_number, is_posted, posted_at, created_by, created_at
                          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $params = [
                    $voucherNumber,
                    $voucherData['transaction_date'] ?? date('Y-m-d'),
                    $transaction['account_id'],
                    $transaction['debit_amount'] ?? 0,
                    $transaction['credit_amount'] ?? 0,
                    $transaction['description'] ?? $voucherData['description'] ?? '',
                    $voucherData['reference_type'] ?? null,
                    $voucherData['reference_id'] ?? null,
                    $voucherData['reference_number'] ?? null,
                    $voucherData['is_posted'] ?? 1,
                    $voucherData['is_posted'] ?? 1 ? date('Y-m-d H:i:s') : null,
                    $_SESSION['user_id'] ?? null
                ];
                
                $transactionId = $this->db->insert($query, $params);
                
                if (!$transactionId) {
                    throw new Exception('فشل في إدراج المعاملة');
                }
            }
            
            $this->db->commit();
            
            logActivity('إنشاء قيد محاسبي', "تم إنشاء القيد رقم: {$voucherNumber}");
            
            return $voucherNumber;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create journal entry error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث معاملة
     */
    public function updateTransaction($transactionId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['account_id'])) {
                $setParts[] = "account_id = ?";
                $params[] = $data['account_id'];
            }
            
            if (isset($data['debit_amount'])) {
                $setParts[] = "debit_amount = ?";
                $params[] = $data['debit_amount'];
            }
            
            if (isset($data['credit_amount'])) {
                $setParts[] = "credit_amount = ?";
                $params[] = $data['credit_amount'];
            }
            
            if (isset($data['description'])) {
                $setParts[] = "description = ?";
                $params[] = $data['description'];
            }
            
            if (isset($data['transaction_date'])) {
                $setParts[] = "transaction_date = ?";
                $params[] = $data['transaction_date'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $transactionId;
            
            $query = "UPDATE account_transactions SET " . implode(', ', $setParts) . " WHERE transaction_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث معاملة', "تم تحديث المعاملة ID: {$transactionId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update transaction error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف قيد محاسبي
     */
    public function deleteVoucher($voucherNumber) {
        try {
            // التحقق من أن القيد غير مرحل
            $postedTransactions = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM account_transactions WHERE voucher_number = ? AND is_posted = 1",
                [$voucherNumber]
            );
            
            if ($postedTransactions['count'] > 0) {
                throw new Exception('لا يمكن حذف قيد مرحل');
            }
            
            $query = "DELETE FROM account_transactions WHERE voucher_number = ?";
            $result = $this->db->delete($query, [$voucherNumber]);
            
            if ($result) {
                logActivity('حذف قيد محاسبي', "تم حذف القيد رقم: {$voucherNumber}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete voucher error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * ترحيل قيد محاسبي
     */
    public function postVoucher($voucherNumber) {
        try {
            $query = "UPDATE account_transactions 
                      SET is_posted = 1, posted_at = NOW() 
                      WHERE voucher_number = ? AND is_posted = 0";
            
            $result = $this->db->update($query, [$voucherNumber]);
            
            if ($result) {
                logActivity('ترحيل قيد محاسبي', "تم ترحيل القيد رقم: {$voucherNumber}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Post voucher error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إلغاء ترحيل قيد محاسبي
     */
    public function unpostVoucher($voucherNumber) {
        try {
            $query = "UPDATE account_transactions 
                      SET is_posted = 0, posted_at = NULL 
                      WHERE voucher_number = ? AND is_posted = 1";
            
            $result = $this->db->update($query, [$voucherNumber]);
            
            if ($result) {
                logActivity('إلغاء ترحيل قيد', "تم إلغاء ترحيل القيد رقم: {$voucherNumber}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Unpost voucher error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إنتاج رقم قيد تلقائي
     */
    private function generateVoucherNumber() {
        $date = date('Ymd');
        
        // الحصول على آخر رقم قيد لهذا اليوم
        $lastVoucher = $this->db->selectOne("
            SELECT voucher_number FROM account_transactions 
            WHERE voucher_number LIKE ? 
            ORDER BY voucher_number DESC LIMIT 1
        ", ["JV-{$date}-%"]);
        
        $sequence = 1;
        if ($lastVoucher) {
            $parts = explode('-', $lastVoucher['voucher_number']);
            if (count($parts) >= 3) {
                $sequence = intval($parts[2]) + 1;
            }
        }
        
        return sprintf("JV-%s-%04d", $date, $sequence);
    }
    
    /**
     * الحصول على ميزان المراجعة
     */
    public function getTrialBalance($dateFrom = null, $dateTo = null) {
        try {
            $query = "SELECT 
                        coa.account_id, coa.account_code, coa.account_name, coa.account_type,
                        COALESCE(SUM(at.debit_amount), 0) as total_debit,
                        COALESCE(SUM(at.credit_amount), 0) as total_credit,
                        COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as balance
                      FROM chart_of_accounts coa
                      LEFT JOIN account_transactions at ON coa.account_id = at.account_id AND at.is_posted = 1";
            
            $params = [];
            $conditions = ["coa.is_active = 1"];
            
            if ($dateFrom) {
                $conditions[] = "(at.transaction_date >= ? OR at.transaction_date IS NULL)";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $conditions[] = "(at.transaction_date <= ? OR at.transaction_date IS NULL)";
                $params[] = $dateTo;
            }
            
            $query .= " WHERE " . implode(' AND ', $conditions);
            $query .= " GROUP BY coa.account_id, coa.account_code, coa.account_name, coa.account_type";
            $query .= " HAVING total_debit > 0 OR total_credit > 0 OR balance != 0";
            $query .= " ORDER BY coa.account_code";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get trial balance error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * البحث في المعاملات
     */
    public function searchTransactions($searchTerm, $dateFrom = null, $dateTo = null) {
        try {
            $query = "SELECT 
                        at.*, coa.account_name, coa.account_code
                      FROM account_transactions at
                      JOIN chart_of_accounts coa ON at.account_id = coa.account_id
                      WHERE (at.voucher_number LIKE ? OR at.description LIKE ? OR coa.account_name LIKE ?)";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($dateFrom) {
                $query .= " AND at.transaction_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $query .= " AND at.transaction_date <= ?";
                $params[] = $dateTo;
            }
            
            $query .= " ORDER BY at.transaction_date DESC LIMIT 100";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search transactions error: " . $e->getMessage());
            return [];
        }
    }
}

?>

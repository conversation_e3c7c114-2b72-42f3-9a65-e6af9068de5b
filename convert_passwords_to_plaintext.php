<?php
/**
 * سكريبت تحويل كلمات المرور من مشفرة إلى نص عادي
 * Convert Encrypted Passwords to Plain Text Script
 * 
 * ⚠️ تحذير أمني خطير:
 * هذا السكريبت يحول كلمات المرور المشفرة إلى نص عادي
 * هذا يشكل خطراً أمنياً كبيراً ولا يُنصح به في البيئات الإنتاجية
 * استخدم هذا السكريبت على مسؤوليتك الخاصة
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';
$conversionDone = false;

// التحقق من وجود كلمات مرور مشفرة
$db = Database::getInstance();

try {
    // فحص هيكل الجدول الحالي
    $tableStructure = $db->select("DESCRIBE users");
    $hasPasswordHash = false;
    $hasPassword = false;
    
    foreach ($tableStructure as $column) {
        if ($column['Field'] === 'password_hash') {
            $hasPasswordHash = true;
        }
        if ($column['Field'] === 'password') {
            $hasPassword = true;
        }
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['convert_passwords'])) {
        $defaultPassword = sanitizeInput($_POST['default_password'] ?? '');
        
        if (empty($defaultPassword)) {
            $message = 'يرجى إدخال كلمة مرور افتراضية';
            $messageType = 'error';
        } else {
            try {
                // إذا كان الجدول يحتوي على password_hash، نحتاج لتحويل الهيكل
                if ($hasPasswordHash && !$hasPassword) {
                    // بدء المعاملة
                    $db->beginTransaction();

                    // إضافة عمود password جديد
                    $db->execute("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT '' AFTER username");

                    // تحديث جميع المستخدمين بكلمة المرور الافتراضية
                    $db->execute("UPDATE users SET password = ?", [$defaultPassword]);

                    // حذف العمود القديم
                    $db->execute("ALTER TABLE users DROP COLUMN password_hash");

                    // تأكيد المعاملة
                    $db->commit();

                    $message = "تم تحويل هيكل قاعدة البيانات وتعيين كلمة المرور الافتراضية '{$defaultPassword}' لجميع المستخدمين";
                    $messageType = 'success';
                    $conversionDone = true;

                } elseif ($hasPassword && $hasPasswordHash) {
                    // بدء المعاملة
                    $db->beginTransaction();

                    // إذا كان كلا العمودين موجودين، نحديث password ونحذف password_hash
                    $db->execute("UPDATE users SET password = ?", [$defaultPassword]);
                    $db->execute("ALTER TABLE users DROP COLUMN password_hash");

                    // تأكيد المعاملة
                    $db->commit();

                    $message = "تم تحديث كلمات المرور وحذف العمود المشفر";
                    $messageType = 'success';
                    $conversionDone = true;

                } elseif ($hasPassword && !$hasPasswordHash) {
                    $message = 'النظام يستخدم بالفعل كلمات مرور غير مشفرة';
                    $messageType = 'info';

                } else {
                    $message = 'هيكل الجدول غير متوقع';
                    $messageType = 'error';
                }

            } catch (Exception $e) {
                // إلغاء المعاملة في حالة الخطأ (إذا كانت نشطة)
                if ($db->inTransaction()) {
                    try {
                        $db->rollback();
                    } catch (Exception $rollbackException) {
                        // تسجيل خطأ rollback ولكن لا نرمي استثناء
                        error_log("Rollback failed: " . $rollbackException->getMessage());
                    }
                }
                throw $e;
            }
        }
    }
    
    // الحصول على قائمة المستخدمين الحاليين
    $users = $db->select("SELECT user_id, username, full_name, role FROM users ORDER BY username");
    
} catch (Exception $e) {
    $message = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
    $messageType = 'error';
    $users = [];
}

$pageTitle = 'تحويل كلمات المرور إلى نص عادي';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gradient-to-br from-red-500 via-red-600 to-red-700 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- تحذير أمني -->
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-6 mb-6 rounded-lg shadow-lg">
                <div class="flex items-center mb-4">
                    <i class="fas fa-exclamation-triangle text-2xl ml-3"></i>
                    <h2 class="text-xl font-bold">⚠️ تحذير أمني خطير</h2>
                </div>
                <ul class="list-disc list-inside space-y-2">
                    <li>هذا السكريبت يحول كلمات المرور المشفرة إلى نص عادي</li>
                    <li>هذا يشكل خطراً أمنياً كبيراً جداً</li>
                    <li>كلمات المرور ستكون مكشوفة لأي شخص يصل إلى قاعدة البيانات</li>
                    <li>لا يُنصح بهذا في البيئات الإنتاجية</li>
                    <li>استخدم هذا على مسؤوليتك الخاصة</li>
                </ul>
            </div>

            <!-- بطاقة التحويل -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <div class="text-center mb-8">
                    <i class="fas fa-key text-4xl text-red-600 mb-4"></i>
                    <h1 class="text-3xl font-bold text-gray-800"><?php echo $pageTitle; ?></h1>
                </div>

                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : ($messageType === 'error' ? 'bg-red-100 text-red-700 border border-red-300' : 'bg-blue-100 text-blue-700 border border-blue-300'); ?>">
                        <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : ($messageType === 'error' ? 'fa-times-circle' : 'fa-info-circle'); ?> ml-2"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <?php if (!$conversionDone): ?>
                    <!-- نموذج التحويل -->
                    <form method="POST" action="" class="space-y-6">
                        <div>
                            <label for="default_password" class="block text-gray-700 text-sm font-medium mb-2">
                                <i class="fas fa-lock ml-2"></i>
                                كلمة المرور الافتراضية لجميع المستخدمين
                            </label>
                            <input 
                                type="text" 
                                id="default_password" 
                                name="default_password" 
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                placeholder="أدخل كلمة المرور الافتراضية"
                                required
                                dir="ltr"
                            >
                            <p class="text-sm text-gray-600 mt-2">
                                <i class="fas fa-info-circle ml-1"></i>
                                سيتم تعيين هذه كلمة المرور لجميع المستخدمين في النظام
                            </p>
                        </div>

                        <div class="flex justify-center">
                            <button 
                                type="submit" 
                                name="convert_passwords"
                                class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg transition duration-300 transform hover:scale-105"
                                onclick="return confirm('هل أنت متأكد من تحويل كلمات المرور إلى نص عادي؟ هذا غير آمن!')"
                            >
                                <i class="fas fa-exchange-alt ml-2"></i>
                                تحويل كلمات المرور
                            </button>
                        </div>
                    </form>
                <?php endif; ?>

                <!-- قائمة المستخدمين -->
                <?php if (!empty($users)): ?>
                    <div class="mt-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">
                            <i class="fas fa-users ml-2"></i>
                            المستخدمون في النظام (<?php echo count($users); ?>)
                        </h3>
                        <div class="overflow-x-auto">
                            <table class="w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">المعرف</th>
                                        <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">اسم المستخدم</th>
                                        <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">الاسم الكامل</th>
                                        <th class="px-4 py-3 text-right text-sm font-medium text-gray-700">الدور</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <?php foreach ($users as $user): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 text-sm text-gray-900"><?php echo $user['user_id']; ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td class="px-4 py-3 text-sm text-gray-900"><?php echo htmlspecialchars($user['full_name']); ?></td>
                                            <td class="px-4 py-3 text-sm">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    <?php echo $user['role'] === 'admin' ? 'bg-red-100 text-red-800' : 
                                                        ($user['role'] === 'accountant' ? 'bg-blue-100 text-blue-800' : 
                                                        'bg-gray-100 text-gray-800'); ?>">
                                                    <?php echo $user['role']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- روابط التنقل -->
                <div class="mt-8 text-center space-x-4">
                    <a href="index.php" class="inline-block bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-home ml-2"></i>
                        العودة للرئيسية
                    </a>
                    <?php if ($conversionDone): ?>
                        <a href="login.php" class="inline-block bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg transition duration-300">
                            <i class="fas fa-sign-in-alt ml-2"></i>
                            تسجيل الدخول
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

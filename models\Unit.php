<?php
/**
 * نموذج الوحدات
 * Unit Model
 */

class Unit {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الوحدات
     */
    public function getAllUnits($activeOnly = true) {
        try {
            $query = "SELECT unit_id, unit_code, unit_name, unit_symbol, unit_type, 
                            is_base_unit, description, is_active, created_at
                      FROM units";
            
            if ($activeOnly) {
                $query .= " WHERE is_active = 1";
            }
            
            $query .= " ORDER BY unit_type, unit_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all units error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على وحدة بالمعرف
     */
    public function getUnitById($unitId) {
        try {
            $query = "SELECT * FROM units WHERE unit_id = ?";
            return $this->db->selectOne($query, [$unitId]);
            
        } catch (Exception $e) {
            error_log("Get unit by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء وحدة جديدة
     */
    public function createUnit($data) {
        try {
            // التحقق من عدم وجود كود الوحدة
            if ($this->unitCodeExists($data['unit_code'])) {
                throw new Exception('كود الوحدة موجود بالفعل');
            }
            
            $query = "INSERT INTO units (
                        unit_code, unit_name, unit_symbol, unit_type, 
                        is_base_unit, description, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['unit_code'],
                $data['unit_name'],
                $data['unit_symbol'] ?? null,
                $data['unit_type'] ?? 'piece',
                $data['is_base_unit'] ?? 0,
                $data['description'] ?? null,
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $unitId = $this->db->insert($query, $params);
            
            if ($unitId) {
                logActivity('إنشاء وحدة', "تم إنشاء الوحدة: {$data['unit_name']}");
            }
            
            return $unitId;
            
        } catch (Exception $e) {
            error_log("Create unit error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الوحدة
     */
    public function updateUnit($unitId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['unit_name'])) {
                $setParts[] = "unit_name = ?";
                $params[] = $data['unit_name'];
            }
            
            if (isset($data['unit_symbol'])) {
                $setParts[] = "unit_symbol = ?";
                $params[] = $data['unit_symbol'];
            }
            
            if (isset($data['unit_type'])) {
                $setParts[] = "unit_type = ?";
                $params[] = $data['unit_type'];
            }
            
            if (isset($data['is_base_unit'])) {
                $setParts[] = "is_base_unit = ?";
                $params[] = $data['is_base_unit'];
            }
            
            if (isset($data['description'])) {
                $setParts[] = "description = ?";
                $params[] = $data['description'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $unitId;
            
            $query = "UPDATE units SET " . implode(', ', $setParts) . " WHERE unit_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث وحدة', "تم تحديث الوحدة ID: {$unitId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update unit error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الوحدة (إلغاء تفعيل)
     */
    public function deleteUnit($unitId) {
        try {
            // التحقق من عدم وجود أصناف مرتبطة
            $itemsCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM items WHERE base_unit_id = ? AND is_active = 1",
                [$unitId]
            );
            
            if ($itemsCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الوحدة لوجود أصناف مرتبطة بها');
            }
            
            $query = "UPDATE units SET is_active = 0, updated_at = NOW() WHERE unit_id = ?";
            $result = $this->db->update($query, [$unitId]);
            
            if ($result) {
                logActivity('حذف وحدة', "تم حذف الوحدة ID: {$unitId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete unit error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود الوحدة
     */
    private function unitCodeExists($unitCode, $excludeUnitId = null) {
        $query = "SELECT COUNT(*) as count FROM units WHERE unit_code = ?";
        $params = [$unitCode];
        
        if ($excludeUnitId) {
            $query .= " AND unit_id != ?";
            $params[] = $excludeUnitId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على الوحدات حسب النوع
     */
    public function getUnitsByType($unitType, $activeOnly = true) {
        try {
            $query = "SELECT unit_id, unit_code, unit_name, unit_symbol
                      FROM units 
                      WHERE unit_type = ?";
            
            $params = [$unitType];
            
            if ($activeOnly) {
                $query .= " AND is_active = 1";
            }
            
            $query .= " ORDER BY unit_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get units by type error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على أنواع الوحدات
     */
    public function getUnitTypes() {
        return [
            'weight' => 'وزن',
            'length' => 'طول',
            'volume' => 'حجم',
            'piece' => 'قطعة',
            'area' => 'مساحة',
            'time' => 'وقت',
            'other' => 'أخرى'
        ];
    }
}

?>

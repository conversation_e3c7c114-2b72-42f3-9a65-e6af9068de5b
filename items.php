<?php
/**
 * صفحة إدارة الأصناف
 * Items Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('items_manage');

// إنشاء مثيلات النماذج
$itemModel = new Item();
$categoryModel = new Category();
$unitModel = new Unit();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$itemId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $itemId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';

        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($itemModel->deleteItem($itemId)) {
                    setAlert('تم حذف الصنف بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف الصنف', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }

        redirect('items.php');
    }
}

// معالجة إضافة/تعديل الصنف
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';

    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'item_code' => sanitizeInput($_POST['item_code'] ?? ''),
            'item_name' => sanitizeInput($_POST['item_name'] ?? ''),
            'item_description' => sanitizeInput($_POST['item_description'] ?? ''),
            'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            'base_unit_id' => (int)($_POST['base_unit_id'] ?? 0),
            'barcode' => sanitizeInput($_POST['barcode'] ?? ''),
            'item_type' => sanitizeInput($_POST['item_type'] ?? 'product'),
            'cost_method' => sanitizeInput($_POST['cost_method'] ?? 'average'),
            'min_stock_level' => (float)($_POST['min_stock_level'] ?? 0),
            'max_stock_level' => (float)($_POST['max_stock_level'] ?? 0),
            'reorder_level' => (float)($_POST['reorder_level'] ?? 0),
            'purchase_price' => (float)($_POST['purchase_price'] ?? 0),
            'sale_price' => (float)($_POST['sale_price'] ?? 0),
            'wholesale_price' => (float)($_POST['wholesale_price'] ?? 0),
            'has_serial_numbers' => isset($_POST['has_serial_numbers']) ? 1 : 0,
            'has_expiry_date' => isset($_POST['has_expiry_date']) ? 1 : 0,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'notes' => sanitizeInput($_POST['notes'] ?? '')
        ];

        // التحقق من البيانات المطلوبة
        if (empty($data['item_code']) || empty($data['item_name']) || empty($data['base_unit_id'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                if ($action === 'add') {
                    $newItemId = $itemModel->createItem($data);
                    if ($newItemId) {
                        setAlert('تم إضافة الصنف بنجاح', 'success');
                        redirect('items.php');
                    } else {
                        $error = 'فشل في إضافة الصنف';
                    }
                } else {
                    if ($itemModel->updateItem($itemId, $data)) {
                        setAlert('تم تحديث الصنف بنجاح', 'success');
                        redirect('items.php');
                    } else {
                        $error = 'فشل في تحديث الصنف';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentItem = null;
if ($action === 'edit' && $itemId) {
    $currentItem = $itemModel->getItemById($itemId);
    if (!$currentItem) {
        setAlert('الصنف غير موجود', 'error');
        redirect('items.php');
    }
}

// الحصول على قائمة الأصناف للعرض
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$categoryFilter = !empty($_GET['category']) ? (int)$_GET['category'] : null;
$page = max(1, (int)($_GET['page'] ?? 1));
$itemsPerPage = 20;
$offset = ($page - 1) * $itemsPerPage;

if (!empty($searchTerm)) {
    $items = $itemModel->searchItems($searchTerm, $categoryFilter);
    $totalItems = count($items);
} else {
    $items = $itemModel->getAllItems(true, $itemsPerPage, $offset);
    $totalItems = $itemModel->getItemsCount();
}

$totalPages = ceil($totalItems / $itemsPerPage);

// الحصول على البيانات المساعدة
$categories = $categoryModel->getAllCategories();
$units = $unitModel->getAllUnits();

$pageTitle = $action === 'add' ? 'إضافة صنف جديد' :
            ($action === 'edit' ? 'تعديل الصنف' : 'إدارة الأصناف');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>

                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-boxes ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">

        <!-- عرض الرسائل -->
        <?php showAlert(); ?>

        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>
            <!-- قائمة الأصناف -->
            <div class="bg-white shadow-lg rounded-lg">

                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قائمة الأصناف (<?php echo number_format($totalItems); ?> صنف)
                    </h3>
                    <a href="items.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة صنف جديد
                    </a>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <form method="GET" action="items.php" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input
                                type="text"
                                name="search"
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="البحث في الأصناف (الاسم، الكود، الباركود)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <div class="min-w-48">
                            <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"
                                            <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm) || $categoryFilter): ?>
                            <a href="items.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- جدول الأصناف -->
                <div class="overflow-x-auto">
                    <?php if (empty($items)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-box-open text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أصناف</h3>
                            <p class="text-gray-500 mb-4">لم يتم العثور على أي أصناف مطابقة للبحث</p>
                            <a href="items.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول صنف
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الصنف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوحدة الأساسية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($items as $item): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($item['item_code'] ?? 'غير محدد'); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($item['item_name'] ?? 'غير محدد'); ?>
                                            </div>
                                            <?php if (!empty($item['item_description'])): ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars(substr($item['item_description'], 0, 50)); ?>
                                                    <?php echo strlen($item['item_description']) > 50 ? '...' : ''; ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo htmlspecialchars($item['category_name'] ?? 'غير محدد'); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo htmlspecialchars($item['base_unit_name'] ?? 'غير محدد'); ?>
                                            <?php if (!empty($item['unit_symbol'])): ?>
                                                (<?php echo htmlspecialchars($item['unit_symbol']); ?>)
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $typeLabels = [
                                                'product' => 'منتج',
                                                'service' => 'خدمة',
                                                'raw_material' => 'مادة خام',
                                                'finished_goods' => 'منتج تام'
                                            ];
                                            $typeClass = [
                                                'product' => 'bg-blue-100 text-blue-800',
                                                'service' => 'bg-green-100 text-green-800',
                                                'raw_material' => 'bg-yellow-100 text-yellow-800',
                                                'finished_goods' => 'bg-purple-100 text-purple-800'
                                            ];
                                            ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo isset($item['item_type']) ? ($typeClass[$item['item_type']] ?? 'bg-gray-100 text-gray-800') : 'bg-gray-100 text-gray-800'; ?>">
                                                <?php echo isset($item['item_type']) ? ($typeLabels[$item['item_type']] ?? $item['item_type']) : 'غير محدد'; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if (isset($item['is_active']) && $item['is_active']): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="items.php?action=edit&id=<?php echo $item['item_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="items.php?action=delete&id=<?php echo $item['item_id']; ?>&confirm_delete=1"
                                                   onclick="return confirm('هل أنت متأكد من حذف الصنف \'<?php echo htmlspecialchars($item['item_name'], ENT_QUOTES); ?>\'؟')"
                                                   class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>

                <!-- ترقيم الصفحات -->
                <?php if ($totalPages > 1): ?>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                عرض <?php echo (($page - 1) * $itemsPerPage) + 1; ?> إلى
                                <?php echo min($page * $itemsPerPage, $totalItems); ?> من
                                <?php echo number_format($totalItems); ?> صنف
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <?php if ($page > 1): ?>
                                    <a href="items.php?page=<?php echo $page - 1; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?><?php echo $categoryFilter ? '&category=' . $categoryFilter : ''; ?>"
                                       class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                        السابق
                                    </a>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <a href="items.php?page=<?php echo $i; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?><?php echo $categoryFilter ? '&category=' . $categoryFilter : ''; ?>"
                                       class="px-3 py-2 text-sm <?php echo $i == $page ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'; ?> rounded transition-colors duration-200">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="items.php?page=<?php echo $page + 1; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?><?php echo $categoryFilter ? '&category=' . $categoryFilter : ''; ?>"
                                       class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                        التالي
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- نموذج إضافة/تعديل الصنف -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> ml-2"></i>
                        <?php echo $action === 'add' ? 'إضافة صنف جديد' : 'تعديل الصنف'; ?>
                    </h3>
                </div>

                <form method="POST" action="" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- كود الصنف -->
                        <div>
                            <label for="item_code" class="block text-sm font-medium text-gray-700 mb-2">
                                كود الصنف <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="item_code"
                                name="item_code"
                                value="<?php echo htmlspecialchars($currentItem['item_code'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                <?php echo $action === 'edit' ? 'readonly' : ''; ?>
                            >
                        </div>

                        <!-- اسم الصنف -->
                        <div>
                            <label for="item_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الصنف <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="item_name"
                                name="item_name"
                                value="<?php echo htmlspecialchars($currentItem['item_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                        </div>

                        <!-- الفئة -->
                        <div>
                            <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الفئة
                            </label>
                            <select
                                id="category_id"
                                name="category_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"
                                            <?php echo ($currentItem['category_id'] ?? '') == $category['category_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- الوحدة الأساسية -->
                        <div>
                            <label for="base_unit_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الوحدة الأساسية <span class="text-red-500">*</span>
                            </label>
                            <select
                                id="base_unit_id"
                                name="base_unit_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                                <option value="">اختر الوحدة</option>
                                <?php foreach ($units as $unit): ?>
                                    <option value="<?php echo $unit['unit_id']; ?>"
                                            <?php echo ($currentItem['base_unit_id'] ?? '') == $unit['unit_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($unit['unit_name']); ?>
                                        <?php if (!empty($unit['unit_symbol'])): ?>
                                            (<?php echo htmlspecialchars($unit['unit_symbol']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- الباركود -->
                        <div>
                            <label for="barcode" class="block text-sm font-medium text-gray-700 mb-2">
                                الباركود
                            </label>
                            <input
                                type="text"
                                id="barcode"
                                name="barcode"
                                value="<?php echo htmlspecialchars($currentItem['barcode'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <!-- نوع الصنف -->
                        <div>
                            <label for="item_type" class="block text-sm font-medium text-gray-700 mb-2">
                                نوع الصنف
                            </label>
                            <select
                                id="item_type"
                                name="item_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="product" <?php echo ($currentItem['item_type'] ?? 'product') === 'product' ? 'selected' : ''; ?>>منتج</option>
                                <option value="service" <?php echo ($currentItem['item_type'] ?? '') === 'service' ? 'selected' : ''; ?>>خدمة</option>
                                <option value="raw_material" <?php echo ($currentItem['item_type'] ?? '') === 'raw_material' ? 'selected' : ''; ?>>مادة خام</option>
                                <option value="finished_goods" <?php echo ($currentItem['item_type'] ?? '') === 'finished_goods' ? 'selected' : ''; ?>>منتج تام</option>
                            </select>
                        </div>

                        <!-- طريقة التكلفة -->
                        <div>
                            <label for="cost_method" class="block text-sm font-medium text-gray-700 mb-2">
                                طريقة تقييم التكلفة
                            </label>
                            <select
                                id="cost_method"
                                name="cost_method"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="average" <?php echo ($currentItem['cost_method'] ?? 'average') === 'average' ? 'selected' : ''; ?>>المتوسط المرجح</option>
                                <option value="fifo" <?php echo ($currentItem['cost_method'] ?? '') === 'fifo' ? 'selected' : ''; ?>>الوارد أولاً صادر أولاً</option>
                                <option value="lifo" <?php echo ($currentItem['cost_method'] ?? '') === 'lifo' ? 'selected' : ''; ?>>الوارد أخيراً صادر أولاً</option>
                                <option value="specific" <?php echo ($currentItem['cost_method'] ?? '') === 'specific' ? 'selected' : ''; ?>>التكلفة المحددة</option>
                            </select>
                        </div>

                    </div>

                    <!-- مستويات المخزون -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">مستويات المخزون</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">

                            <div>
                                <label for="min_stock_level" class="block text-sm font-medium text-gray-700 mb-2">
                                    الحد الأدنى للمخزون
                                </label>
                                <input
                                    type="number"
                                    id="min_stock_level"
                                    name="min_stock_level"
                                    value="<?php echo $currentItem['min_stock_level'] ?? '0'; ?>"
                                    step="0.001"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                            <div>
                                <label for="max_stock_level" class="block text-sm font-medium text-gray-700 mb-2">
                                    الحد الأقصى للمخزون
                                </label>
                                <input
                                    type="number"
                                    id="max_stock_level"
                                    name="max_stock_level"
                                    value="<?php echo $currentItem['max_stock_level'] ?? '0'; ?>"
                                    step="0.001"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                            <div>
                                <label for="reorder_level" class="block text-sm font-medium text-gray-700 mb-2">
                                    نقطة إعادة الطلب
                                </label>
                                <input
                                    type="number"
                                    id="reorder_level"
                                    name="reorder_level"
                                    value="<?php echo $currentItem['reorder_level'] ?? '0'; ?>"
                                    step="0.001"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                        </div>
                    </div>

                    <!-- الأسعار -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">الأسعار (للوحدة الأساسية)</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">

                            <div>
                                <label for="purchase_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    سعر الشراء
                                </label>
                                <input
                                    type="number"
                                    id="purchase_price"
                                    name="purchase_price"
                                    value="<?php echo $currentItem['purchase_price'] ?? '0'; ?>"
                                    step="0.01"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                            <div>
                                <label for="sale_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    سعر البيع
                                </label>
                                <input
                                    type="number"
                                    id="sale_price"
                                    name="sale_price"
                                    value="<?php echo $currentItem['sale_price'] ?? '0'; ?>"
                                    step="0.01"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                            <div>
                                <label for="wholesale_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    سعر الجملة
                                </label>
                                <input
                                    type="number"
                                    id="wholesale_price"
                                    name="wholesale_price"
                                    value="<?php echo $currentItem['wholesale_price'] ?? '0'; ?>"
                                    step="0.01"
                                    min="0"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>

                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="mt-6">
                        <label for="item_description" class="block text-sm font-medium text-gray-700 mb-2">
                            وصف الصنف
                        </label>
                        <textarea
                            id="item_description"
                            name="item_description"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="أدخل وصف مفصل للصنف"
                        ><?php echo htmlspecialchars($currentItem['item_description'] ?? ''); ?></textarea>
                    </div>

                    <!-- الملاحظات -->
                    <div class="mt-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            ملاحظات إضافية
                        </label>
                        <textarea
                            id="notes"
                            name="notes"
                            rows="2"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="ملاحظات أو تعليقات إضافية"
                        ><?php echo htmlspecialchars($currentItem['notes'] ?? ''); ?></textarea>
                    </div>

                    <!-- الخيارات الإضافية -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">خيارات إضافية</h4>
                        <div class="space-y-3">

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="has_serial_numbers"
                                    name="has_serial_numbers"
                                    value="1"
                                    <?php echo ($currentItem['has_serial_numbers'] ?? 0) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="has_serial_numbers" class="mr-2 block text-sm text-gray-900">
                                    يحتاج أرقام تسلسلية
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="has_expiry_date"
                                    name="has_expiry_date"
                                    value="1"
                                    <?php echo ($currentItem['has_expiry_date'] ?? 0) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="has_expiry_date" class="mr-2 block text-sm text-gray-900">
                                    له تاريخ انتهاء صلاحية
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    value="1"
                                    <?php echo ($currentItem['is_active'] ?? 1) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    الصنف نشط
                                </label>
                            </div>

                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-8 flex justify-end space-x-4 space-x-reverse">
                        <a href="items.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            <?php echo $action === 'add' ? 'إضافة الصنف' : 'حفظ التغييرات'; ?>
                        </button>
                    </div>

                </form>
            </div>

        <?php endif; ?>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر النماذج */
        input:focus, select:focus, textarea:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين مظهر الجداول */
        table tr:hover {
            background-color: #f9fafb;
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تأثير التحميل للنماذج */
        form:target {
            opacity: 0.7;
            pointer-events: none;
        }

        /* تحسين مظهر البطاقات */
        .card-hover:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>

</body>
</html>
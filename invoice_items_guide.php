<?php
/**
 * دليل استخدام نظام إدارة عناصر الفاتورة
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$pageTitle = 'دليل استخدام نظام إدارة عناصر الفاتورة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-book mr-3"></i>
                دليل استخدام نظام إدارة عناصر الفاتورة
            </h1>

            <!-- نظرة عامة -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">
                    <i class="fas fa-info-circle mr-2"></i>
                    نظرة عامة
                </h2>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <p class="text-gray-700 leading-relaxed">
                        تم تطوير نظام إدارة عناصر الفاتورة ليتيح لك إضافة عناصر متعددة لكل فاتورة بدون استخدام JavaScript. 
                        يعتمد النظام على PHP فقط ويستخدم الجلسات (Sessions) لحفظ العناصر مؤقتاً حتى يتم حفظ الفاتورة نهائياً.
                    </p>
                </div>
            </div>

            <!-- كيفية الاستخدام -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">
                    <i class="fas fa-list-ol mr-2"></i>
                    خطوات إنشاء فاتورة مع عناصر متعددة
                </h2>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">1</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">الانتقال لصفحة إنشاء الفاتورة</h3>
                            <p class="text-gray-600">اذهب إلى <code class="bg-gray-200 px-2 py-1 rounded">invoices.php?action=add&type=sales</code></p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">2</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">ملء المعلومات الأساسية</h3>
                            <p class="text-gray-600">أدخل رقم الفاتورة، التاريخ، واختر العميل/المورد</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">3</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">إضافة العناصر</h3>
                            <p class="text-gray-600">استخدم نموذج "إضافة عنصر جديد" لإضافة كل عنصر على حدة</p>
                            <ul class="list-disc list-inside mt-2 text-sm text-gray-600">
                                <li>اسم العنصر (مطلوب)</li>
                                <li>الكمية (مطلوب)</li>
                                <li>سعر الوحدة (مطلوب)</li>
                                <li>الخصم (اختياري)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">4</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">مراجعة العناصر</h3>
                            <p class="text-gray-600">ستظهر العناصر المضافة في جدول، يمكنك حذف أي عنصر غير مرغوب فيه</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">5</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">إضافة الضريبة</h3>
                            <p class="text-gray-600">أدخل مبلغ الضريبة واضغط "تحديث الإجماليات" لرؤية الإجمالي النهائي</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">6</div>
                        <div>
                            <h3 class="font-semibold text-gray-800">حفظ الفاتورة</h3>
                            <p class="text-gray-600">اختر "حفظ كمسودة" أو "حفظ وترحيل" لإنهاء العملية</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الميزات -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">
                    <i class="fas fa-star mr-2"></i>
                    الميزات الرئيسية
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-800 mb-2">
                            <i class="fas fa-plus-circle mr-2"></i>
                            إضافة عناصر متعددة
                        </h3>
                        <p class="text-green-700 text-sm">يمكنك إضافة عدد غير محدود من العناصر لكل فاتورة</p>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-800 mb-2">
                            <i class="fas fa-calculator mr-2"></i>
                            حساب تلقائي للإجماليات
                        </h3>
                        <p class="text-blue-700 text-sm">يتم حساب الإجماليات تلقائياً بناءً على العناصر المضافة</p>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-yellow-800 mb-2">
                            <i class="fas fa-trash mr-2"></i>
                            إدارة العناصر
                        </h3>
                        <p class="text-yellow-700 text-sm">يمكنك حذف عناصر فردية أو مسح جميع العناصر</p>
                    </div>
                    
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-purple-800 mb-2">
                            <i class="fas fa-percent mr-2"></i>
                            دعم الخصومات والضرائب
                        </h3>
                        <p class="text-purple-700 text-sm">يمكن إضافة خصم لكل عنصر وضريبة إجمالية للفاتورة</p>
                    </div>
                </div>
            </div>

            <!-- ملاحظات مهمة -->
            <div class="mb-8">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    ملاحظات مهمة
                </h2>
                
                <div class="bg-yellow-50 border-r-4 border-yellow-400 p-4">
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-500 mt-1 mr-2"></i>
                            <span>العناصر يتم حفظها مؤقتاً في الجلسة حتى يتم حفظ الفاتورة نهائياً</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-500 mt-1 mr-2"></i>
                            <span>يجب إضافة عنصر واحد على الأقل قبل حفظ الفاتورة</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-500 mt-1 mr-2"></i>
                            <span>إذا تركت الصفحة بدون حفظ، ستفقد العناصر المضافة</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-500 mt-1 mr-2"></i>
                            <span>النظام لا يستخدم JavaScript ويعتمد على PHP فقط</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="text-center space-x-4 space-x-reverse">
                <a href="test_invoice_items.php" class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-flask mr-2"></i>
                    اختبار النظام
                </a>
                <a href="invoices.php?action=add&type=sales" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    إنشاء فاتورة مبيعات
                </a>
                <a href="invoices.php?action=add&type=purchase" class="inline-block bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    إنشاء فاتورة مشتريات
                </a>
                <a href="invoices.php" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-list mr-2"></i>
                    إدارة الفواتير
                </a>
            </div>
        </div>
    </div>
</body>
</html>

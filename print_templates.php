<?php
/**
 * صفحة إدارة قوالب الطباعة
 * Print Templates Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_settings');

// إنشاء مثيل نموذج قوالب الطباعة
$templateModel = new PrintTemplate();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$templateId = $_GET['id'] ?? null;
$message = '';
$messageType = '';

// معالجة إنشاء/تحديث قالب
if (($_SERVER['REQUEST_METHOD'] === 'POST') && in_array($action, ['create', 'edit'])) {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $templateData = [
            'template_name' => sanitizeInput($_POST['template_name'] ?? ''),
            'template_type' => sanitizeInput($_POST['template_type'] ?? ''),
            'template_content' => $_POST['template_content'] ?? '',
            'template_css' => $_POST['template_css'] ?? '',
            'paper_size' => sanitizeInput($_POST['paper_size'] ?? 'A4'),
            'orientation' => sanitizeInput($_POST['orientation'] ?? 'portrait'),
            'margins' => sanitizeInput($_POST['margins'] ?? '20mm'),
            'header_height' => sanitizeInput($_POST['header_height'] ?? '50mm'),
            'footer_height' => sanitizeInput($_POST['footer_height'] ?? '30mm'),
            'is_default' => isset($_POST['is_default']) ? 1 : 0,
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($action === 'create') {
            $result = $templateModel->createTemplate($templateData);
            if ($result) {
                $message = 'تم إنشاء القالب بنجاح';
                $messageType = 'success';
                $action = 'list';
            } else {
                $message = 'فشل في إنشاء القالب';
                $messageType = 'error';
            }
        } else {
            $result = $templateModel->updateTemplate($templateId, $templateData);
            if ($result) {
                $message = 'تم تحديث القالب بنجاح';
                $messageType = 'success';
                $action = 'list';
            } else {
                $message = 'فشل في تحديث القالب';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة حذف قالب
if ($action === 'delete' && $templateId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if ($templateModel->deleteTemplate($templateId)) {
            $message = 'تم حذف القالب بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في حذف القالب';
            $messageType = 'error';
        }
        $action = 'list';
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة تعيين قالب كافتراضي
if ($action === 'set_default' && $templateId) {
    if ($templateModel->setAsDefault($templateId)) {
        $message = 'تم تعيين القالب كافتراضي';
        $messageType = 'success';
    } else {
        $message = 'فشل في تعيين القالب كافتراضي';
        $messageType = 'error';
    }
    $action = 'list';
}

// الحصول على البيانات حسب العملية
$templates = [];
$currentTemplate = null;

if ($action === 'list') {
    $templates = $templateModel->getAllTemplates();
} elseif ($action === 'edit' && $templateId) {
    $currentTemplate = $templateModel->getTemplateById($templateId);
    if (!$currentTemplate) {
        $message = 'القالب غير موجود';
        $messageType = 'error';
        $action = 'list';
        $templates = $templateModel->getAllTemplates();
    }
}

$pageTitle = 'إدارة قوالب الطباعة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- CodeMirror للمحرر -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .CodeMirror { border: 1px solid #ddd; border-radius: 4px; height: 300px; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="settings.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للإعدادات</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-print ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            
            <!-- قائمة القوالب -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قوالب الطباعة (<?php echo count($templates); ?> قالب)
                    </h3>
                    <a href="?action=create" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة قالب جديد
                    </a>
                </div>
                
                <div class="overflow-x-auto">
                    <?php if (empty($templates)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-print text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد قوالب</h3>
                            <p class="text-gray-500 mb-4">لم يتم إنشاء أي قوالب طباعة بعد</p>
                            <a href="?action=create" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إنشاء أول قالب
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        اسم القالب
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        النوع
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        حجم الورق
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الاتجاه
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الحالة
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        تاريخ الإنشاء
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        إجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($templates as $template): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($template['template_name']); ?>
                                                        <?php if ($template['is_default']): ?>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                                                افتراضي
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        بواسطة: <?php echo htmlspecialchars($template['created_by_name'] ?? 'غير محدد'); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php
                                            $typeLabels = [
                                                'invoice' => 'فاتورة',
                                                'report' => 'تقرير',
                                                'receipt' => 'إيصال',
                                                'statement' => 'كشف حساب'
                                            ];
                                            echo $typeLabels[$template['template_type']] ?? $template['template_type'];
                                            ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($template['paper_size']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo $template['orientation'] === 'portrait' ? 'عمودي' : 'أفقي'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($template['is_active']): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo formatDateTime($template['created_at']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="?action=preview&id=<?php echo $template['template_id']; ?>" 
                                               class="text-purple-600 hover:text-purple-900 ml-3" title="معاينة">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="?action=edit&id=<?php echo $template['template_id']; ?>" 
                                               class="text-blue-600 hover:text-blue-900 ml-3" title="تحرير">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if (!$template['is_default']): ?>
                                                <a href="?action=set_default&id=<?php echo $template['template_id']; ?>" 
                                                   class="text-green-600 hover:text-green-900 ml-3" title="تعيين كافتراضي">
                                                    <i class="fas fa-star"></i>
                                                </a>
                                            <?php endif; ?>
                                            <form method="POST" action="?action=delete&id=<?php echo $template['template_id']; ?>" class="inline">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذا القالب؟')" 
                                                        class="text-red-600 hover:text-red-900" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif (in_array($action, ['create', 'edit'])): ?>

            <!-- نموذج إنشاء/تحرير قالب -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas <?php echo $action === 'create' ? 'fa-plus' : 'fa-edit'; ?> ml-2"></i>
                        <?php echo $action === 'create' ? 'إنشاء قالب جديد' : 'تحرير القالب'; ?>
                    </h3>
                </div>

                <div class="p-6">
                    <form method="POST" action="?action=<?php echo $action; ?><?php echo $templateId ? '&id=' . $templateId : ''; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <!-- معلومات أساسية -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">

                            <!-- اسم القالب -->
                            <div>
                                <label for="template_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    اسم القالب <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="template_name" name="template_name" required
                                       value="<?php echo htmlspecialchars($currentTemplate['template_name'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- نوع القالب -->
                            <div>
                                <label for="template_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    نوع القالب <span class="text-red-500">*</span>
                                </label>
                                <select id="template_type" name="template_type" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر نوع القالب</option>
                                    <option value="invoice" <?php echo ($currentTemplate['template_type'] ?? '') === 'invoice' ? 'selected' : ''; ?>>فاتورة</option>
                                    <option value="report" <?php echo ($currentTemplate['template_type'] ?? '') === 'report' ? 'selected' : ''; ?>>تقرير</option>
                                    <option value="receipt" <?php echo ($currentTemplate['template_type'] ?? '') === 'receipt' ? 'selected' : ''; ?>>إيصال</option>
                                    <option value="statement" <?php echo ($currentTemplate['template_type'] ?? '') === 'statement' ? 'selected' : ''; ?>>كشف حساب</option>
                                </select>
                            </div>

                            <!-- حجم الورق -->
                            <div>
                                <label for="paper_size" class="block text-sm font-medium text-gray-700 mb-2">
                                    حجم الورق
                                </label>
                                <select id="paper_size" name="paper_size"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="A4" <?php echo ($currentTemplate['paper_size'] ?? 'A4') === 'A4' ? 'selected' : ''; ?>>A4</option>
                                    <option value="A3" <?php echo ($currentTemplate['paper_size'] ?? '') === 'A3' ? 'selected' : ''; ?>>A3</option>
                                    <option value="A5" <?php echo ($currentTemplate['paper_size'] ?? '') === 'A5' ? 'selected' : ''; ?>>A5</option>
                                    <option value="Letter" <?php echo ($currentTemplate['paper_size'] ?? '') === 'Letter' ? 'selected' : ''; ?>>Letter</option>
                                    <option value="Legal" <?php echo ($currentTemplate['paper_size'] ?? '') === 'Legal' ? 'selected' : ''; ?>>Legal</option>
                                </select>
                            </div>

                            <!-- اتجاه الورق -->
                            <div>
                                <label for="orientation" class="block text-sm font-medium text-gray-700 mb-2">
                                    اتجاه الورق
                                </label>
                                <select id="orientation" name="orientation"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="portrait" <?php echo ($currentTemplate['orientation'] ?? 'portrait') === 'portrait' ? 'selected' : ''; ?>>عمودي</option>
                                    <option value="landscape" <?php echo ($currentTemplate['orientation'] ?? '') === 'landscape' ? 'selected' : ''; ?>>أفقي</option>
                                </select>
                            </div>

                        </div>

                        <!-- إعدادات الهوامش -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">

                            <!-- الهوامش -->
                            <div>
                                <label for="margins" class="block text-sm font-medium text-gray-700 mb-2">
                                    الهوامش
                                </label>
                                <input type="text" id="margins" name="margins"
                                       value="<?php echo htmlspecialchars($currentTemplate['margins'] ?? '20mm'); ?>"
                                       placeholder="20mm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- ارتفاع الرأس -->
                            <div>
                                <label for="header_height" class="block text-sm font-medium text-gray-700 mb-2">
                                    ارتفاع الرأس
                                </label>
                                <input type="text" id="header_height" name="header_height"
                                       value="<?php echo htmlspecialchars($currentTemplate['header_height'] ?? '50mm'); ?>"
                                       placeholder="50mm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- ارتفاع التذييل -->
                            <div>
                                <label for="footer_height" class="block text-sm font-medium text-gray-700 mb-2">
                                    ارتفاع التذييل
                                </label>
                                <input type="text" id="footer_height" name="footer_height"
                                       value="<?php echo htmlspecialchars($currentTemplate['footer_height'] ?? '30mm'); ?>"
                                       placeholder="30mm"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                        </div>

                        <!-- محتوى القالب -->
                        <div class="mb-6">
                            <label for="template_content" class="block text-sm font-medium text-gray-700 mb-2">
                                محتوى القالب (HTML) <span class="text-red-500">*</span>
                            </label>
                            <textarea id="template_content" name="template_content" required
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($currentTemplate['template_content'] ?? ''); ?></textarea>
                        </div>

                        <!-- تنسيق CSS -->
                        <div class="mb-6">
                            <label for="template_css" class="block text-sm font-medium text-gray-700 mb-2">
                                تنسيق CSS
                            </label>
                            <textarea id="template_css" name="template_css"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($currentTemplate['template_css'] ?? ''); ?></textarea>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="mb-6 space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_default" name="is_default" value="1"
                                       <?php echo !empty($currentTemplate['is_default']) ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_default" class="mr-2 block text-sm text-gray-900">
                                    تعيين كقالب افتراضي
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" value="1"
                                       <?php echo ($currentTemplate['is_active'] ?? 1) ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    قالب نشط
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="flex justify-between">
                            <a href="?action=list" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء
                            </a>
                            <div class="space-x-2 space-x-reverse">
                                <button type="button" onclick="previewTemplate()" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-eye ml-2"></i>
                                    معاينة
                                </button>
                                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-save ml-2"></i>
                                    <?php echo $action === 'create' ? 'إنشاء القالب' : 'حفظ التغييرات'; ?>
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>

        <?php endif; ?>

    </div>

    <!-- JavaScript -->
    <script>
        let htmlEditor, cssEditor;

        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة محرر HTML
            if (document.getElementById('template_content')) {
                htmlEditor = CodeMirror.fromTextArea(document.getElementById('template_content'), {
                    mode: 'xml',
                    lineNumbers: true,
                    theme: 'default',
                    lineWrapping: true,
                    autoCloseTags: true
                });
            }

            // تهيئة محرر CSS
            if (document.getElementById('template_css')) {
                cssEditor = CodeMirror.fromTextArea(document.getElementById('template_css'), {
                    mode: 'css',
                    lineNumbers: true,
                    theme: 'default',
                    lineWrapping: true,
                    autoCloseBrackets: true
                });
            }
        });

        // معاينة القالب
        function previewTemplate() {
            if (htmlEditor && cssEditor) {
                const htmlContent = htmlEditor.getValue();
                const cssContent = cssEditor.getValue();

                const previewWindow = window.open('', '_blank', 'width=800,height=600');
                previewWindow.document.write(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>معاينة القالب</title>
                        <style>
                            ${cssContent}
                        </style>
                    </head>
                    <body>
                        ${htmlContent}
                    </body>
                    </html>
                `);
                previewWindow.document.close();
            }
        }

        // تحديث محررات النص عند إرسال النموذج
        document.querySelector('form').addEventListener('submit', function() {
            if (htmlEditor) {
                htmlEditor.save();
            }
            if (cssEditor) {
                cssEditor.save();
            }
        });
    </script>

</body>
</html>

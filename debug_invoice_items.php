<?php
/**
 * تشخيص مشكلة إضافة عناصر الفاتورة
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>تشخيص مشكلة إضافة عناصر الفاتورة</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; margin: 10px 0; }
    .debug-box { border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9; }
    .btn { display: inline-block; padding: 8px 16px; margin: 4px; text-decoration: none; border-radius: 4px; color: white; }
    .btn-primary { background-color: #007bff; }
    .btn-success { background-color: #28a745; }
    .btn-danger { background-color: #dc3545; }
</style>";

// معالجة اختبار إضافة عنصر
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_add_item'])) {
    echo "<div class='debug-box'>";
    echo "<h3>نتائج اختبار إضافة العنصر:</h3>";
    
    // إنشاء الجلسة إذا لم تكن موجودة
    if (!isset($_SESSION['invoice_items'])) {
        $_SESSION['invoice_items'] = [];
        echo "<p class='info'>تم إنشاء مصفوفة العناصر في الجلسة</p>";
    }
    
    // محاولة إضافة عنصر تجريبي
    $testItem = [
        'item_name' => $_POST['item_name'] ?? 'عنصر تجريبي',
        'quantity' => floatval($_POST['quantity'] ?? 1),
        'unit_price' => floatval($_POST['unit_price'] ?? 100),
        'discount_amount' => floatval($_POST['discount_amount'] ?? 0)
    ];
    
    echo "<p class='info'>البيانات المستلمة:</p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<p class='info'>العنصر المعالج:</p>";
    echo "<pre>" . print_r($testItem, true) . "</pre>";
    
    // التحقق من صحة البيانات
    if (!empty($testItem['item_name']) && $testItem['quantity'] > 0 && $testItem['unit_price'] >= 0) {
        $testItem['total_price'] = ($testItem['quantity'] * $testItem['unit_price']) - $testItem['discount_amount'];
        $_SESSION['invoice_items'][] = $testItem;
        echo "<p class='success'>✅ تم إضافة العنصر بنجاح!</p>";
    } else {
        echo "<p class='error'>❌ فشل في إضافة العنصر - بيانات غير صحيحة</p>";
        echo "<p>الشروط:</p>";
        echo "<ul>";
        echo "<li>اسم العنصر: " . (!empty($testItem['item_name']) ? '✅' : '❌') . "</li>";
        echo "<li>الكمية > 0: " . ($testItem['quantity'] > 0 ? '✅' : '❌') . "</li>";
        echo "<li>سعر الوحدة >= 0: " . ($testItem['unit_price'] >= 0 ? '✅' : '❌') . "</li>";
        echo "</ul>";
    }
    echo "</div>";
}

// معالجة مسح العناصر
if (isset($_POST['clear_test_items'])) {
    $_SESSION['invoice_items'] = [];
    echo "<p class='info'>تم مسح جميع العناصر التجريبية</p>";
}

try {
    echo "<h2>1. فحص حالة الجلسة:</h2>";
    echo "<div class='debug-box'>";
    
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p class='success'>✅ الجلسة نشطة</p>";
        echo "<p>معرف الجلسة: " . session_id() . "</p>";
    } else {
        echo "<p class='error'>❌ الجلسة غير نشطة</p>";
    }
    
    echo "<p>العناصر في الجلسة:</p>";
    $sessionItems = $_SESSION['invoice_items'] ?? [];
    echo "<pre>" . print_r($sessionItems, true) . "</pre>";
    echo "<p>عدد العناصر: " . count($sessionItems) . "</p>";
    echo "</div>";
    
    echo "<h2>2. اختبار إضافة عنصر:</h2>";
    echo "<div class='debug-box'>";
    echo "<form method='POST'>";
    echo "<table>";
    echo "<tr>";
    echo "<td><label>اسم العنصر:</label></td>";
    echo "<td><input type='text' name='item_name' value='خدمة اختبار' required></td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><label>الكمية:</label></td>";
    echo "<td><input type='number' name='quantity' value='2' step='0.01' min='0.01' required></td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><label>سعر الوحدة:</label></td>";
    echo "<td><input type='number' name='unit_price' value='150' step='0.01' min='0' required></td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td><label>الخصم:</label></td>";
    echo "<td><input type='number' name='discount_amount' value='10' step='0.01' min='0'></td>";
    echo "</tr>";
    echo "<tr>";
    echo "<td colspan='2'>";
    echo "<button type='submit' name='test_add_item' class='btn btn-success'>اختبار إضافة العنصر</button>";
    echo "<button type='submit' name='clear_test_items' class='btn btn-danger'>مسح العناصر</button>";
    echo "</td>";
    echo "</tr>";
    echo "</table>";
    echo "</form>";
    echo "</div>";
    
    echo "<h2>3. فحص معالجة النموذج في invoices.php:</h2>";
    echo "<div class='debug-box'>";
    
    // محاكاة معالجة النموذج
    $action = 'add';
    $mockPost = [
        'manage_items' => '1',
        'add_item' => '1',
        'item_name' => 'عنصر محاكاة',
        'quantity' => '1',
        'unit_price' => '200',
        'discount_amount' => '0'
    ];
    
    echo "<p class='info'>محاكاة معالجة النموذج:</p>";
    echo "<pre>";
    echo "if (\$action === 'add' && isset(\$_POST['manage_items'])) {\n";
    echo "    // الشرط: " . ($action === 'add' && isset($mockPost['manage_items']) ? '✅ صحيح' : '❌ خاطئ') . "\n";
    echo "    if (isset(\$_POST['add_item'])) {\n";
    echo "        // الشرط: " . (isset($mockPost['add_item']) ? '✅ صحيح' : '❌ خاطئ') . "\n";
    echo "        // معالجة البيانات...\n";
    echo "    }\n";
    echo "}\n";
    echo "</pre>";
    echo "</div>";
    
    echo "<h2>4. فحص دوال الأمان:</h2>";
    echo "<div class='debug-box'>";
    
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        echo "<p class='success'>✅ دالة generateCSRFToken تعمل</p>";
        echo "<p>Token: " . substr($token, 0, 20) . "...</p>";
    } else {
        echo "<p class='error'>❌ دالة generateCSRFToken غير موجودة</p>";
    }
    
    if (function_exists('verifyCSRFToken')) {
        echo "<p class='success'>✅ دالة verifyCSRFToken موجودة</p>";
    } else {
        echo "<p class='error'>❌ دالة verifyCSRFToken غير موجودة</p>";
    }
    
    if (function_exists('setAlert')) {
        echo "<p class='success'>✅ دالة setAlert موجودة</p>";
    } else {
        echo "<p class='error'>❌ دالة setAlert غير موجودة</p>";
    }
    
    if (function_exists('redirect')) {
        echo "<p class='success'>✅ دالة redirect موجودة</p>";
    } else {
        echo "<p class='error'>❌ دالة redirect غير موجودة</p>";
    }
    echo "</div>";
    
    echo "<h2>5. اختبار الرابط المباشر:</h2>";
    echo "<div class='debug-box'>";
    echo "<p>جرب الروابط التالية:</p>";
    echo "<p><a href='invoices.php?action=add&type=sales' class='btn btn-primary' target='_blank'>فتح نموذج الفاتورة</a></p>";
    echo "<p><a href='invoices.php?action=add&type=sales&debug=1' class='btn btn-primary' target='_blank'>فتح نموذج الفاتورة مع التشخيص</a></p>";
    echo "</div>";
    
    if (!empty($sessionItems)) {
        echo "<h2>6. العناصر المحفوظة حالياً:</h2>";
        echo "<div class='debug-box'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>اسم العنصر</th><th>الكمية</th><th>سعر الوحدة</th><th>الخصم</th><th>الإجمالي</th>";
        echo "</tr>";
        
        $grandTotal = 0;
        foreach ($sessionItems as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['item_name']) . "</td>";
            echo "<td>" . number_format($item['quantity'], 2) . "</td>";
            echo "<td>" . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>" . number_format($item['discount_amount'], 2) . "</td>";
            echo "<td>" . number_format($item['total_price'], 2) . "</td>";
            echo "</tr>";
            $grandTotal += $item['total_price'];
        }
        
        echo "<tr style='background: #e0e0e0; font-weight: bold;'>";
        echo "<td colspan='4'>الإجمالي</td>";
        echo "<td>" . number_format($grandTotal, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li>تأكد من أن الجلسة تعمل بشكل صحيح</li>";
echo "<li>اختبر إضافة عنصر باستخدام النموذج أعلاه</li>";
echo "<li>افتح نموذج الفاتورة الأصلي وجرب إضافة عنصر</li>";
echo "<li>تحقق من ظهور رسائل النجاح/الخطأ</li>";
echo "<li>تأكد من ظهور العناصر في الجدول</li>";
echo "</ol>";
?>

<?php
/**
 * نموذج فئات الأصناف
 * Category Model
 */

class Category {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الفئات
     */
    public function getAllCategories($activeOnly = true) {
        try {
            $query = "SELECT 
                        c.category_id, c.category_code, c.category_name, 
                        c.parent_category_id, c.description, c.is_active,
                        c.created_at, parent.category_name as parent_category_name
                      FROM item_categories c
                      LEFT JOIN item_categories parent ON c.parent_category_id = parent.category_id";
            
            if ($activeOnly) {
                $query .= " WHERE c.is_active = 1";
            }
            
            $query .= " ORDER BY c.category_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all categories error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على فئة بالمعرف
     */
    public function getCategoryById($categoryId) {
        try {
            $query = "SELECT 
                        c.*, parent.category_name as parent_category_name
                      FROM item_categories c
                      LEFT JOIN item_categories parent ON c.parent_category_id = parent.category_id
                      WHERE c.category_id = ?";
            
            return $this->db->selectOne($query, [$categoryId]);
            
        } catch (Exception $e) {
            error_log("Get category by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء فئة جديدة
     */
    public function createCategory($data) {
        try {
            // التحقق من عدم وجود كود الفئة
            if ($this->categoryCodeExists($data['category_code'])) {
                throw new Exception('كود الفئة موجود بالفعل');
            }
            
            $query = "INSERT INTO item_categories (
                        category_code, category_name, parent_category_id, 
                        description, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['category_code'],
                $data['category_name'],
                $data['parent_category_id'] ?? null,
                $data['description'] ?? null,
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $categoryId = $this->db->insert($query, $params);
            
            if ($categoryId) {
                logActivity('إنشاء فئة', "تم إنشاء الفئة: {$data['category_name']}");
            }
            
            return $categoryId;
            
        } catch (Exception $e) {
            error_log("Create category error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الفئة
     */
    public function updateCategory($categoryId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['category_name'])) {
                $setParts[] = "category_name = ?";
                $params[] = $data['category_name'];
            }
            
            if (isset($data['parent_category_id'])) {
                $setParts[] = "parent_category_id = ?";
                $params[] = $data['parent_category_id'];
            }
            
            if (isset($data['description'])) {
                $setParts[] = "description = ?";
                $params[] = $data['description'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $categoryId;
            
            $query = "UPDATE item_categories SET " . implode(', ', $setParts) . " WHERE category_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث فئة', "تم تحديث الفئة ID: {$categoryId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update category error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الفئة (إلغاء تفعيل)
     */
    public function deleteCategory($categoryId) {
        try {
            // التحقق من عدم وجود أصناف مرتبطة
            $itemsCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM items WHERE category_id = ? AND is_active = 1",
                [$categoryId]
            );
            
            if ($itemsCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الفئة لوجود أصناف مرتبطة بها');
            }
            
            $query = "UPDATE item_categories SET is_active = 0, updated_at = NOW() WHERE category_id = ?";
            $result = $this->db->update($query, [$categoryId]);
            
            if ($result) {
                logActivity('حذف فئة', "تم حذف الفئة ID: {$categoryId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete category error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود الفئة
     */
    private function categoryCodeExists($categoryCode, $excludeCategoryId = null) {
        $query = "SELECT COUNT(*) as count FROM item_categories WHERE category_code = ?";
        $params = [$categoryCode];
        
        if ($excludeCategoryId) {
            $query .= " AND category_id != ?";
            $params[] = $excludeCategoryId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على الفئات الرئيسية فقط
     */
    public function getMainCategories($activeOnly = true) {
        try {
            $query = "SELECT category_id, category_code, category_name, description
                      FROM item_categories 
                      WHERE parent_category_id IS NULL";
            
            if ($activeOnly) {
                $query .= " AND is_active = 1";
            }
            
            $query .= " ORDER BY category_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get main categories error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الفئات الفرعية
     */
    public function getSubCategories($parentCategoryId, $activeOnly = true) {
        try {
            $query = "SELECT category_id, category_code, category_name, description
                      FROM item_categories 
                      WHERE parent_category_id = ?";
            
            $params = [$parentCategoryId];
            
            if ($activeOnly) {
                $query .= " AND is_active = 1";
            }
            
            $query .= " ORDER BY category_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get sub categories error: " . $e->getMessage());
            return [];
        }
    }
}

?>

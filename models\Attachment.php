<?php
/**
 * نموذج المرفقات
 * Attachments Model
 */

class Attachment {
    private $db;
    
    // أنواع الملفات المسموحة
    private $allowedTypes = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
        'archive' => ['zip', 'rar', '7z'],
        'other' => []
    ];
    
    // الحد الأقصى لحجم الملف (بالبايت) - 10MB
    private $maxFileSize = 10485760;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * رفع ملف جديد
     */
    public function uploadFile($fileData, $entityType, $entityId, $description = '') {
        try {
            // التحقق من صحة الملف
            $validation = $this->validateFile($fileData);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            // إنشاء مجلد الرفع إذا لم يكن موجوداً
            $uploadDir = $this->createUploadDirectory($entityType);
            if (!$uploadDir) {
                return ['success' => false, 'message' => 'فشل في إنشاء مجلد الرفع'];
            }
            
            // إنشاء اسم ملف فريد
            $fileExtension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
            $fileName = $this->generateUniqueFileName($fileExtension);
            $filePath = $uploadDir . '/' . $fileName;
            
            // نقل الملف إلى المجلد المحدد
            if (!move_uploaded_file($fileData['tmp_name'], $filePath)) {
                return ['success' => false, 'message' => 'فشل في رفع الملف'];
            }
            
            // حفظ معلومات الملف في قاعدة البيانات
            $attachmentId = $this->saveFileInfo([
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'original_name' => $fileData['name'],
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_size' => $fileData['size'],
                'file_type' => $this->getFileType($fileExtension),
                'mime_type' => $fileData['type'],
                'description' => $description
            ]);
            
            if ($attachmentId) {
                logActivity('رفع مرفق', "تم رفع ملف: {$fileData['name']} للكيان: {$entityType} #{$entityId}");
                return [
                    'success' => true, 
                    'message' => 'تم رفع الملف بنجاح',
                    'attachment_id' => $attachmentId,
                    'file_name' => $fileName
                ];
            } else {
                // حذف الملف في حالة فشل حفظ البيانات
                unlink($filePath);
                return ['success' => false, 'message' => 'فشل في حفظ معلومات الملف'];
            }
            
        } catch (Exception $e) {
            error_log("Upload file error: " . $e->getMessage());
            return ['success' => false, 'message' => 'خطأ في رفع الملف'];
        }
    }
    
    /**
     * الحصول على مرفقات كيان معين
     */
    public function getEntityAttachments($entityType, $entityId) {
        try {
            $query = "SELECT a.*, u.full_name as uploaded_by_name
                     FROM attachments a
                     LEFT JOIN users u ON a.uploaded_by = u.user_id
                     WHERE a.entity_type = ? AND a.entity_id = ?
                     ORDER BY a.created_at DESC";
            
            return $this->db->select($query, [$entityType, $entityId]);
            
        } catch (Exception $e) {
            error_log("Get entity attachments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مرفق بالمعرف
     */
    public function getAttachmentById($attachmentId) {
        try {
            $query = "SELECT a.*, u.full_name as uploaded_by_name
                     FROM attachments a
                     LEFT JOIN users u ON a.uploaded_by = u.user_id
                     WHERE a.attachment_id = ?";
            
            return $this->db->selectOne($query, [$attachmentId]);
            
        } catch (Exception $e) {
            error_log("Get attachment by ID error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * تحديث وصف المرفق
     */
    public function updateDescription($attachmentId, $description) {
        try {
            $query = "UPDATE attachments SET description = ?, updated_at = NOW() WHERE attachment_id = ?";
            $result = $this->db->update($query, [$description, $attachmentId]);
            
            if ($result) {
                logActivity('تحديث مرفق', "تم تحديث وصف المرفق #{$attachmentId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update attachment description error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف مرفق
     */
    public function deleteAttachment($attachmentId) {
        try {
            // الحصول على معلومات الملف
            $attachment = $this->getAttachmentById($attachmentId);
            if (!$attachment) {
                return false;
            }
            
            // حذف الملف من النظام
            if (file_exists($attachment['file_path'])) {
                unlink($attachment['file_path']);
            }
            
            // حذف السجل من قاعدة البيانات
            $query = "DELETE FROM attachments WHERE attachment_id = ?";
            $result = $this->db->delete($query, [$attachmentId]);
            
            if ($result) {
                logActivity('حذف مرفق', "تم حذف المرفق: {$attachment['original_name']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete attachment error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات المرفقات
     */
    public function getAttachmentsStats() {
        try {
            $query = "SELECT 
                        COUNT(*) as total_attachments,
                        SUM(file_size) as total_size,
                        COUNT(CASE WHEN file_type = 'image' THEN 1 END) as images_count,
                        COUNT(CASE WHEN file_type = 'document' THEN 1 END) as documents_count,
                        COUNT(CASE WHEN file_type = 'archive' THEN 1 END) as archives_count,
                        COUNT(CASE WHEN file_type = 'other' THEN 1 END) as others_count
                      FROM attachments";
            
            return $this->db->selectOne($query) ?: [];
            
        } catch (Exception $e) {
            error_log("Get attachments stats error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * البحث في المرفقات
     */
    public function searchAttachments($searchTerm, $entityType = null, $fileType = null, $limit = 50) {
        try {
            $conditions = [];
            $params = [];
            
            // البحث في اسم الملف أو الوصف
            $conditions[] = "(a.original_name LIKE ? OR a.description LIKE ?)";
            $params[] = "%{$searchTerm}%";
            $params[] = "%{$searchTerm}%";
            
            // فلترة حسب نوع الكيان
            if ($entityType) {
                $conditions[] = "a.entity_type = ?";
                $params[] = $entityType;
            }
            
            // فلترة حسب نوع الملف
            if ($fileType) {
                $conditions[] = "a.file_type = ?";
                $params[] = $fileType;
            }
            
            $whereClause = implode(' AND ', $conditions);
            
            $query = "SELECT a.*, u.full_name as uploaded_by_name
                     FROM attachments a
                     LEFT JOIN users u ON a.uploaded_by = u.user_id
                     WHERE {$whereClause}
                     ORDER BY a.created_at DESC
                     LIMIT {$limit}";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search attachments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * التحقق من صحة الملف
     */
    private function validateFile($fileData) {
        // التحقق من وجود خطأ في الرفع
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            return ['valid' => false, 'message' => 'خطأ في رفع الملف'];
        }
        
        // التحقق من حجم الملف
        if ($fileData['size'] > $this->maxFileSize) {
            $maxSizeMB = $this->maxFileSize / 1048576;
            return ['valid' => false, 'message' => "حجم الملف يجب أن يكون أقل من {$maxSizeMB} ميجابايت"];
        }
        
        // التحقق من نوع الملف
        $fileExtension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
        if (!$this->isAllowedFileType($fileExtension)) {
            return ['valid' => false, 'message' => 'نوع الملف غير مسموح'];
        }
        
        return ['valid' => true, 'message' => 'الملف صالح'];
    }
    
    /**
     * التحقق من أن نوع الملف مسموح
     */
    private function isAllowedFileType($extension) {
        foreach ($this->allowedTypes as $type => $extensions) {
            if (in_array($extension, $extensions)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * الحصول على نوع الملف
     */
    private function getFileType($extension) {
        foreach ($this->allowedTypes as $type => $extensions) {
            if (in_array($extension, $extensions)) {
                return $type;
            }
        }
        return 'other';
    }
    
    /**
     * إنشاء مجلد الرفع
     */
    private function createUploadDirectory($entityType) {
        $baseDir = 'uploads';
        $entityDir = $baseDir . '/' . $entityType;
        $yearMonth = date('Y/m');
        $fullDir = $entityDir . '/' . $yearMonth;
        
        if (!is_dir($fullDir)) {
            if (!mkdir($fullDir, 0755, true)) {
                return false;
            }
        }
        
        return $fullDir;
    }
    
    /**
     * إنشاء اسم ملف فريد
     */
    private function generateUniqueFileName($extension) {
        return uniqid() . '_' . time() . '.' . $extension;
    }
    
    /**
     * حفظ معلومات الملف في قاعدة البيانات
     */
    private function saveFileInfo($data) {
        try {
            $query = "INSERT INTO attachments (
                        entity_type, entity_id, original_name, file_name, 
                        file_path, file_size, file_type, mime_type, 
                        description, uploaded_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['entity_type'],
                $data['entity_id'],
                $data['original_name'],
                $data['file_name'],
                $data['file_path'],
                $data['file_size'],
                $data['file_type'],
                $data['mime_type'],
                $data['description'],
                $_SESSION['user_id']
            ];
            
            return $this->db->insert($query, $params);
            
        } catch (Exception $e) {
            error_log("Save file info error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنسيق حجم الملف
     */
    public function formatFileSize($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
    
    /**
     * الحصول على أيقونة الملف
     */
    public function getFileIcon($fileType, $fileName = '') {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        switch ($fileType) {
            case 'image':
                return 'fas fa-image text-green-500';
            case 'document':
                if (in_array($extension, ['pdf'])) {
                    return 'fas fa-file-pdf text-red-500';
                } elseif (in_array($extension, ['doc', 'docx'])) {
                    return 'fas fa-file-word text-blue-500';
                } elseif (in_array($extension, ['xls', 'xlsx'])) {
                    return 'fas fa-file-excel text-green-600';
                } else {
                    return 'fas fa-file-alt text-gray-500';
                }
            case 'archive':
                return 'fas fa-file-archive text-orange-500';
            default:
                return 'fas fa-file text-gray-400';
        }
    }
}

?>

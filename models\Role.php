<?php
/**
 * نموذج الأدوار والصلاحيات
 * Roles and Permissions Model
 */

class Role {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الأدوار
     */
    public function getAllRoles($activeOnly = true) {
        try {
            $query = "SELECT 
                        r.role_id, r.role_name, r.role_description, r.is_active,
                        r.created_at, r.updated_at,
                        COUNT(u.user_id) as users_count
                      FROM roles r
                      LEFT JOIN users u ON r.role_id = u.role_id AND u.is_active = 1";
            
            $params = [];
            
            if ($activeOnly) {
                $query .= " WHERE r.is_active = 1";
            }
            
            $query .= " GROUP BY r.role_id, r.role_name, r.role_description, r.is_active, r.created_at, r.updated_at
                       ORDER BY r.role_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all roles error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على دور بالمعرف
     */
    public function getRoleById($roleId) {
        try {
            $query = "SELECT * FROM roles WHERE role_id = ?";
            return $this->db->selectOne($query, [$roleId]);
            
        } catch (Exception $e) {
            error_log("Get role by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء دور جديد
     */
    public function createRole($data) {
        try {
            // التحقق من عدم وجود اسم الدور
            if ($this->roleNameExists($data['role_name'])) {
                throw new Exception('اسم الدور موجود بالفعل');
            }
            
            $query = "INSERT INTO roles (
                        role_name, role_description, is_active, created_at
                      ) VALUES (?, ?, ?, NOW())";
            
            $params = [
                $data['role_name'],
                $data['role_description'] ?? null,
                $data['is_active'] ?? 1
            ];
            
            $roleId = $this->db->insert($query, $params);
            
            if ($roleId) {
                logActivity('إنشاء دور', "تم إنشاء الدور: {$data['role_name']}");
            }
            
            return $roleId;
            
        } catch (Exception $e) {
            error_log("Create role error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الدور
     */
    public function updateRole($roleId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['role_name'])) {
                // التحقق من عدم وجود اسم الدور
                if ($this->roleNameExists($data['role_name'], $roleId)) {
                    throw new Exception('اسم الدور موجود بالفعل');
                }
                $setParts[] = "role_name = ?";
                $params[] = $data['role_name'];
            }
            
            if (isset($data['role_description'])) {
                $setParts[] = "role_description = ?";
                $params[] = $data['role_description'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $roleId;
            
            $query = "UPDATE roles SET " . implode(', ', $setParts) . " WHERE role_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث دور', "تم تحديث الدور ID: {$roleId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update role error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الدور (إلغاء تفعيل)
     */
    public function deleteRole($roleId) {
        try {
            // التحقق من عدم وجود مستخدمين مرتبطين
            $usersCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM users WHERE role_id = ? AND is_active = 1",
                [$roleId]
            );
            
            if ($usersCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الدور لوجود مستخدمين مرتبطين به');
            }
            
            $query = "UPDATE roles SET is_active = 0, updated_at = NOW() WHERE role_id = ?";
            $result = $this->db->update($query, [$roleId]);
            
            if ($result) {
                logActivity('حذف دور', "تم حذف الدور ID: {$roleId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete role error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود اسم الدور
     */
    private function roleNameExists($roleName, $excludeRoleId = null) {
        $query = "SELECT COUNT(*) as count FROM roles WHERE role_name = ?";
        $params = [$roleName];
        
        if ($excludeRoleId) {
            $query .= " AND role_id != ?";
            $params[] = $excludeRoleId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على صلاحيات الدور
     */
    public function getRolePermissions($roleId) {
        try {
            $query = "SELECT permission_name FROM role_permissions WHERE role_id = ?";
            $permissions = $this->db->select($query, [$roleId]);
            
            return array_column($permissions, 'permission_name');
            
        } catch (Exception $e) {
            error_log("Get role permissions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث صلاحيات الدور
     */
    public function updateRolePermissions($roleId, $permissions) {
        try {
            $this->db->beginTransaction();
            
            // حذف الصلاحيات الحالية
            $this->db->delete("DELETE FROM role_permissions WHERE role_id = ?", [$roleId]);
            
            // إضافة الصلاحيات الجديدة
            if (!empty($permissions)) {
                $query = "INSERT INTO role_permissions (role_id, permission_name) VALUES (?, ?)";
                foreach ($permissions as $permission) {
                    $this->db->insert($query, [$roleId, $permission]);
                }
            }
            
            $this->db->commit();
            
            logActivity('تحديث صلاحيات دور', "تم تحديث صلاحيات الدور ID: {$roleId}");
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Update role permissions error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على جميع الصلاحيات المتاحة
     */
    public function getAllPermissions() {
        return [
            // إدارة المستخدمين
            'users_view' => 'عرض المستخدمين',
            'users_manage' => 'إدارة المستخدمين',
            'roles_manage' => 'إدارة الأدوار والصلاحيات',
            
            // إدارة الأصناف والمخازن
            'items_view' => 'عرض الأصناف',
            'items_manage' => 'إدارة الأصناف',
            'categories_manage' => 'إدارة الفئات',
            'units_manage' => 'إدارة الوحدات',
            'warehouses_manage' => 'إدارة المخازن',
            'inventory_view' => 'عرض المخزون',
            'inventory_manage' => 'إدارة المخزون',
            
            // إدارة العملاء والموردين
            'customers_view' => 'عرض العملاء',
            'customers_manage' => 'إدارة العملاء والموردين',
            
            // إدارة الفواتير
            'invoices_view' => 'عرض الفواتير',
            'invoices_manage' => 'إدارة الفواتير',
            'invoices_post' => 'ترحيل الفواتير',
            
            // النظام المحاسبي
            'accounts_view' => 'عرض الحسابات',
            'accounts_manage' => 'إدارة الحسابات',
            'transactions_view' => 'عرض القيود المحاسبية',
            'transactions_manage' => 'إدارة القيود المحاسبية',
            'transactions_post' => 'ترحيل القيود المحاسبية',
            
            // التقارير
            'reports_view' => 'عرض التقارير',
            'reports_financial' => 'التقارير المالية',
            'reports_inventory' => 'تقارير المخزون',
            'reports_sales' => 'تقارير المبيعات',
            
            // إدارة النظام
            'system_settings' => 'إعدادات النظام',
            'system_backup' => 'النسخ الاحتياطي',
            'activity_log' => 'سجل الأنشطة',
            'branches_manage' => 'إدارة الفروع'
        ];
    }
    
    /**
     * تجميع الصلاحيات حسب الفئة
     */
    public function getPermissionsByCategory() {
        $allPermissions = $this->getAllPermissions();
        
        $categories = [
            'المستخدمين والأدوار' => [
                'users_view', 'users_manage', 'roles_manage'
            ],
            'الأصناف والمخازن' => [
                'items_view', 'items_manage', 'categories_manage', 'units_manage',
                'warehouses_manage', 'inventory_view', 'inventory_manage'
            ],
            'العملاء والموردين' => [
                'customers_view', 'customers_manage'
            ],
            'الفواتير' => [
                'invoices_view', 'invoices_manage', 'invoices_post'
            ],
            'النظام المحاسبي' => [
                'accounts_view', 'accounts_manage', 'transactions_view',
                'transactions_manage', 'transactions_post'
            ],
            'التقارير' => [
                'reports_view', 'reports_financial', 'reports_inventory', 'reports_sales'
            ],
            'إدارة النظام' => [
                'system_settings', 'system_backup', 'activity_log', 'branches_manage'
            ]
        ];
        
        $result = [];
        foreach ($categories as $categoryName => $permissions) {
            $result[$categoryName] = [];
            foreach ($permissions as $permission) {
                if (isset($allPermissions[$permission])) {
                    $result[$categoryName][$permission] = $allPermissions[$permission];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * الحصول على إحصائيات الأدوار
     */
    public function getRolesStats() {
        try {
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_roles,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_roles,
                    COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_roles
                FROM roles
            ");
            
            $userStats = $this->db->selectOne("
                SELECT 
                    COUNT(DISTINCT u.user_id) as total_users,
                    COUNT(DISTINCT r.role_id) as roles_with_users
                FROM users u
                JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = 1 AND r.is_active = 1
            ");
            
            return array_merge(
                $stats ?: [
                    'total_roles' => 0,
                    'active_roles' => 0,
                    'inactive_roles' => 0
                ],
                $userStats ?: [
                    'total_users' => 0,
                    'roles_with_users' => 0
                ]
            );
            
        } catch (Exception $e) {
            error_log("Get roles stats error: " . $e->getMessage());
            return [
                'total_roles' => 0,
                'active_roles' => 0,
                'inactive_roles' => 0,
                'total_users' => 0,
                'roles_with_users' => 0
            ];
        }
    }
}

?>

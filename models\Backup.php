<?php
/**
 * نموذج النسخ الاحتياطي
 * Backup Model
 */

class Backup {
    private $db;
    private $backupDir;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->backupDir = 'backups/';
        
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من قاعدة البيانات
     */
    public function createDatabaseBackup($description = null) {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "database_backup_{$timestamp}.sql";
            $filepath = $this->backupDir . $filename;
            
            // الحصول على معلومات قاعدة البيانات
            $host = DB_HOST;
            $username = DB_USER;
            $password = DB_PASS;
            $database = DB_NAME;
            
            // إنشاء النسخة الاحتياطية باستخدام mysqldump
            $command = "mysqldump --host={$host} --user={$username} --password={$password} --single-transaction --routines --triggers {$database} > {$filepath}";
            
            // تنفيذ الأمر
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($filepath)) {
                // ضغط الملف
                $compressedFile = $filepath . '.gz';
                if (function_exists('gzencode')) {
                    $data = file_get_contents($filepath);
                    file_put_contents($compressedFile, gzencode($data, 9));
                    unlink($filepath); // حذف الملف غير المضغوط
                    $filepath = $compressedFile;
                    $filename = $filename . '.gz';
                }
                
                $fileSize = filesize($filepath);
                
                // تسجيل النسخة الاحتياطية في قاعدة البيانات
                $backupId = $this->logBackup([
                    'backup_type' => 'database',
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize,
                    'description' => $description,
                    'status' => 'completed'
                ]);
                
                logActivity('إنشاء نسخة احتياطية', "تم إنشاء نسخة احتياطية من قاعدة البيانات: {$filename}");
                
                return [
                    'success' => true,
                    'backup_id' => $backupId,
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize
                ];
            } else {
                throw new Exception('فشل في إنشاء النسخة الاحتياطية');
            }
            
        } catch (Exception $e) {
            error_log("Database backup error: " . $e->getMessage());
            
            // تسجيل الفشل
            $this->logBackup([
                'backup_type' => 'database',
                'filename' => $filename ?? null,
                'description' => $description,
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من الملفات
     */
    public function createFilesBackup($description = null) {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "files_backup_{$timestamp}.zip";
            $filepath = $this->backupDir . $filename;
            
            // إنشاء ملف ZIP
            $zip = new ZipArchive();
            if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
                throw new Exception('فشل في إنشاء ملف ZIP');
            }
            
            // إضافة الملفات المهمة
            $filesToBackup = [
                'uploads/',
                'includes/config.php',
                'includes/functions.php',
                '.htaccess'
            ];
            
            foreach ($filesToBackup as $item) {
                if (is_file($item)) {
                    $zip->addFile($item, $item);
                } elseif (is_dir($item)) {
                    $this->addDirectoryToZip($zip, $item, $item);
                }
            }
            
            $zip->close();
            
            if (file_exists($filepath)) {
                $fileSize = filesize($filepath);
                
                // تسجيل النسخة الاحتياطية
                $backupId = $this->logBackup([
                    'backup_type' => 'files',
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize,
                    'description' => $description,
                    'status' => 'completed'
                ]);
                
                logActivity('إنشاء نسخة احتياطية', "تم إنشاء نسخة احتياطية من الملفات: {$filename}");
                
                return [
                    'success' => true,
                    'backup_id' => $backupId,
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize
                ];
            } else {
                throw new Exception('فشل في إنشاء ملف النسخة الاحتياطية');
            }
            
        } catch (Exception $e) {
            error_log("Files backup error: " . $e->getMessage());
            
            // تسجيل الفشل
            $this->logBackup([
                'backup_type' => 'files',
                'filename' => $filename ?? null,
                'description' => $description,
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية كاملة (قاعدة البيانات + الملفات)
     */
    public function createFullBackup($description = null) {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $filename = "full_backup_{$timestamp}.zip";
            $filepath = $this->backupDir . $filename;
            
            // إنشاء نسخة احتياطية من قاعدة البيانات أولاً
            $dbBackup = $this->createDatabaseBackup("نسخة قاعدة البيانات ضمن النسخة الكاملة");
            if (!$dbBackup['success']) {
                throw new Exception('فشل في إنشاء نسخة احتياطية من قاعدة البيانات');
            }
            
            // إنشاء ملف ZIP للنسخة الكاملة
            $zip = new ZipArchive();
            if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
                throw new Exception('فشل في إنشاء ملف ZIP');
            }
            
            // إضافة نسخة قاعدة البيانات
            $zip->addFile($dbBackup['filepath'], 'database/' . $dbBackup['filename']);
            
            // إضافة الملفات المهمة
            $filesToBackup = [
                'uploads/',
                'includes/config.php',
                'includes/functions.php',
                '.htaccess'
            ];
            
            foreach ($filesToBackup as $item) {
                if (is_file($item)) {
                    $zip->addFile($item, 'files/' . $item);
                } elseif (is_dir($item)) {
                    $this->addDirectoryToZip($zip, $item, 'files/' . $item);
                }
            }
            
            $zip->close();
            
            if (file_exists($filepath)) {
                $fileSize = filesize($filepath);
                
                // تسجيل النسخة الاحتياطية
                $backupId = $this->logBackup([
                    'backup_type' => 'full',
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize,
                    'description' => $description,
                    'status' => 'completed'
                ]);
                
                logActivity('إنشاء نسخة احتياطية', "تم إنشاء نسخة احتياطية كاملة: {$filename}");
                
                return [
                    'success' => true,
                    'backup_id' => $backupId,
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'file_size' => $fileSize
                ];
            } else {
                throw new Exception('فشل في إنشاء ملف النسخة الاحتياطية');
            }
            
        } catch (Exception $e) {
            error_log("Full backup error: " . $e->getMessage());
            
            // تسجيل الفشل
            $this->logBackup([
                'backup_type' => 'full',
                'filename' => $filename ?? null,
                'description' => $description,
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * إضافة مجلد إلى ملف ZIP
     */
    private function addDirectoryToZip($zip, $dir, $zipPath) {
        if (is_dir($dir)) {
            $files = scandir($dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $filePath = $dir . '/' . $file;
                    $zipFilePath = $zipPath . '/' . $file;
                    
                    if (is_file($filePath)) {
                        $zip->addFile($filePath, $zipFilePath);
                    } elseif (is_dir($filePath)) {
                        $this->addDirectoryToZip($zip, $filePath, $zipFilePath);
                    }
                }
            }
        }
    }
    
    /**
     * تسجيل النسخة الاحتياطية في قاعدة البيانات
     */
    private function logBackup($data) {
        try {
            $query = "INSERT INTO backups (
                        backup_type, filename, filepath, file_size, 
                        description, status, error_message, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['backup_type'],
                $data['filename'],
                $data['filepath'] ?? null,
                $data['file_size'] ?? null,
                $data['description'],
                $data['status'],
                $data['error_message'] ?? null,
                $_SESSION['user_id'] ?? null
            ];
            
            return $this->db->insert($query, $params);
            
        } catch (Exception $e) {
            error_log("Log backup error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getBackups($filters = []) {
        try {
            $query = "SELECT 
                        b.backup_id, b.backup_type, b.filename, b.filepath, 
                        b.file_size, b.description, b.status, b.error_message,
                        b.created_at, u.full_name as created_by_name
                      FROM backups b
                      LEFT JOIN users u ON b.created_by = u.user_id";
            
            $whereConditions = [];
            $params = [];
            
            // فلترة حسب النوع
            if (!empty($filters['backup_type'])) {
                $whereConditions[] = "b.backup_type = ?";
                $params[] = $filters['backup_type'];
            }
            
            // فلترة حسب الحالة
            if (!empty($filters['status'])) {
                $whereConditions[] = "b.status = ?";
                $params[] = $filters['status'];
            }
            
            // فلترة حسب التاريخ من
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(b.created_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            // فلترة حسب التاريخ إلى
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(b.created_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            $query .= " ORDER BY b.created_at DESC";
            
            // تحديد عدد النتائج
            $limit = $filters['limit'] ?? 50;
            $offset = $filters['offset'] ?? 0;
            
            $query .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get backups error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على نسخة احتياطية بالمعرف
     */
    public function getBackupById($backupId) {
        try {
            $query = "SELECT 
                        b.*, u.full_name as created_by_name
                      FROM backups b
                      LEFT JOIN users u ON b.created_by = u.user_id
                      WHERE b.backup_id = ?";
            
            return $this->db->selectOne($query, [$backupId]);
            
        } catch (Exception $e) {
            error_log("Get backup by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup($backupId) {
        try {
            $backup = $this->getBackupById($backupId);
            if (!$backup) {
                throw new Exception('النسخة الاحتياطية غير موجودة');
            }
            
            // حذف الملف من النظام
            if (!empty($backup['filepath']) && file_exists($backup['filepath'])) {
                unlink($backup['filepath']);
            }
            
            // حذف السجل من قاعدة البيانات
            $query = "DELETE FROM backups WHERE backup_id = ?";
            $result = $this->db->delete($query, [$backupId]);
            
            if ($result) {
                logActivity('حذف نسخة احتياطية', "تم حذف النسخة الاحتياطية: {$backup['filename']}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete backup error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * الحصول على إحصائيات النسخ الاحتياطية
     */
    public function getBackupStats() {
        try {
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_backups,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_backups,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backups,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_backups,
                    SUM(CASE WHEN status = 'completed' THEN file_size ELSE 0 END) as total_size
                FROM backups
            ");
            
            // إحصائيات حسب النوع
            $typeStats = $this->db->select("
                SELECT 
                    backup_type,
                    COUNT(*) as count,
                    SUM(CASE WHEN status = 'completed' THEN file_size ELSE 0 END) as total_size
                FROM backups
                WHERE status = 'completed'
                GROUP BY backup_type
            ");
            
            return [
                'general' => $stats ?: [
                    'total_backups' => 0,
                    'successful_backups' => 0,
                    'failed_backups' => 0,
                    'today_backups' => 0,
                    'total_size' => 0
                ],
                'by_type' => $typeStats ?: []
            ];
            
        } catch (Exception $e) {
            error_log("Get backup stats error: " . $e->getMessage());
            return [
                'general' => [
                    'total_backups' => 0,
                    'successful_backups' => 0,
                    'failed_backups' => 0,
                    'today_backups' => 0,
                    'total_size' => 0
                ],
                'by_type' => []
            ];
        }
    }
    
    /**
     * تنظيف النسخ الاحتياطية القديمة
     */
    public function cleanOldBackups($daysToKeep = 30) {
        try {
            $query = "SELECT backup_id, filepath FROM backups 
                     WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $oldBackups = $this->db->select($query, [$daysToKeep]);
            
            $deletedCount = 0;
            foreach ($oldBackups as $backup) {
                if ($this->deleteBackup($backup['backup_id'])) {
                    $deletedCount++;
                }
            }
            
            if ($deletedCount > 0) {
                logActivity('تنظيف النسخ الاحتياطية', "تم حذف {$deletedCount} نسخة احتياطية قديمة");
            }
            
            return $deletedCount;
            
        } catch (Exception $e) {
            error_log("Clean old backups error: " . $e->getMessage());
            return false;
        }
    }
}

?>

<?php
/**
 * تشخيص مشكلة الفواتير
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>تشخيص مشكلة الفواتير</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f2f2f2; }
</style>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. فحص الاتصال بقاعدة البيانات:</h2>";
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    echo "<h2>2. فحص وجود الجداول المطلوبة:</h2>";
    
    $requiredTables = ['users', 'customers', 'invoices', 'invoice_items'];
    $existingTables = [];
    
    foreach ($requiredTables as $table) {
        try {
            $result = $db->select("SHOW TABLES LIKE '$table'");
            if (!empty($result)) {
                $existingTables[] = $table;
                echo "<p class='success'>✅ جدول $table موجود</p>";
                
                // عدد السجلات
                $count = $db->selectOne("SELECT COUNT(*) as count FROM $table");
                echo "<p class='info'>   - عدد السجلات: " . $count['count'] . "</p>";
                
            } else {
                echo "<p class='error'>❌ جدول $table غير موجود</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في فحص جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>3. فحص النماذج (Models):</h2>";
    
    $models = [
        'Customer' => 'models/Customer.php',
        'Invoice' => 'models/Invoice.php'
    ];
    
    foreach ($models as $modelName => $modelPath) {
        if (file_exists($modelPath)) {
            echo "<p class='success'>✅ ملف $modelName موجود</p>";
            
            try {
                if (class_exists($modelName)) {
                    echo "<p class='success'>   - فئة $modelName محملة بنجاح</p>";
                    
                    // اختبار إنشاء مثيل
                    $instance = new $modelName();
                    echo "<p class='success'>   - تم إنشاء مثيل من $modelName بنجاح</p>";
                    
                } else {
                    echo "<p class='error'>   - فئة $modelName غير محملة</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>   - خطأ في $modelName: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ ملف $modelName غير موجود في $modelPath</p>";
        }
    }
    
    echo "<h2>4. اختبار تحميل العملاء:</h2>";
    
    if (in_array('customers', $existingTables) && class_exists('Customer')) {
        try {
            $customerModel = new Customer();
            
            // جميع العملاء
            $allCustomers = $customerModel->getAllCustomers();
            echo "<p class='info'>عدد جميع العملاء: " . count($allCustomers) . "</p>";
            
            // عملاء فقط
            $customers = $customerModel->getAllCustomers('customer');
            echo "<p class='info'>عدد العملاء: " . count($customers) . "</p>";
            
            // موردين فقط
            $suppliers = $customerModel->getAllCustomers('supplier');
            echo "<p class='info'>عدد الموردين: " . count($suppliers) . "</p>";
            
            if (!empty($allCustomers)) {
                echo "<h3>عينة من العملاء:</h3>";
                echo "<table>";
                echo "<tr><th>المعرف</th><th>الكود</th><th>الاسم</th><th>النوع</th></tr>";
                foreach (array_slice($allCustomers, 0, 5) as $customer) {
                    echo "<tr>";
                    echo "<td>" . $customer['customer_id'] . "</td>";
                    echo "<td>" . htmlspecialchars($customer['customer_code']) . "</td>";
                    echo "<td>" . htmlspecialchars($customer['customer_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($customer['customer_type']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في تحميل العملاء: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ لا يمكن اختبار تحميل العملاء (جدول أو نموذج مفقود)</p>";
    }
    
    echo "<h2>5. اختبار تحميل الفواتير:</h2>";
    
    if (in_array('invoices', $existingTables) && class_exists('Invoice')) {
        try {
            $invoiceModel = new Invoice();
            
            $allInvoices = $invoiceModel->getAllInvoices();
            echo "<p class='info'>عدد الفواتير: " . count($allInvoices) . "</p>";
            
            if (!empty($allInvoices)) {
                echo "<h3>عينة من الفواتير:</h3>";
                echo "<table>";
                echo "<tr><th>المعرف</th><th>الرقم</th><th>النوع</th><th>المبلغ</th><th>الحالة</th></tr>";
                foreach (array_slice($allInvoices, 0, 5) as $invoice) {
                    echo "<tr>";
                    echo "<td>" . $invoice['invoice_id'] . "</td>";
                    echo "<td>" . htmlspecialchars($invoice['invoice_number']) . "</td>";
                    echo "<td>" . htmlspecialchars($invoice['invoice_type']) . "</td>";
                    echo "<td>" . number_format($invoice['total_amount'], 2) . "</td>";
                    echo "<td>" . htmlspecialchars($invoice['status']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في تحميل الفواتير: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ لا يمكن اختبار تحميل الفواتير (جدول أو نموذج مفقود)</p>";
    }
    
    echo "<h2>6. التوصيات:</h2>";
    
    if (!in_array('customers', $existingTables)) {
        echo "<p class='warning'>🔧 يجب إنشاء جدول العملاء: <a href='setup_customers.php'>اضغط هنا</a></p>";
    }
    
    if (!in_array('invoices', $existingTables) || !in_array('invoice_items', $existingTables)) {
        echo "<p class='warning'>🔧 يجب إنشاء جداول الفواتير: <a href='setup_invoices.php'>اضغط هنا</a></p>";
    }
    
    if (in_array('customers', $existingTables) && in_array('invoices', $existingTables)) {
        echo "<p class='success'>🎉 جميع الجداول موجودة! يمكنك الآن استخدام نظام الفواتير</p>";
        echo "<p><a href='invoices.php?action=add&type=sales'>جرب إضافة فاتورة مبيعات</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p>";
echo "<a href='setup_customers.php'>إعداد العملاء</a> | ";
echo "<a href='setup_invoices.php'>إعداد الفواتير</a> | ";
echo "<a href='invoices.php'>إدارة الفواتير</a> | ";
echo "<a href='index.php'>الرئيسية</a>";
echo "</p>";
?>

<?php
/**
 * نموذج العملاء والموردين
 * Customers and Suppliers Model
 */

class Customer {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع العملاء/الموردين
     */
    public function getAllCustomers($type = null, $activeOnly = true) {
        try {
            $query = "SELECT 
                        customer_id, customer_code, customer_name, customer_type,
                        phone, email, address, city, tax_number, credit_limit,
                        payment_terms, is_active, created_at,
                        COALESCE(account_balance, 0) as balance
                      FROM customers";
            
            $params = [];
            $conditions = [];
            
            if ($activeOnly) {
                $conditions[] = "is_active = 1";
            }
            
            if ($type) {
                $conditions[] = "customer_type = ?";
                $params[] = $type;
            }
            
            if (!empty($conditions)) {
                $query .= " WHERE " . implode(' AND ', $conditions);
            }
            
            $query .= " ORDER BY customer_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all customers error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على عميل بالمعرف
     */
    public function getCustomerById($customerId) {
        try {
            $query = "SELECT * FROM customers WHERE customer_id = ?";
            return $this->db->selectOne($query, [$customerId]);
            
        } catch (Exception $e) {
            error_log("Get customer by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء عميل جديد
     */
    public function createCustomer($data) {
        try {
            // التحقق من عدم وجود كود العميل
            if ($this->customerCodeExists($data['customer_code'])) {
                throw new Exception('كود العميل موجود بالفعل');
            }
            
            $query = "INSERT INTO customers (
                        customer_code, customer_name, customer_type, phone, email,
                        address, city, tax_number, credit_limit, payment_terms,
                        account_balance, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['customer_code'],
                $data['customer_name'],
                $data['customer_type'],
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['address'] ?? null,
                $data['city'] ?? null,
                $data['tax_number'] ?? null,
                $data['credit_limit'] ?? 0,
                $data['payment_terms'] ?? 30,
                0, // الرصيد الابتدائي
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $customerId = $this->db->insert($query, $params);
            
            if ($customerId) {
                logActivity('إنشاء عميل', "تم إنشاء العميل: {$data['customer_name']}");
            }
            
            return $customerId;
            
        } catch (Exception $e) {
            error_log("Create customer error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث العميل
     */
    public function updateCustomer($customerId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['customer_name'])) {
                $setParts[] = "customer_name = ?";
                $params[] = $data['customer_name'];
            }
            
            if (isset($data['customer_type'])) {
                $setParts[] = "customer_type = ?";
                $params[] = $data['customer_type'];
            }
            
            if (isset($data['phone'])) {
                $setParts[] = "phone = ?";
                $params[] = $data['phone'];
            }
            
            if (isset($data['email'])) {
                $setParts[] = "email = ?";
                $params[] = $data['email'];
            }
            
            if (isset($data['address'])) {
                $setParts[] = "address = ?";
                $params[] = $data['address'];
            }
            
            if (isset($data['city'])) {
                $setParts[] = "city = ?";
                $params[] = $data['city'];
            }
            
            if (isset($data['tax_number'])) {
                $setParts[] = "tax_number = ?";
                $params[] = $data['tax_number'];
            }
            
            if (isset($data['credit_limit'])) {
                $setParts[] = "credit_limit = ?";
                $params[] = $data['credit_limit'];
            }
            
            if (isset($data['payment_terms'])) {
                $setParts[] = "payment_terms = ?";
                $params[] = $data['payment_terms'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $customerId;
            
            $query = "UPDATE customers SET " . implode(', ', $setParts) . " WHERE customer_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث عميل', "تم تحديث العميل ID: {$customerId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update customer error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف العميل (إلغاء تفعيل)
     */
    public function deleteCustomer($customerId) {
        try {
            // التحقق من عدم وجود فواتير مرتبطة
            $invoicesCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM invoices WHERE customer_id = ?",
                [$customerId]
            );
            
            if ($invoicesCount['count'] > 0) {
                throw new Exception('لا يمكن حذف العميل لوجود فواتير مرتبطة به');
            }
            
            $query = "UPDATE customers SET is_active = 0, updated_at = NOW() WHERE customer_id = ?";
            $result = $this->db->update($query, [$customerId]);
            
            if ($result) {
                logActivity('حذف عميل', "تم حذف العميل ID: {$customerId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete customer error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود العميل
     */
    private function customerCodeExists($customerCode, $excludeCustomerId = null) {
        $query = "SELECT COUNT(*) as count FROM customers WHERE customer_code = ?";
        $params = [$customerCode];
        
        if ($excludeCustomerId) {
            $query .= " AND customer_id != ?";
            $params[] = $excludeCustomerId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * البحث في العملاء
     */
    public function searchCustomers($searchTerm, $type = null) {
        try {
            $query = "SELECT 
                        customer_id, customer_code, customer_name, customer_type, phone, city
                      FROM customers 
                      WHERE (customer_name LIKE ? OR customer_code LIKE ? OR phone LIKE ?) AND is_active = 1";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($type) {
                $query .= " AND customer_type = ?";
                $params[] = $type;
            }
            
            $query .= " ORDER BY customer_name LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search customers error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تحديث رصيد العميل
     */
    public function updateCustomerBalance($customerId, $amount, $operation = 'add') {
        try {
            if ($operation === 'add') {
                $query = "UPDATE customers SET account_balance = account_balance + ? WHERE customer_id = ?";
            } else {
                $query = "UPDATE customers SET account_balance = account_balance - ? WHERE customer_id = ?";
            }
            
            return $this->db->update($query, [$amount, $customerId]) > 0;
            
        } catch (Exception $e) {
            error_log("Update customer balance error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على رصيد العميل
     */
    public function getCustomerBalance($customerId) {
        try {
            $result = $this->db->selectOne(
                "SELECT account_balance FROM customers WHERE customer_id = ?",
                [$customerId]
            );
            
            return $result ? $result['account_balance'] : 0;
            
        } catch (Exception $e) {
            error_log("Get customer balance error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * أنواع العملاء
     */
    public function getCustomerTypes() {
        return [
            'customer' => 'عميل',
            'supplier' => 'مورد',
            'both' => 'عميل ومورد'
        ];
    }
    
    /**
     * الحصول على إحصائيات العملاء
     */
    public function getCustomersStats() {
        try {
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_customers,
                    COUNT(CASE WHEN customer_type = 'customer' THEN 1 END) as total_customers_only,
                    COUNT(CASE WHEN customer_type = 'supplier' THEN 1 END) as total_suppliers,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_customers,
                    COALESCE(SUM(CASE WHEN account_balance > 0 THEN account_balance END), 0) as total_receivables,
                    COALESCE(SUM(CASE WHEN account_balance < 0 THEN ABS(account_balance) END), 0) as total_payables
                FROM customers
            ");
            
            return $stats ?: [
                'total_customers' => 0,
                'total_customers_only' => 0,
                'total_suppliers' => 0,
                'active_customers' => 0,
                'total_receivables' => 0,
                'total_payables' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get customers stats error: " . $e->getMessage());
            return [
                'total_customers' => 0,
                'total_customers_only' => 0,
                'total_suppliers' => 0,
                'active_customers' => 0,
                'total_receivables' => 0,
                'total_payables' => 0
            ];
        }
    }
}

?>

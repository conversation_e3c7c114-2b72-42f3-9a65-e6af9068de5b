<?php
/**
 * صفحة التقارير المتقدمة
 * Advanced Reports Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';
require_once 'includes/chart_components.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيل نموذج التحليلات
$analyticsModel = new Analytics();

// الحصول على المعاملات
$reportType = $_GET['type'] ?? 'sales';
$period = $_GET['period'] ?? 'month';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// التحقق من صحة المعاملات
$validReportTypes = ['sales', 'purchases', 'inventory', 'customers', 'profit'];
$validPeriods = ['today', 'week', 'month', 'quarter', 'year', 'custom'];

if (!in_array($reportType, $validReportTypes)) {
    $reportType = 'sales';
}

if (!in_array($period, $validPeriods)) {
    $period = 'month';
}

// إعداد التواريخ للفترة المخصصة
if ($period === 'custom') {
    if (empty($dateFrom)) {
        $dateFrom = date('Y-m-01'); // بداية الشهر الحالي
    }
    if (empty($dateTo)) {
        $dateTo = date('Y-m-d'); // اليوم
    }
}

// الحصول على البيانات حسب نوع التقرير
$reportData = [];
$chartData = [];

switch ($reportType) {
    case 'sales':
        $reportData = $analyticsModel->getSalesStats($period);
        $chartData['daily'] = $analyticsModel->getDailySales(30);
        $chartData['monthly'] = $analyticsModel->getMonthlySales(12);
        $chartData['top_items'] = $analyticsModel->getTopSellingItems(10, $period);
        $chartData['top_customers'] = $analyticsModel->getTopCustomers(10, $period);
        break;
        
    case 'purchases':
        $reportData = $analyticsModel->getPurchaseStats($period);
        break;
        
    case 'inventory':
        $reportData = $analyticsModel->getInventoryStats();
        $chartData['low_stock'] = $analyticsModel->getLowStockItems(20);
        break;
        
    case 'customers':
        $reportData = $analyticsModel->getCustomerStats($period);
        $chartData['top_customers'] = $analyticsModel->getTopCustomers(15, $period);
        break;
        
    case 'profit':
        $reportData = $analyticsModel->getProfitSummary($period);
        $chartData['monthly'] = $analyticsModel->getMonthlySales(12);
        break;
}

$pageTitle = 'التقارير المتقدمة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- CSS الرسوم البيانية -->
    <link href="assets/css/charts.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .filter-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .report-tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .report-tab {
            padding: 12px 20px;
            border: none;
            background: none;
            color: #6b7280;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            text-decoration: none;
        }
        
        .report-tab:hover {
            color: #374151;
            background: #f9fafb;
        }
        
        .report-tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
            background: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-chart-bar ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- تبويبات التقارير -->
        <div class="report-tabs">
            <?php
            $reportTypes = [
                'sales' => ['المبيعات', 'fas fa-shopping-cart'],
                'purchases' => ['المشتريات', 'fas fa-shopping-bag'],
                'inventory' => ['المخزون', 'fas fa-boxes'],
                'customers' => ['العملاء', 'fas fa-users'],
                'profit' => ['الأرباح', 'fas fa-chart-line']
            ];
            
            foreach ($reportTypes as $type => $info) {
                $activeClass = ($reportType === $type) ? 'active' : '';
                $url = "?type={$type}&period={$period}";
                if ($period === 'custom') {
                    $url .= "&date_from={$dateFrom}&date_to={$dateTo}";
                }
                echo '<a href="' . $url . '" class="report-tab ' . $activeClass . '">
                    <i class="' . $info[1] . ' ml-2"></i>
                    ' . $info[0] . '
                </a>';
            }
            ?>
        </div>
        
        <!-- فلاتر التقرير -->
        <div class="filter-section">
            <form method="GET" action="">
                <input type="hidden" name="type" value="<?php echo $reportType; ?>">
                
                <div class="filter-grid">
                    
                    <!-- اختيار الفترة -->
                    <div>
                        <label for="period" class="block text-sm font-medium text-gray-700 mb-2">
                            الفترة الزمنية
                        </label>
                        <select id="period" name="period" onchange="toggleCustomDates()" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="today" <?php echo $period === 'today' ? 'selected' : ''; ?>>اليوم</option>
                            <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>هذا الأسبوع</option>
                            <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>هذا الشهر</option>
                            <option value="quarter" <?php echo $period === 'quarter' ? 'selected' : ''; ?>>هذا الربع</option>
                            <option value="year" <?php echo $period === 'year' ? 'selected' : ''; ?>>هذا العام</option>
                            <option value="custom" <?php echo $period === 'custom' ? 'selected' : ''; ?>>فترة مخصصة</option>
                        </select>
                    </div>
                    
                    <!-- تاريخ البداية -->
                    <div id="date_from_container" style="display: <?php echo $period === 'custom' ? 'block' : 'none'; ?>;">
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                            من تاريخ
                        </label>
                        <input type="date" id="date_from" name="date_from" value="<?php echo $dateFrom; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- تاريخ النهاية -->
                    <div id="date_to_container" style="display: <?php echo $period === 'custom' ? 'block' : 'none'; ?>;">
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                            إلى تاريخ
                        </label>
                        <input type="date" id="date_to" name="date_to" value="<?php echo $dateTo; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- زر التطبيق -->
                    <div>
                        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            تطبيق الفلتر
                        </button>
                    </div>
                    
                </div>
            </form>
        </div>

        <!-- محتوى التقرير -->
        <div class="dashboard-section">

            <?php if ($reportType === 'sales'): ?>
                <!-- تقرير المبيعات -->
                <h2 class="section-title">
                    <i class="fas fa-shopping-cart ml-2"></i>
                    تقرير المبيعات
                </h2>

                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid mb-6">
                    <?php
                    echo renderStatCard(
                        'إجمالي المبيعات',
                        formatMoney($reportData['total_sales'] ?? 0),
                        ($reportData['total_invoices'] ?? 0) . ' فاتورة',
                        'fas fa-shopping-cart',
                        '#10b981'
                    );

                    echo renderStatCard(
                        'متوسط الفاتورة',
                        formatMoney($reportData['average_sale'] ?? 0),
                        'متوسط قيمة الفاتورة',
                        'fas fa-calculator',
                        '#3b82f6'
                    );

                    echo renderStatCard(
                        'المبلغ المدفوع',
                        formatMoney($reportData['paid_amount'] ?? 0),
                        'المبالغ المحصلة',
                        'fas fa-check-circle',
                        '#10b981'
                    );

                    echo renderStatCard(
                        'المبلغ المعلق',
                        formatMoney($reportData['pending_amount'] ?? 0),
                        'في انتظار التحصيل',
                        'fas fa-clock',
                        '#f59e0b'
                    );
                    ?>
                </div>

                <!-- الرسوم البيانية -->
                <div class="charts-grid">

                    <!-- المبيعات اليومية -->
                    <div class="chart-container">
                        <?php
                        $dailySalesData = [];
                        foreach ($chartData['daily'] as $sale) {
                            $dailySalesData[date('m/d', strtotime($sale['sale_date']))] = $sale['daily_sales'];
                        }
                        echo renderLineChart($dailySalesData, 'المبيعات اليومية (آخر 30 يوم)', '#10b981');
                        ?>
                    </div>

                    <!-- أفضل الأصناف -->
                    <div class="chart-container">
                        <?php
                        $topItemsData = [];
                        foreach ($chartData['top_items'] as $item) {
                            $topItemsData[$item['item_name']] = $item['total_sales'];
                        }
                        echo renderBarChart($topItemsData, 'أفضل الأصناف مبيعاً', '#3b82f6');
                        ?>
                    </div>

                </div>

                <!-- جدول أفضل العملاء -->
                <div class="chart-container">
                    <?php
                    $topCustomersData = [];
                    foreach ($chartData['top_customers'] as $customer) {
                        $topCustomersData[] = [
                            $customer['customer_name'],
                            formatMoney($customer['total_purchases']),
                            $customer['invoice_count'],
                            formatMoney($customer['average_purchase'])
                        ];
                    }
                    echo renderStatsTable($topCustomersData, 'أفضل العملاء', ['اسم العميل', 'إجمالي المشتريات', 'عدد الفواتير', 'متوسط الفاتورة']);
                    ?>
                </div>

            <?php elseif ($reportType === 'inventory'): ?>
                <!-- تقرير المخزون -->
                <h2 class="section-title">
                    <i class="fas fa-boxes ml-2"></i>
                    تقرير المخزون
                </h2>

                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid mb-6">
                    <?php
                    echo renderStatCard(
                        'إجمالي الأصناف',
                        number_format($reportData['total_items'] ?? 0),
                        'صنف مسجل',
                        'fas fa-boxes',
                        '#8b5cf6'
                    );

                    echo renderStatCard(
                        'الأصناف المتوفرة',
                        number_format($reportData['items_in_stock'] ?? 0),
                        'صنف متوفر',
                        'fas fa-check-circle',
                        '#10b981'
                    );

                    echo renderStatCard(
                        'الأصناف منخفضة المخزون',
                        number_format($reportData['low_stock_items'] ?? 0),
                        'صنف تحت الحد الأدنى',
                        'fas fa-exclamation-triangle',
                        '#f59e0b'
                    );

                    echo renderStatCard(
                        'قيمة المخزون',
                        formatMoney($reportData['total_inventory_value'] ?? 0),
                        'القيمة الإجمالية',
                        'fas fa-dollar-sign',
                        '#3b82f6'
                    );
                    ?>
                </div>

                <!-- مؤشرات المخزون -->
                <div class="charts-grid">

                    <!-- نسبة الأصناف المتوفرة -->
                    <div class="chart-container">
                        <?php
                        $totalItems = $reportData['total_items'] ?? 1;
                        $inStockItems = $reportData['items_in_stock'] ?? 0;
                        $stockPercentage = ($inStockItems / $totalItems) * 100;
                        echo renderProgressCircle($stockPercentage, 'نسبة الأصناف المتوفرة', '#10b981');
                        ?>
                    </div>

                    <!-- توزيع حالة المخزون -->
                    <div class="chart-container">
                        <?php
                        $stockStatusData = [
                            'متوفر' => $reportData['items_in_stock'] ?? 0,
                            'منخفض' => $reportData['low_stock_items'] ?? 0,
                            'نفد' => $reportData['out_of_stock_items'] ?? 0
                        ];
                        echo renderPieChart($stockStatusData, 'توزيع حالة المخزون', ['#10b981', '#f59e0b', '#ef4444']);
                        ?>
                    </div>

                </div>

                <!-- جدول الأصناف منخفضة المخزون -->
                <div class="chart-container">
                    <?php
                    $lowStockData = [];
                    foreach ($chartData['low_stock'] as $item) {
                        $lowStockData[] = [
                            $item['item_name'],
                            $item['item_code'],
                            $item['current_stock'] . ' ' . ($item['unit_name'] ?? ''),
                            $item['minimum_stock'] . ' ' . ($item['unit_name'] ?? ''),
                            $item['shortage_quantity'] . ' ' . ($item['unit_name'] ?? '')
                        ];
                    }
                    echo renderStatsTable($lowStockData, 'الأصناف منخفضة المخزون', ['اسم الصنف', 'كود الصنف', 'المخزون الحالي', 'الحد الأدنى', 'النقص']);
                    ?>
                </div>

            <?php endif; ?>

        </div>

        <!-- أزرار الإجراءات -->
        <div class="bg-white rounded-lg shadow p-6 text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4">إجراءات التقرير</h3>
            <div class="flex justify-center gap-4 flex-wrap">

                <!-- طباعة التقرير -->
                <button onclick="printReport()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-print ml-2"></i>
                    طباعة التقرير
                </button>

                <!-- تصدير PDF -->
                <button onclick="exportPDF()" class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-file-pdf ml-2"></i>
                    تصدير PDF
                </button>

                <!-- تصدير Excel -->
                <button onclick="exportExcel()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-file-excel ml-2"></i>
                    تصدير Excel
                </button>

            </div>
        </div>

    </div>

    <!-- JavaScript بدون مكتبات خارجية -->
    <script>
        // إظهار/إخفاء حقول التاريخ المخصص
        function toggleCustomDates() {
            const period = document.getElementById('period').value;
            const dateFromContainer = document.getElementById('date_from_container');
            const dateToContainer = document.getElementById('date_to_container');

            if (period === 'custom') {
                dateFromContainer.style.display = 'block';
                dateToContainer.style.display = 'block';
            } else {
                dateFromContainer.style.display = 'none';
                dateToContainer.style.display = 'none';
            }
        }

        // طباعة التقرير
        function printReport() {
            // إخفاء العناصر غير المرغوب فيها في الطباعة
            const elementsToHide = document.querySelectorAll('.report-tabs, .filter-section, .bg-white.rounded-lg.shadow.p-6.text-center');
            elementsToHide.forEach(element => {
                element.style.display = 'none';
            });

            // طباعة الصفحة
            window.print();

            // إعادة إظهار العناصر بعد الطباعة
            elementsToHide.forEach(element => {
                element.style.display = '';
            });
        }

        // تصدير PDF (يحتاج تطبيق لاحق)
        function exportPDF() {
            alert('ميزة تصدير PDF ستكون متاحة قريباً');
        }

        // تصدير Excel (يحتاج تطبيق لاحق)
        function exportExcel() {
            alert('ميزة تصدير Excel ستكون متاحة قريباً');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق إعدادات التاريخ المخصص عند تحميل الصفحة
            toggleCustomDates();
        });
    </script>

</body>
</html>

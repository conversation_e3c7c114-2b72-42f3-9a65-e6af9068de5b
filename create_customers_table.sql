-- =====================================================
-- إنشاء جدول العملاء والموردين
-- Create Customers and Suppliers Table
-- =====================================================

USE accounting_inventory_system;

-- =====================================================
-- جدول العملاء والموردين (customers)
-- وصف: يحتوي على بيانات العملاء والموردين
-- =====================================================
CREATE TABLE IF NOT EXISTS customers (
    customer_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف العميل الفريد',
    customer_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'كود العميل',
    customer_name VARCHAR(150) NOT NULL COMMENT 'اسم العميل',
    customer_type ENUM('customer', 'supplier', 'both') 
                  NOT NULL DEFAULT 'customer' COMMENT 'نوع العميل (عميل/مورد/كلاهما)',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    address TEXT COMMENT 'العنوان',
    city VARCHAR(50) COMMENT 'المدينة',
    country VARCHAR(50) DEFAULT 'السعودية' COMMENT 'البلد',
    tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
    credit_limit DECIMAL(12,2) DEFAULT 0.00 COMMENT 'حد الائتمان',
    payment_terms INT DEFAULT 30 COMMENT 'شروط الدفع (بالأيام)',
    account_balance DECIMAL(12,2) DEFAULT 0.00 COMMENT 'رصيد الحساب',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط العميل',
    notes TEXT COMMENT 'ملاحظات إضافية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ السجل',
    
    INDEX idx_customer_code (customer_code),
    INDEX idx_customer_name (customer_name),
    INDEX idx_customer_type (customer_type),
    INDEX idx_phone (phone),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول العملاء والموردين';

-- =====================================================
-- إدراج بيانات تجريبية للعملاء
-- =====================================================

INSERT INTO customers (
    customer_code, customer_name, customer_type, phone, email, 
    address, city, credit_limit, payment_terms, is_active, created_by
) VALUES 
-- عملاء
('C001', 'شركة الرياض التجارية', 'customer', '0112345678', '<EMAIL>', 
 'شارع الملك فهد، الرياض', 'الرياض', 50000.00, 30, 1, 1),

('C002', 'مؤسسة جدة للمقاولات', 'customer', '0123456789', '<EMAIL>', 
 'شارع الأمير سلطان، جدة', 'جدة', 75000.00, 45, 1, 1),

('C003', 'شركة الدمام للصناعات', 'customer', '0134567890', '<EMAIL>', 
 'المنطقة الصناعية، الدمام', 'الدمام', 100000.00, 60, 1, 1),

('C004', 'مكتب الخبر للاستشارات', 'customer', '0145678901', '<EMAIL>', 
 'شارع الملك عبدالعزيز، الخبر', 'الخبر', 25000.00, 15, 1, 1),

('C005', 'شركة المدينة للتجارة', 'customer', '0156789012', '<EMAIL>', 
 'شارع قباء، المدينة المنورة', 'المدينة المنورة', 40000.00, 30, 1, 1),

-- موردين
('S001', 'شركة النخيل للمواد الغذائية', 'supplier', '0167890123', '<EMAIL>', 
 'المنطقة الصناعية الثانية، الرياض', 'الرياض', 0.00, 30, 1, 1),

('S002', 'مؤسسة البحر الأحمر للاستيراد', 'supplier', '0178901234', '<EMAIL>', 
 'ميناء جدة الإسلامي، جدة', 'جدة', 0.00, 45, 1, 1),

('S003', 'شركة الخليج للتكنولوجيا', 'supplier', '0189012345', '<EMAIL>', 
 'مدينة الملك عبدالله الاقتصادية، رابغ', 'رابغ', 0.00, 60, 1, 1),

('S004', 'مصنع الشرق للبلاستيك', 'supplier', '0190123456', '<EMAIL>', 
 'المنطقة الصناعية الأولى، الدمام', 'الدمام', 0.00, 30, 1, 1),

('S005', 'شركة الجنوب للنقل والتوزيع', 'supplier', '0101234567', '<EMAIL>', 
 'شارع الملك خالد، أبها', 'أبها', 0.00, 15, 1, 1),

-- عملاء وموردين (كلاهما)
('B001', 'مجموعة الوطن التجارية', 'both', '0112223333', '<EMAIL>', 
 'برج الوطن، شارع العليا، الرياض', 'الرياض', 200000.00, 30, 1, 1),

('B002', 'شركة المملكة للاستثمار', 'both', '0123334444', '<EMAIL>', 
 'مركز المملكة، الرياض', 'الرياض', 500000.00, 60, 1, 1);

-- =====================================================
-- رسائل النجاح
-- =====================================================
SELECT 'تم إنشاء جدول العملاء بنجاح!' as message;
SELECT 'تم إدراج البيانات التجريبية بنجاح!' as message;
SELECT 'النظام جاهز لإدارة العملاء والموردين!' as message;

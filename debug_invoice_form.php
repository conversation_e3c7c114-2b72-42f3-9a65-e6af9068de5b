<?php
/**
 * تشخيص مشكلة نموذج الفاتورة
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>تشخيص مشكلة نموذج الفاتورة</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>";

try {
    echo "<h2>1. فحص المتغيرات الأساسية:</h2>";
    
    $action = $_GET['action'] ?? 'list';
    $type = $_GET['type'] ?? 'sales';
    
    echo "<p class='info'>Action: " . htmlspecialchars($action) . "</p>";
    echo "<p class='info'>Type: " . htmlspecialchars($type) . "</p>";
    
    echo "<h2>2. فحص النماذج:</h2>";
    
    // فحص نموذج العميل
    echo "<h3>نموذج العميل:</h3>";
    try {
        $customerModel = new Customer();
        echo "<p class='success'>✅ تم إنشاء نموذج العميل بنجاح</p>";
        
        // اختبار تحميل العملاء
        $customerType = $type === 'sales' ? 'customer' : 'supplier';
        echo "<p class='info'>نوع العميل المطلوب: $customerType</p>";
        
        $customers = $customerModel->getAllCustomers($customerType);
        echo "<p class='info'>عدد العملاء المحملين: " . count($customers) . "</p>";
        
        if (!empty($customers)) {
            echo "<h4>العملاء المتاحين:</h4>";
            echo "<pre>";
            foreach ($customers as $customer) {
                echo "ID: " . $customer['customer_id'] . 
                     " | Code: " . $customer['customer_code'] . 
                     " | Name: " . $customer['customer_name'] . 
                     " | Type: " . $customer['customer_type'] . "\n";
            }
            echo "</pre>";
        } else {
            echo "<p class='warning'>⚠️ لا توجد عملاء من النوع المطلوب</p>";
            
            // فحص جميع العملاء
            $allCustomers = $customerModel->getAllCustomers();
            echo "<p class='info'>عدد جميع العملاء: " . count($allCustomers) . "</p>";
            
            if (!empty($allCustomers)) {
                echo "<h4>جميع العملاء الموجودين:</h4>";
                echo "<pre>";
                foreach ($allCustomers as $customer) {
                    echo "ID: " . $customer['customer_id'] . 
                         " | Code: " . $customer['customer_code'] . 
                         " | Name: " . $customer['customer_name'] . 
                         " | Type: " . $customer['customer_type'] . "\n";
                }
                echo "</pre>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في نموذج العميل: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // فحص نموذج الفاتورة
    echo "<h3>نموذج الفاتورة:</h3>";
    try {
        $invoiceModel = new Invoice();
        echo "<p class='success'>✅ تم إنشاء نموذج الفاتورة بنجاح</p>";
        
        // فحص دالة createInvoice
        $reflection = new ReflectionClass('Invoice');
        $method = $reflection->getMethod('createInvoice');
        $parameters = $method->getParameters();
        
        echo "<p class='info'>معاملات دالة createInvoice:</p>";
        echo "<pre>";
        foreach ($parameters as $param) {
            echo "- " . $param->getName() . ($param->isOptional() ? ' (اختياري)' : ' (مطلوب)') . "\n";
        }
        echo "</pre>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في نموذج الفاتورة: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>3. اختبار إنشاء فاتورة تجريبية:</h2>";
    
    if (isset($_POST['test_create'])) {
        try {
            $testData = [
                'invoice_number' => 'TEST-' . time(),
                'invoice_type' => 'sales',
                'customer_id' => 1, // أول عميل
                'invoice_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'total_amount' => 100.00,
                'tax_amount' => 15.00,
                'notes' => 'فاتورة تجريبية',
                'status' => 'draft'
            ];
            
            $testItems = [
                [
                    'item_name' => 'منتج تجريبي',
                    'quantity' => 1,
                    'unit_price' => 85.00,
                    'total_price' => 85.00
                ]
            ];
            
            echo "<p class='info'>بيانات الفاتورة التجريبية:</p>";
            echo "<pre>" . print_r($testData, true) . "</pre>";
            
            echo "<p class='info'>عناصر الفاتورة التجريبية:</p>";
            echo "<pre>" . print_r($testItems, true) . "</pre>";
            
            $newInvoiceId = $invoiceModel->createInvoice($testData, $testItems);
            
            if ($newInvoiceId) {
                echo "<p class='success'>✅ تم إنشاء الفاتورة التجريبية بنجاح! معرف الفاتورة: $newInvoiceId</p>";
            } else {
                echo "<p class='error'>❌ فشل في إنشاء الفاتورة التجريبية</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في إنشاء الفاتورة التجريبية: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
    
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_create' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer;'>اختبار إنشاء فاتورة تجريبية</button>";
    echo "</form>";
    
    echo "<h2>4. فحص الجلسة والمستخدم:</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'>✅ المستخدم مسجل دخول - ID: " . $_SESSION['user_id'] . "</p>";
    } else {
        echo "<p class='error'>❌ المستخدم غير مسجل دخول</p>";
    }
    
    echo "<h2>5. فحص دوال الأمان:</h2>";
    
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        echo "<p class='success'>✅ دالة generateCSRFToken تعمل - Token: " . substr($token, 0, 20) . "...</p>";
    } else {
        echo "<p class='error'>❌ دالة generateCSRFToken غير موجودة</p>";
    }
    
    if (function_exists('verifyCSRFToken')) {
        echo "<p class='success'>✅ دالة verifyCSRFToken موجودة</p>";
    } else {
        echo "<p class='error'>❌ دالة verifyCSRFToken غير موجودة</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='invoices.php?action=add&type=sales'>العودة لنموذج الفاتورة</a></p>";
?>

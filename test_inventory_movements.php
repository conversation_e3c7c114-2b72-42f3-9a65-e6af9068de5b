<?php
/**
 * اختبار نظام حركات المخزون
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار نظام حركات المخزون</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f2f2f2; }
    .btn { display: inline-block; padding: 8px 16px; margin: 4px; text-decoration: none; border-radius: 4px; color: white; }
    .btn-primary { background-color: #007bff; }
    .btn-success { background-color: #28a745; }
    .btn-warning { background-color: #ffc107; color: black; }
    .btn-danger { background-color: #dc3545; }
</style>";

try {
    echo "<h2>1. فحص الجداول المطلوبة:</h2>";
    
    $db = Database::getInstance();
    
    // فحص جدول حركات المخزون
    try {
        $movementsCount = $db->selectOne("SELECT COUNT(*) as count FROM inventory_movements");
        echo "<p class='success'>✅ جدول inventory_movements موجود (" . $movementsCount['count'] . " حركة)</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ جدول inventory_movements غير موجود: " . $e->getMessage() . "</p>";
    }
    
    // فحص جدول المستودعات
    try {
        $warehousesCount = $db->selectOne("SELECT COUNT(*) as count FROM warehouses WHERE is_active = 1");
        echo "<p class='success'>✅ جدول warehouses موجود (" . $warehousesCount['count'] . " مستودع نشط)</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ جدول warehouses غير موجود: " . $e->getMessage() . "</p>";
    }
    
    // فحص جدول الأصناف
    try {
        $itemsCount = $db->selectOne("SELECT COUNT(*) as count FROM items WHERE is_active = 1");
        echo "<p class='success'>✅ جدول items موجود (" . $itemsCount['count'] . " صنف نشط)</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ جدول items غير موجود: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>2. فحص نموذج المخزون:</h2>";
    
    if (class_exists('Inventory')) {
        echo "<p class='success'>✅ فئة Inventory موجودة</p>";
        
        $inventoryModel = new Inventory();
        
        // اختبار دالة الحصول على المستودعات
        try {
            $warehouses = $inventoryModel->getWarehouses();
            echo "<p class='success'>✅ دالة getWarehouses تعمل (" . count($warehouses) . " مستودع)</p>";
            
            if (!empty($warehouses)) {
                echo "<table>";
                echo "<tr><th>المعرف</th><th>اسم المستودع</th><th>الكود</th></tr>";
                foreach (array_slice($warehouses, 0, 5) as $warehouse) {
                    echo "<tr>";
                    echo "<td>" . $warehouse['warehouse_id'] . "</td>";
                    echo "<td>" . htmlspecialchars($warehouse['warehouse_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($warehouse['warehouse_code'] ?? '') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في دالة getWarehouses: " . $e->getMessage() . "</p>";
        }
        
        // اختبار دالة الحصول على الأصناف
        try {
            $items = $inventoryModel->getItems();
            echo "<p class='success'>✅ دالة getItems تعمل (" . count($items) . " صنف)</p>";
            
            if (!empty($items)) {
                echo "<table>";
                echo "<tr><th>المعرف</th><th>اسم الصنف</th><th>الكود</th><th>الوحدة</th></tr>";
                foreach (array_slice($items, 0, 5) as $item) {
                    echo "<tr>";
                    echo "<td>" . $item['item_id'] . "</td>";
                    echo "<td>" . htmlspecialchars($item['item_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($item['item_code']) . "</td>";
                    echo "<td>" . htmlspecialchars($item['unit_symbol'] ?? '') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في دالة getItems: " . $e->getMessage() . "</p>";
        }
        
        // اختبار دالة الحصول على الحركات
        try {
            $movements = $inventoryModel->getInventoryMovements([], 5, 0);
            echo "<p class='success'>✅ دالة getInventoryMovements تعمل (" . count($movements) . " حركة)</p>";
            
            if (!empty($movements)) {
                echo "<table>";
                echo "<tr><th>رقم الحركة</th><th>التاريخ</th><th>النوع</th><th>الصنف</th><th>المستودع</th><th>الكمية</th></tr>";
                foreach ($movements as $movement) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($movement['movement_number']) . "</td>";
                    echo "<td>" . $movement['movement_date'] . "</td>";
                    echo "<td>" . $movement['movement_type'] . "</td>";
                    echo "<td>" . htmlspecialchars($movement['item_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($movement['warehouse_name']) . "</td>";
                    echo "<td>" . number_format($movement['quantity'], 2) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='info'>لا توجد حركات مخزون</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في دالة getInventoryMovements: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p class='error'>❌ فئة Inventory غير موجودة</p>";
    }
    
    echo "<h2>3. إنشاء بيانات تجريبية (إذا لزم الأمر):</h2>";
    
    if (isset($_POST['create_sample_data'])) {
        try {
            // إنشاء مستودع تجريبي
            $warehouseExists = $db->selectOne("SELECT warehouse_id FROM warehouses WHERE warehouse_code = 'MAIN'");
            if (!$warehouseExists) {
                $db->insert("INSERT INTO warehouses (warehouse_name, warehouse_code, is_active) VALUES (?, ?, ?)", 
                           ['المستودع الرئيسي', 'MAIN', 1]);
                echo "<p class='success'>✅ تم إنشاء مستودع تجريبي</p>";
            }
            
            // إنشاء وحدة تجريبية
            $unitExists = $db->selectOne("SELECT unit_id FROM units WHERE unit_code = 'PCS'");
            if (!$unitExists) {
                $db->insert("INSERT INTO units (unit_name, unit_symbol, unit_code, is_active) VALUES (?, ?, ?, ?)", 
                           ['قطعة', 'قطعة', 'PCS', 1]);
                echo "<p class='success'>✅ تم إنشاء وحدة تجريبية</p>";
            }
            
            // إنشاء صنف تجريبي
            $itemExists = $db->selectOne("SELECT item_id FROM items WHERE item_code = 'ITEM001'");
            if (!$itemExists) {
                $unitId = $db->selectOne("SELECT unit_id FROM units WHERE unit_code = 'PCS'")['unit_id'];
                $db->insert("INSERT INTO items (item_name, item_code, unit_id, is_active) VALUES (?, ?, ?, ?)", 
                           ['صنف تجريبي', 'ITEM001', $unitId, 1]);
                echo "<p class='success'>✅ تم إنشاء صنف تجريبي</p>";
            }
            
            echo "<p class='info'>تم إنشاء البيانات التجريبية بنجاح</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في إنشاء البيانات التجريبية: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<form method='POST'>";
    echo "<button type='submit' name='create_sample_data' class='btn btn-success'>إنشاء بيانات تجريبية</button>";
    echo "</form>";
    
    echo "<h2>4. اختبار الروابط:</h2>";
    
    echo "<p>";
    echo "<a href='inventory_movements.php' class='btn btn-primary' target='_blank'>صفحة حركات المخزون</a>";
    echo "<a href='inventory_movements.php?action=add' class='btn btn-success' target='_blank'>إضافة حركة جديدة</a>";
    echo "<a href='inventory.php' class='btn btn-warning' target='_blank'>أرصدة المخزون</a>";
    echo "<a href='warehouses.php' class='btn btn-primary' target='_blank'>إدارة المستودعات</a>";
    echo "<a href='items.php' class='btn btn-primary' target='_blank'>إدارة الأصناف</a>";
    echo "</p>";
    
    echo "<h2>5. فحص هيكل الجداول:</h2>";
    
    // فحص هيكل جدول حركات المخزون
    try {
        $structure = $db->select("DESCRIBE inventory_movements");
        echo "<h3>هيكل جدول inventory_movements:</h3>";
        echo "<table>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($structure as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . $col['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ خطأ في فحص هيكل الجدول: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li>تأكد من وجود الجداول المطلوبة</li>";
echo "<li>أنشئ بيانات تجريبية إذا لزم الأمر</li>";
echo "<li>اختبر صفحة حركات المخزون</li>";
echo "<li>جرب إضافة حركة جديدة</li>";
echo "<li>تحقق من عرض الحركات والفلترة</li>";
echo "</ol>";
?>

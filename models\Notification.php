<?php
/**
 * نموذج الإشعارات
 * Notifications Model
 */

class Notification {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * إنشاء إشعار جديد
     */
    public function createNotification($data) {
        try {
            $query = "INSERT INTO notifications (
                        notification_type, title, message, priority, 
                        user_id, related_table, related_id, 
                        action_url, expires_at, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['notification_type'],
                $data['title'],
                $data['message'],
                $data['priority'] ?? 'medium',
                $data['user_id'] ?? null,
                $data['related_table'] ?? null,
                $data['related_id'] ?? null,
                $data['action_url'] ?? null,
                $data['expires_at'] ?? null
            ];
            
            return $this->db->insert($query, $params);
            
        } catch (Exception $e) {
            error_log("Create notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($userId, $unreadOnly = false, $limit = 50) {
        try {
            $query = "SELECT * FROM notifications 
                     WHERE (user_id = ? OR user_id IS NULL)
                     AND (expires_at IS NULL OR expires_at > NOW())";
            
            $params = [$userId];
            
            if ($unreadOnly) {
                $query .= " AND is_read = 0";
            }
            
            $query .= " ORDER BY priority DESC, created_at DESC LIMIT ?";
            $params[] = $limit;
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get user notifications error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount($userId) {
        try {
            $query = "SELECT COUNT(*) as count FROM notifications 
                     WHERE (user_id = ? OR user_id IS NULL)
                     AND is_read = 0
                     AND (expires_at IS NULL OR expires_at > NOW())";
            
            $result = $this->db->selectOne($query, [$userId]);
            return $result['count'] ?? 0;
            
        } catch (Exception $e) {
            error_log("Get unread count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead($notificationId, $userId = null) {
        try {
            $query = "UPDATE notifications SET is_read = 1, read_at = NOW() 
                     WHERE notification_id = ?";
            $params = [$notificationId];
            
            if ($userId) {
                $query .= " AND (user_id = ? OR user_id IS NULL)";
                $params[] = $userId;
            }
            
            return $this->db->update($query, $params) > 0;
            
        } catch (Exception $e) {
            error_log("Mark as read error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead($userId) {
        try {
            $query = "UPDATE notifications SET is_read = 1, read_at = NOW() 
                     WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0";
            
            return $this->db->update($query, [$userId]) > 0;
            
        } catch (Exception $e) {
            error_log("Mark all as read error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * حذف إشعار
     */
    public function deleteNotification($notificationId, $userId = null) {
        try {
            $query = "DELETE FROM notifications WHERE notification_id = ?";
            $params = [$notificationId];
            
            if ($userId) {
                $query .= " AND (user_id = ? OR user_id IS NULL)";
                $params[] = $userId;
            }
            
            return $this->db->delete($query, $params) > 0;
            
        } catch (Exception $e) {
            error_log("Delete notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فحص المخزون المنخفض وإنشاء إشعارات
     */
    public function checkLowStockNotifications() {
        try {
            // الحصول على الأصناف ذات المخزون المنخفض
            $query = "SELECT i.item_id, i.item_name, i.item_code, i.min_stock_level,
                            inv.available_quantity, w.warehouse_name
                     FROM items i
                     JOIN inventory inv ON i.item_id = inv.item_id
                     JOIN warehouses w ON inv.warehouse_id = w.warehouse_id
                     WHERE i.min_stock_level > 0 
                     AND inv.available_quantity <= i.min_stock_level
                     AND i.is_active = 1";
            
            $lowStockItems = $this->db->select($query);
            
            foreach ($lowStockItems as $item) {
                // التحقق من عدم وجود إشعار مشابه في آخر 24 ساعة
                $existingNotification = $this->db->selectOne(
                    "SELECT notification_id FROM notifications 
                     WHERE notification_type = 'low_stock' 
                     AND related_table = 'items' 
                     AND related_id = ? 
                     AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)",
                    [$item['item_id']]
                );
                
                if (!$existingNotification) {
                    $this->createNotification([
                        'notification_type' => 'low_stock',
                        'title' => 'مخزون منخفض',
                        'message' => "الصنف '{$item['item_name']}' في مخزن '{$item['warehouse_name']}' وصل إلى الحد الأدنى. الكمية المتاحة: {$item['available_quantity']}، الحد الأدنى: {$item['min_stock_level']}",
                        'priority' => 'high',
                        'related_table' => 'items',
                        'related_id' => $item['item_id'],
                        'action_url' => "items.php?action=edit&id={$item['item_id']}"
                    ]);
                }
            }
            
            return count($lowStockItems);
            
        } catch (Exception $e) {
            error_log("Check low stock notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فحص الفواتير المستحقة وإنشاء إشعارات
     */
    public function checkOverdueInvoicesNotifications() {
        try {
            // الحصول على الفواتير المستحقة
            $query = "SELECT i.invoice_id, i.invoice_number, i.invoice_type, 
                            i.total_amount, i.due_date, c.customer_name
                     FROM invoices i
                     LEFT JOIN customers c ON i.customer_id = c.customer_id
                     WHERE i.payment_status != 'paid' 
                     AND i.due_date < CURDATE()
                     AND i.status = 'active'";
            
            $overdueInvoices = $this->db->select($query);
            
            foreach ($overdueInvoices as $invoice) {
                // التحقق من عدم وجود إشعار مشابه في آخر 7 أيام
                $existingNotification = $this->db->selectOne(
                    "SELECT notification_id FROM notifications 
                     WHERE notification_type = 'overdue_invoice' 
                     AND related_table = 'invoices' 
                     AND related_id = ? 
                     AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
                    [$invoice['invoice_id']]
                );
                
                if (!$existingNotification) {
                    $invoiceTypeLabel = $invoice['invoice_type'] === 'sales' ? 'مبيعات' : 'مشتريات';
                    $customerName = $invoice['customer_name'] ?? 'غير محدد';
                    
                    $this->createNotification([
                        'notification_type' => 'overdue_invoice',
                        'title' => 'فاتورة مستحقة',
                        'message' => "فاتورة {$invoiceTypeLabel} رقم {$invoice['invoice_number']} للعميل '{$customerName}' مستحقة منذ {$invoice['due_date']}. المبلغ: " . formatMoney($invoice['total_amount']),
                        'priority' => 'high',
                        'related_table' => 'invoices',
                        'related_id' => $invoice['invoice_id'],
                        'action_url' => "invoices.php?action=view&id={$invoice['invoice_id']}"
                    ]);
                }
            }
            
            return count($overdueInvoices);
            
        } catch (Exception $e) {
            error_log("Check overdue invoices notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فحص النسخ الاحتياطية وإنشاء إشعارات
     */
    public function checkBackupNotifications() {
        try {
            // التحقق من آخر نسخة احتياطية ناجحة
            $lastBackup = $this->db->selectOne(
                "SELECT created_at FROM backups 
                 WHERE status = 'completed' 
                 ORDER BY created_at DESC LIMIT 1"
            );
            
            if ($lastBackup) {
                $lastBackupTime = strtotime($lastBackup['created_at']);
                $hoursSinceLastBackup = (time() - $lastBackupTime) / 3600;
                
                // إشعار إذا لم يتم إنشاء نسخة احتياطية منذ أكثر من 48 ساعة
                if ($hoursSinceLastBackup > 48) {
                    // التحقق من عدم وجود إشعار مشابه في آخر 24 ساعة
                    $existingNotification = $this->db->selectOne(
                        "SELECT notification_id FROM notifications 
                         WHERE notification_type = 'backup_warning' 
                         AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)"
                    );
                    
                    if (!$existingNotification) {
                        $this->createNotification([
                            'notification_type' => 'backup_warning',
                            'title' => 'تحذير النسخ الاحتياطي',
                            'message' => "لم يتم إنشاء نسخة احتياطية منذ أكثر من 48 ساعة. آخر نسخة احتياطية: " . formatDate($lastBackup['created_at']),
                            'priority' => 'high',
                            'action_url' => 'backup.php'
                        ]);
                        
                        return 1;
                    }
                }
            } else {
                // لا توجد نسخ احتياطية على الإطلاق
                $existingNotification = $this->db->selectOne(
                    "SELECT notification_id FROM notifications 
                     WHERE notification_type = 'no_backup' 
                     AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)"
                );
                
                if (!$existingNotification) {
                    $this->createNotification([
                        'notification_type' => 'no_backup',
                        'title' => 'لا توجد نسخ احتياطية',
                        'message' => 'لم يتم إنشاء أي نسخة احتياطية بعد. يُنصح بإنشاء نسخة احتياطية لحماية البيانات.',
                        'priority' => 'high',
                        'action_url' => 'backup.php'
                    ]);
                    
                    return 1;
                }
            }
            
            return 0;
            
        } catch (Exception $e) {
            error_log("Check backup notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تشغيل جميع فحوصات الإشعارات
     */
    public function runAllChecks() {
        $results = [
            'low_stock' => $this->checkLowStockNotifications(),
            'overdue_invoices' => $this->checkOverdueInvoicesNotifications(),
            'backup_warnings' => $this->checkBackupNotifications()
        ];
        
        return $results;
    }
    
    /**
     * تنظيف الإشعارات القديمة
     */
    public function cleanOldNotifications($daysToKeep = 30) {
        try {
            $query = "DELETE FROM notifications 
                     WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
                     OR (expires_at IS NOT NULL AND expires_at < NOW())";
            
            return $this->db->delete($query, [$daysToKeep]);
            
        } catch (Exception $e) {
            error_log("Clean old notifications error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على إحصائيات الإشعارات
     */
    public function getNotificationStats($userId = null) {
        try {
            $whereClause = $userId ? "WHERE (user_id = ? OR user_id IS NULL)" : "";
            $params = $userId ? [$userId] : [];
            
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_notifications,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_notifications,
                    COUNT(CASE WHEN priority = 'high' AND is_read = 0 THEN 1 END) as high_priority_unread,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_notifications
                FROM notifications 
                {$whereClause}
                AND (expires_at IS NULL OR expires_at > NOW())
            ", $params);
            
            // إحصائيات حسب النوع
            $typeStats = $this->db->select("
                SELECT 
                    notification_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count
                FROM notifications 
                {$whereClause}
                AND (expires_at IS NULL OR expires_at > NOW())
                GROUP BY notification_type
            ", $params);
            
            return [
                'general' => $stats ?: [
                    'total_notifications' => 0,
                    'unread_notifications' => 0,
                    'high_priority_unread' => 0,
                    'today_notifications' => 0
                ],
                'by_type' => $typeStats ?: []
            ];
            
        } catch (Exception $e) {
            error_log("Get notification stats error: " . $e->getMessage());
            return [
                'general' => [
                    'total_notifications' => 0,
                    'unread_notifications' => 0,
                    'high_priority_unread' => 0,
                    'today_notifications' => 0
                ],
                'by_type' => []
            ];
        }
    }
}

?>

<?php
/**
 * نموذج سجل الأنشطة
 * Activity Log Model
 */

class ActivityLog {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * تسجيل نشاط جديد
     */
    public function logActivity($userId, $action, $description, $tableName = null, $recordId = null, $ipAddress = null) {
        try {
            $query = "INSERT INTO activity_log (
                        user_id, action, description, table_name, record_id, 
                        ip_address, user_agent, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            if ($ipAddress === null) {
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            }
            
            $params = [
                $userId,
                $action,
                $description,
                $tableName,
                $recordId,
                $ipAddress,
                $userAgent
            ];
            
            return $this->db->insert($query, $params);
            
        } catch (Exception $e) {
            error_log("Log activity error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على سجل الأنشطة مع الفلترة
     */
    public function getActivityLog($filters = []) {
        try {
            $query = "SELECT 
                        al.log_id, al.action, al.description, al.table_name, 
                        al.record_id, al.ip_address, al.created_at,
                        u.username, u.full_name
                      FROM activity_log al
                      LEFT JOIN users u ON al.user_id = u.user_id";
            
            $whereConditions = [];
            $params = [];
            
            // فلترة حسب المستخدم
            if (!empty($filters['user_id'])) {
                $whereConditions[] = "al.user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            // فلترة حسب النشاط
            if (!empty($filters['action'])) {
                $whereConditions[] = "al.action = ?";
                $params[] = $filters['action'];
            }
            
            // فلترة حسب الجدول
            if (!empty($filters['table_name'])) {
                $whereConditions[] = "al.table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            // فلترة حسب التاريخ من
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(al.created_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            // فلترة حسب التاريخ إلى
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(al.created_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            // فلترة حسب البحث في الوصف
            if (!empty($filters['search'])) {
                $whereConditions[] = "(al.description LIKE ? OR al.action LIKE ? OR u.full_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            $query .= " ORDER BY al.created_at DESC";
            
            // تحديد عدد النتائج
            $limit = $filters['limit'] ?? 100;
            $offset = $filters['offset'] ?? 0;
            
            $query .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get activity log error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * عدد إجمالي سجلات الأنشطة
     */
    public function getActivityLogCount($filters = []) {
        try {
            $query = "SELECT COUNT(*) as count
                      FROM activity_log al
                      LEFT JOIN users u ON al.user_id = u.user_id";
            
            $whereConditions = [];
            $params = [];
            
            // نفس شروط الفلترة
            if (!empty($filters['user_id'])) {
                $whereConditions[] = "al.user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $whereConditions[] = "al.action = ?";
                $params[] = $filters['action'];
            }
            
            if (!empty($filters['table_name'])) {
                $whereConditions[] = "al.table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "DATE(al.created_at) >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "DATE(al.created_at) <= ?";
                $params[] = $filters['date_to'];
            }
            
            if (!empty($filters['search'])) {
                $whereConditions[] = "(al.description LIKE ? OR al.action LIKE ? OR u.full_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if (!empty($whereConditions)) {
                $query .= " WHERE " . implode(' AND ', $whereConditions);
            }
            
            $result = $this->db->selectOne($query, $params);
            return $result['count'] ?? 0;
            
        } catch (Exception $e) {
            error_log("Get activity log count error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * الحصول على إحصائيات الأنشطة
     */
    public function getActivityStats() {
        try {
            // إحصائيات عامة
            $generalStats = $this->db->selectOne("
                SELECT 
                    COUNT(*) as total_activities,
                    COUNT(DISTINCT user_id) as active_users,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
                    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities
                FROM activity_log
            ");
            
            // أكثر الأنشطة
            $topActions = $this->db->select("
                SELECT 
                    action,
                    COUNT(*) as count
                FROM activity_log
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY action
                ORDER BY count DESC
                LIMIT 10
            ");
            
            // أكثر المستخدمين نشاطاً
            $topUsers = $this->db->select("
                SELECT 
                    u.full_name,
                    u.username,
                    COUNT(al.log_id) as activity_count
                FROM activity_log al
                JOIN users u ON al.user_id = u.user_id
                WHERE DATE(al.created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY al.user_id, u.full_name, u.username
                ORDER BY activity_count DESC
                LIMIT 10
            ");
            
            // الأنشطة حسب اليوم (آخر 7 أيام)
            $dailyActivities = $this->db->select("
                SELECT 
                    DATE(created_at) as activity_date,
                    COUNT(*) as count
                FROM activity_log
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                GROUP BY DATE(created_at)
                ORDER BY activity_date DESC
            ");
            
            return [
                'general' => $generalStats ?: [
                    'total_activities' => 0,
                    'active_users' => 0,
                    'today_activities' => 0,
                    'week_activities' => 0
                ],
                'top_actions' => $topActions ?: [],
                'top_users' => $topUsers ?: [],
                'daily_activities' => $dailyActivities ?: []
            ];
            
        } catch (Exception $e) {
            error_log("Get activity stats error: " . $e->getMessage());
            return [
                'general' => [
                    'total_activities' => 0,
                    'active_users' => 0,
                    'today_activities' => 0,
                    'week_activities' => 0
                ],
                'top_actions' => [],
                'top_users' => [],
                'daily_activities' => []
            ];
        }
    }
    
    /**
     * الحصول على أنواع الأنشطة المتاحة
     */
    public function getAvailableActions() {
        try {
            $query = "SELECT DISTINCT action FROM activity_log ORDER BY action";
            $result = $this->db->select($query);
            
            return array_column($result, 'action');
            
        } catch (Exception $e) {
            error_log("Get available actions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على الجداول المتاحة
     */
    public function getAvailableTables() {
        try {
            $query = "SELECT DISTINCT table_name FROM activity_log WHERE table_name IS NOT NULL ORDER BY table_name";
            $result = $this->db->select($query);
            
            return array_column($result, 'table_name');
            
        } catch (Exception $e) {
            error_log("Get available tables error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حذف السجلات القديمة
     */
    public function cleanOldLogs($daysToKeep = 90) {
        try {
            $query = "DELETE FROM activity_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $result = $this->db->delete($query, [$daysToKeep]);
            
            if ($result > 0) {
                $this->logActivity(
                    $_SESSION['user_id'] ?? null,
                    'تنظيف سجل الأنشطة',
                    "تم حذف {$result} سجل أقدم من {$daysToKeep} يوم"
                );
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Clean old logs error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تصدير سجل الأنشطة إلى CSV
     */
    public function exportToCSV($filters = []) {
        try {
            $activities = $this->getActivityLog($filters);
            
            if (empty($activities)) {
                return false;
            }
            
            $filename = 'activity_log_' . date('Y-m-d_H-i-s') . '.csv';
            $filepath = 'exports/' . $filename;
            
            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            if (!is_dir('exports')) {
                mkdir('exports', 0755, true);
            }
            
            $file = fopen($filepath, 'w');
            
            // إضافة BOM للدعم العربي
            fwrite($file, "\xEF\xBB\xBF");
            
            // رأس الجدول
            fputcsv($file, [
                'التاريخ والوقت',
                'المستخدم',
                'النشاط',
                'الوصف',
                'الجدول',
                'معرف السجل',
                'عنوان IP'
            ]);
            
            // البيانات
            foreach ($activities as $activity) {
                fputcsv($file, [
                    $activity['created_at'],
                    $activity['full_name'] ?? $activity['username'] ?? 'غير معروف',
                    $activity['action'],
                    $activity['description'],
                    $activity['table_name'] ?? '',
                    $activity['record_id'] ?? '',
                    $activity['ip_address'] ?? ''
                ]);
            }
            
            fclose($file);
            
            return $filepath;
            
        } catch (Exception $e) {
            error_log("Export activity log error: " . $e->getMessage());
            return false;
        }
    }
}

?>

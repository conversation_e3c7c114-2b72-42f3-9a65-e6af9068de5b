<?php
/**
 * نموذج الإعدادات
 * Settings Model
 */

class Setting {
    private $db;
    private static $cache = [];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على قيمة إعداد
     */
    public function get($key, $default = null) {
        // التحقق من الكاش أولاً
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }
        
        try {
            $result = $this->db->selectOne(
                "SELECT setting_value FROM settings WHERE setting_key = ?",
                [$key]
            );
            
            $value = $result ? $result['setting_value'] : $default;
            
            // حفظ في الكاش
            self::$cache[$key] = $value;
            
            return $value;
            
        } catch (Exception $e) {
            error_log("Get setting error: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * تعيين قيمة إعداد
     */
    public function set($key, $value, $description = null) {
        try {
            // التحقق من وجود الإعداد
            $existing = $this->db->selectOne(
                "SELECT setting_id FROM settings WHERE setting_key = ?",
                [$key]
            );
            
            if ($existing) {
                // تحديث الإعداد الموجود
                $result = $this->db->update(
                    "UPDATE settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
                    [$value, $key]
                );
            } else {
                // إنشاء إعداد جديد
                $result = $this->db->insert(
                    "INSERT INTO settings (setting_key, setting_value, setting_description, created_at) VALUES (?, ?, ?, NOW())",
                    [$key, $value, $description]
                );
            }
            
            if ($result) {
                // تحديث الكاش
                self::$cache[$key] = $value;
                
                // تسجيل النشاط
                logActivity('تحديث إعداد', "تم تحديث الإعداد: {$key}");
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Set setting error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على مجموعة إعدادات
     */
    public function getGroup($prefix) {
        try {
            $results = $this->db->select(
                "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE ?",
                [$prefix . '%']
            );
            
            $settings = [];
            foreach ($results as $result) {
                $key = str_replace($prefix, '', $result['setting_key']);
                $settings[$key] = $result['setting_value'];
                
                // حفظ في الكاش
                self::$cache[$result['setting_key']] = $result['setting_value'];
            }
            
            return $settings;
            
        } catch (Exception $e) {
            error_log("Get settings group error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * تعيين مجموعة إعدادات
     */
    public function setGroup($prefix, $settings) {
        try {
            $success = true;
            
            foreach ($settings as $key => $value) {
                $fullKey = $prefix . $key;
                if (!$this->set($fullKey, $value)) {
                    $success = false;
                }
            }
            
            return $success;
            
        } catch (Exception $e) {
            error_log("Set settings group error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getAllSettings() {
        try {
            $results = $this->db->select(
                "SELECT * FROM settings ORDER BY setting_key"
            );
            
            $settings = [];
            foreach ($results as $result) {
                $settings[$result['setting_key']] = $result;
                
                // حفظ في الكاش
                self::$cache[$result['setting_key']] = $result['setting_value'];
            }
            
            return $settings;
            
        } catch (Exception $e) {
            error_log("Get all settings error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * حذف إعداد
     */
    public function delete($key) {
        try {
            $result = $this->db->delete(
                "DELETE FROM settings WHERE setting_key = ?",
                [$key]
            );
            
            if ($result) {
                // حذف من الكاش
                unset(self::$cache[$key]);
                
                logActivity('حذف إعداد', "تم حذف الإعداد: {$key}");
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Delete setting error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * مسح الكاش
     */
    public static function clearCache() {
        self::$cache = [];
    }
    
    /**
     * الحصول على إعدادات الشركة
     */
    public function getCompanySettings() {
        return $this->getGroup('company_');
    }
    
    /**
     * تحديث إعدادات الشركة
     */
    public function updateCompanySettings($settings) {
        return $this->setGroup('company_', $settings);
    }
    
    /**
     * الحصول على إعدادات النظام
     */
    public function getSystemSettings() {
        return $this->getGroup('system_');
    }
    
    /**
     * تحديث إعدادات النظام
     */
    public function updateSystemSettings($settings) {
        return $this->setGroup('system_', $settings);
    }
    
    /**
     * الحصول على إعدادات العملة
     */
    public function getCurrencySettings() {
        return $this->getGroup('currency_');
    }
    
    /**
     * تحديث إعدادات العملة
     */
    public function updateCurrencySettings($settings) {
        return $this->setGroup('currency_', $settings);
    }
    
    /**
     * الحصول على إعدادات الضرائب
     */
    public function getTaxSettings() {
        return $this->getGroup('tax_');
    }
    
    /**
     * تحديث إعدادات الضرائب
     */
    public function updateTaxSettings($settings) {
        return $this->setGroup('tax_', $settings);
    }
    
    /**
     * الحصول على إعدادات الفواتير
     */
    public function getInvoiceSettings() {
        return $this->getGroup('invoice_');
    }
    
    /**
     * تحديث إعدادات الفواتير
     */
    public function updateInvoiceSettings($settings) {
        return $this->setGroup('invoice_', $settings);
    }
    
    /**
     * تهيئة الإعدادات الافتراضية
     */
    public function initializeDefaultSettings() {
        $defaultSettings = [
            // إعدادات الشركة
            'company_name' => 'اسم الشركة',
            'company_address' => 'عنوان الشركة',
            'company_phone' => 'هاتف الشركة',
            'company_email' => '<EMAIL>',
            'company_website' => 'www.company.com',
            'company_tax_number' => 'الرقم الضريبي',
            'company_commercial_register' => 'السجل التجاري',
            'company_logo' => '',
            
            // إعدادات النظام
            'system_timezone' => 'Asia/Riyadh',
            'system_date_format' => 'Y-m-d',
            'system_time_format' => 'H:i:s',
            'system_language' => 'ar',
            'system_items_per_page' => '20',
            'system_session_timeout' => '3600',
            'system_backup_auto' => '1',
            'system_maintenance_mode' => '0',
            
            // إعدادات العملة
            'currency_code' => 'SAR',
            'currency_symbol' => 'ريال',
            'currency_position' => 'after',
            'currency_decimal_places' => '2',
            'currency_thousands_separator' => ',',
            'currency_decimal_separator' => '.',
            
            // إعدادات الضرائب
            'tax_enabled' => '1',
            'tax_default_rate' => '15',
            'tax_inclusive' => '0',
            'tax_number_required' => '1',
            
            // إعدادات الفواتير
            'invoice_prefix_sales' => 'INV',
            'invoice_prefix_purchase' => 'PUR',
            'invoice_auto_number' => '1',
            'invoice_terms' => 'شروط وأحكام الفاتورة',
            'invoice_footer' => 'شكراً لتعاملكم معنا',
            'invoice_due_days' => '30',
            'invoice_late_fee' => '0'
        ];
        
        foreach ($defaultSettings as $key => $value) {
            // تحقق من عدم وجود الإعداد مسبقاً
            if ($this->get($key) === null) {
                $this->set($key, $value);
            }
        }
        
        return true;
    }
    
    /**
     * تصدير الإعدادات
     */
    public function exportSettings() {
        try {
            $settings = $this->getAllSettings();
            
            $export = [];
            foreach ($settings as $key => $setting) {
                $export[$key] = [
                    'value' => $setting['setting_value'],
                    'description' => $setting['setting_description']
                ];
            }
            
            return json_encode($export, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            error_log("Export settings error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * استيراد الإعدادات
     */
    public function importSettings($jsonData) {
        try {
            $settings = json_decode($jsonData, true);
            
            if (!$settings) {
                return false;
            }
            
            $success = true;
            foreach ($settings as $key => $data) {
                $value = $data['value'] ?? $data;
                $description = $data['description'] ?? null;
                
                if (!$this->set($key, $value, $description)) {
                    $success = false;
                }
            }
            
            if ($success) {
                logActivity('استيراد إعدادات', 'تم استيراد الإعدادات بنجاح');
            }
            
            return $success;
            
        } catch (Exception $e) {
            error_log("Import settings error: " . $e->getMessage());
            return false;
        }
    }
}

?>

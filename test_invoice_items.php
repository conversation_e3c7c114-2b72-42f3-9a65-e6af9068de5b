<?php
/**
 * اختبار نظام إدارة عناصر الفاتورة
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار نظام إدارة عناصر الفاتورة</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f2f2f2; }
    .btn { display: inline-block; padding: 8px 16px; margin: 4px; text-decoration: none; border-radius: 4px; color: white; }
    .btn-primary { background-color: #007bff; }
    .btn-success { background-color: #28a745; }
    .btn-warning { background-color: #ffc107; color: black; }
    .btn-danger { background-color: #dc3545; }
</style>";

try {
    echo "<h2>1. فحص الجلسة والعناصر المحفوظة:</h2>";
    
    $invoiceItems = $_SESSION['invoice_items'] ?? [];
    echo "<p class='info'>عدد العناصر في الجلسة: " . count($invoiceItems) . "</p>";
    
    if (!empty($invoiceItems)) {
        echo "<table>";
        echo "<tr><th>اسم العنصر</th><th>الكمية</th><th>سعر الوحدة</th><th>الخصم</th><th>الإجمالي</th></tr>";
        
        $subtotal = 0;
        $totalDiscount = 0;
        
        foreach ($invoiceItems as $index => $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['item_name']) . "</td>";
            echo "<td>" . number_format($item['quantity'], 2) . "</td>";
            echo "<td>" . number_format($item['unit_price'], 2) . "</td>";
            echo "<td>" . number_format($item['discount_amount'], 2) . "</td>";
            echo "<td>" . number_format($item['total_price'], 2) . "</td>";
            echo "</tr>";
            
            $subtotal += $item['quantity'] * $item['unit_price'];
            $totalDiscount += $item['discount_amount'];
        }
        
        echo "<tr style='background-color: #f9f9f9; font-weight: bold;'>";
        echo "<td colspan='4'>الإجماليات</td>";
        echo "<td>" . number_format($subtotal - $totalDiscount, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<p class='info'>المجموع الفرعي: " . number_format($subtotal, 2) . " ريال</p>";
        echo "<p class='info'>إجمالي الخصم: " . number_format($totalDiscount, 2) . " ريال</p>";
        echo "<p class='info'>الصافي: " . number_format($subtotal - $totalDiscount, 2) . " ريال</p>";
        
    } else {
        echo "<p class='warning'>لا توجد عناصر محفوظة في الجلسة</p>";
    }
    
    echo "<h2>2. إضافة عناصر تجريبية:</h2>";
    
    if (isset($_POST['add_sample_items'])) {
        $sampleItems = [
            [
                'item_name' => 'خدمة استشارية',
                'quantity' => 2,
                'unit_price' => 500.00,
                'discount_amount' => 50.00,
                'total_price' => 950.00
            ],
            [
                'item_name' => 'تصميم موقع إلكتروني',
                'quantity' => 1,
                'unit_price' => 2000.00,
                'discount_amount' => 0.00,
                'total_price' => 2000.00
            ],
            [
                'item_name' => 'صيانة نظام',
                'quantity' => 3,
                'unit_price' => 150.00,
                'discount_amount' => 25.00,
                'total_price' => 425.00
            ]
        ];
        
        $_SESSION['invoice_items'] = $sampleItems;
        echo "<p class='success'>✅ تم إضافة " . count($sampleItems) . " عنصر تجريبي</p>";
        echo "<script>window.location.reload();</script>";
    }
    
    if (isset($_POST['clear_all_items'])) {
        $_SESSION['invoice_items'] = [];
        echo "<p class='info'>تم مسح جميع العناصر</p>";
        echo "<script>window.location.reload();</script>";
    }
    
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<button type='submit' name='add_sample_items' class='btn btn-success'>إضافة عناصر تجريبية</button>";
    echo "<button type='submit' name='clear_all_items' class='btn btn-danger' onclick='return confirm(\"هل أنت متأكد؟\")'>مسح جميع العناصر</button>";
    echo "</form>";
    
    echo "<h2>3. فحص العملاء المتاحين:</h2>";
    
    $db = Database::getInstance();
    $customers = $db->select("SELECT customer_id, customer_name, customer_type FROM customers LIMIT 5");
    
    if (!empty($customers)) {
        echo "<p class='success'>✅ العملاء متوفرون (" . count($customers) . ")</p>";
        echo "<table>";
        echo "<tr><th>المعرف</th><th>الاسم</th><th>النوع</th></tr>";
        foreach ($customers as $customer) {
            echo "<tr>";
            echo "<td>" . $customer['customer_id'] . "</td>";
            echo "<td>" . htmlspecialchars($customer['customer_name']) . "</td>";
            echo "<td>" . $customer['customer_type'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ لا توجد عملاء</p>";
        echo "<p><a href='add_sample_customers.php' class='btn btn-primary'>إضافة عملاء تجريبيين</a></p>";
    }
    
    echo "<h2>4. اختبار إنشاء فاتورة مع العناصر:</h2>";
    
    if (!empty($invoiceItems) && !empty($customers)) {
        if (isset($_POST['create_test_invoice'])) {
            try {
                $invoiceModel = new Invoice();
                
                // حساب الإجماليات
                $subtotal = 0;
                $totalDiscount = 0;
                foreach ($invoiceItems as $item) {
                    $subtotal += $item['quantity'] * $item['unit_price'];
                    $totalDiscount += $item['discount_amount'];
                }
                
                $taxAmount = 75.00; // ضريبة تجريبية
                $totalAmount = $subtotal - $totalDiscount + $taxAmount;
                
                // بيانات الفاتورة
                $invoiceData = [
                    'invoice_number' => 'TEST-ITEMS-' . time(),
                    'invoice_type' => 'sales',
                    'customer_id' => $customers[0]['customer_id'],
                    'invoice_date' => date('Y-m-d'),
                    'due_date' => date('Y-m-d', strtotime('+30 days')),
                    'total_amount' => $totalAmount,
                    'tax_amount' => $taxAmount,
                    'discount_amount' => $totalDiscount,
                    'notes' => 'فاتورة اختبار مع عناصر متعددة',
                    'status' => 'draft'
                ];
                
                echo "<p class='info'>بيانات الفاتورة:</p>";
                echo "<pre>" . print_r($invoiceData, true) . "</pre>";
                
                echo "<p class='info'>عناصر الفاتورة:</p>";
                echo "<pre>" . print_r($invoiceItems, true) . "</pre>";
                
                // إنشاء الفاتورة
                $newInvoiceId = $invoiceModel->createInvoice($invoiceData, $invoiceItems);
                
                if ($newInvoiceId) {
                    echo "<p class='success'>✅ تم إنشاء الفاتورة بنجاح! معرف الفاتورة: $newInvoiceId</p>";
                    echo "<p><a href='invoices.php?action=view&id=$newInvoiceId' class='btn btn-primary' target='_blank'>عرض الفاتورة</a></p>";
                    
                    // مسح العناصر من الجلسة
                    unset($_SESSION['invoice_items']);
                    echo "<p class='info'>تم مسح العناصر من الجلسة</p>";
                    
                } else {
                    echo "<p class='error'>❌ فشل في إنشاء الفاتورة</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        } else {
            echo "<form method='POST'>";
            echo "<button type='submit' name='create_test_invoice' class='btn btn-success'>إنشاء فاتورة اختبار مع العناصر</button>";
            echo "</form>";
        }
    } else {
        echo "<p class='warning'>⚠️ تحتاج إلى عناصر وعملاء لإنشاء فاتورة اختبار</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>الروابط المفيدة:</h2>";
echo "<p>";
echo "<a href='invoices.php?action=add&type=sales' class='btn btn-primary'>إنشاء فاتورة مبيعات</a>";
echo "<a href='invoices.php?action=add&type=purchase' class='btn btn-primary'>إنشاء فاتورة مشتريات</a>";
echo "<a href='invoices.php' class='btn btn-warning'>إدارة الفواتير</a>";
echo "<a href='add_sample_customers.php' class='btn btn-success'>إضافة عملاء</a>";
echo "</p>";
?>

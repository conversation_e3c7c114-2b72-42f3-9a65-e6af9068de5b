<?php
/**
 * صفحة إدارة الفواتير
 * Invoices Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('invoices_manage');

// إنشاء مثيلات النماذج
$invoiceModel = new Invoice();
$customerModel = new Customer();
$warehouseModel = new Warehouse();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$invoiceId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة إضافة/حذف عناصر الفاتورة
if ($action === 'add' && isset($_POST['manage_items'])) {
    // إدارة العناصر في الجلسة
    if (!isset($_SESSION['invoice_items'])) {
        $_SESSION['invoice_items'] = [];
    }

    if (isset($_POST['add_item'])) {
        // إضافة عنصر جديد
        $newItem = [
            'item_name' => $_POST['item_name'] ?? '',
            'quantity' => floatval($_POST['quantity'] ?? 1),
            'unit_price' => floatval($_POST['unit_price'] ?? 0),
            'discount_amount' => floatval($_POST['discount_amount'] ?? 0)
        ];

        if (!empty($newItem['item_name']) && $newItem['quantity'] > 0 && $newItem['unit_price'] >= 0) {
            $newItem['total_price'] = ($newItem['quantity'] * $newItem['unit_price']) - $newItem['discount_amount'];
            $_SESSION['invoice_items'][] = $newItem;
            setAlert('تم إضافة العنصر بنجاح', 'success');
        } else {
            setAlert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح', 'error');
        }
    }

    if (isset($_POST['remove_item'])) {
        // حذف عنصر
        $itemIndex = intval($_POST['item_index']);
        if (isset($_SESSION['invoice_items'][$itemIndex])) {
            unset($_SESSION['invoice_items'][$itemIndex]);
            $_SESSION['invoice_items'] = array_values($_SESSION['invoice_items']); // إعادة ترقيم المصفوفة
            setAlert('تم حذف العنصر بنجاح', 'success');
        }
    }

    if (isset($_POST['clear_items'])) {
        // مسح جميع العناصر
        $_SESSION['invoice_items'] = [];
        setAlert('تم مسح جميع العناصر', 'info');
    }
}

// معالجة إضافة فاتورة جديدة
if ($action === 'add' && $_SERVER['REQUEST_METHOD'] === 'POST' && (isset($_POST['save_draft']) || isset($_POST['save_and_post']))) {
    $csrfToken = $_POST['csrf_token'] ?? '';

    if (verifyCSRFToken($csrfToken)) {
        $invoiceType = $_POST['invoice_type'] ?? 'sales';

        $data = [
            'invoice_number' => sanitizeInput($_POST['invoice_number'] ?? ''),
            'invoice_type' => $invoiceType,
            'customer_id' => intval($_POST['customer_id'] ?? 0),
            'invoice_date' => $_POST['invoice_date'] ?? date('Y-m-d'),
            'due_date' => $_POST['due_date'] ?? null,
            'total_amount' => floatval($_POST['total_amount'] ?? 0),
            'tax_amount' => floatval($_POST['tax_amount'] ?? 0),
            'notes' => sanitizeInput($_POST['notes'] ?? ''),
            'status' => isset($_POST['save_draft']) ? 'draft' : 'posted'
        ];

        // التحقق من وجود عناصر في الفاتورة
        $invoiceItems = $_SESSION['invoice_items'] ?? [];
        if (empty($invoiceItems)) {
            $error = 'يجب إضافة عنصر واحد على الأقل للفاتورة';
        } elseif (empty($data['invoice_number']) || $data['customer_id'] <= 0) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                // حساب الإجماليات من العناصر
                $subtotal = 0;
                $totalDiscount = 0;
                foreach ($invoiceItems as $item) {
                    $subtotal += $item['quantity'] * $item['unit_price'];
                    $totalDiscount += $item['discount_amount'];
                }

                // تحديث بيانات الفاتورة بالإجماليات المحسوبة
                $data['total_amount'] = $subtotal - $totalDiscount + $data['tax_amount'];
                $data['discount_amount'] = $totalDiscount;

                $newInvoiceId = $invoiceModel->createInvoice($data, $invoiceItems);
                if ($newInvoiceId) {
                    // مسح العناصر من الجلسة بعد الحفظ الناجح
                    unset($_SESSION['invoice_items']);

                    $statusText = $data['status'] === 'draft' ? 'كمسودة' : 'وتم ترحيلها';
                    setAlert("تم إنشاء الفاتورة بنجاح {$statusText}", 'success');
                    redirect("invoices.php?action=view&id={$newInvoiceId}");
                } else {
                    $error = 'فشل في إنشاء الفاتورة';
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// معالجة المرشحات
$typeFilter = sanitizeInput($_GET['type'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$customerFilter = !empty($_GET['customer']) ? (int)$_GET['customer'] : null;
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$invoicesPerPage = 50;
$offset = ($page - 1) * $invoicesPerPage;

// الحصول على الفواتير
if (!empty($searchTerm)) {
    $invoices = $invoiceModel->searchInvoices($searchTerm, $typeFilter, $dateFrom, $dateTo);
    $totalInvoices = count($invoices);
} else {
    $invoices = $invoiceModel->getAllInvoices($typeFilter, $statusFilter, $dateFrom, $dateTo, $invoicesPerPage, $offset);
    
    // حساب إجمالي الفواتير للترقيم
    $allInvoices = $invoiceModel->getAllInvoices($typeFilter, $statusFilter, $dateFrom, $dateTo);
    $totalInvoices = count($allInvoices);
}

$totalPages = ceil($totalInvoices / $invoicesPerPage);

// الحصول على قوائم المساعدة
$customers = $customerModel->getAllCustomers();
$warehouses = $warehouseModel->getAllWarehouses();

// الحصول على إحصائيات الفواتير
$salesStats = $invoiceModel->getInvoicesStats('sales', $dateFrom, $dateTo);
$purchaseStats = $invoiceModel->getInvoicesStats('purchase', $dateFrom, $dateTo);

// الحصول على بيانات الفاتورة المحددة (في حالة العرض)
$currentInvoice = null;
$invoiceItems = [];
if ($action === 'view' && $invoiceId) {
    $currentInvoice = $invoiceModel->getInvoiceById($invoiceId);
    if (!$currentInvoice) {
        setAlert('الفاتورة غير موجودة', 'error');
        redirect('invoices.php');
    }

    // تحميل عناصر الفاتورة
    try {
        $invoiceItems = $invoiceModel->getInvoiceItems($invoiceId);
    } catch (Exception $e) {
        $invoiceItems = [];
        error_log("Error loading invoice items: " . $e->getMessage());
    }
}

$pageTitle = 'إدارة الفواتير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض الفواتير */
        .invoice-type-sales { border-right: 4px solid #10b981; }
        .invoice-type-purchase { border-right: 4px solid #3b82f6; }
        
        .status-draft { background-color: #fef3c7; color: #92400e; }
        .status-posted { background-color: #d1fae5; color: #065f46; }
        .status-cancelled { background-color: #fee2e2; color: #991b1b; }
        
        .payment-paid { background-color: #d1fae5; color: #065f46; }
        .payment-unpaid { background-color: #fee2e2; color: #991b1b; }
        .payment-partial { background-color: #fef3c7; color: #92400e; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-file-invoice ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">

        <!-- عرض الرسائل -->
        <?php showAlert(); ?>

        <?php if ($action === 'list' || $action === ''): ?>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- فواتير المبيعات -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-shopping-cart text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">فواتير المبيعات</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($salesStats['total_invoices']); ?>
                                </dd>
                                <dd class="text-sm text-gray-600">
                                    <?php echo formatMoney($salesStats['total_amount']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- فواتير المشتريات -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-truck text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">فواتير المشتريات</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($purchaseStats['total_invoices']); ?>
                                </dd>
                                <dd class="text-sm text-gray-600">
                                    <?php echo formatMoney($purchaseStats['total_amount']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المبالغ المستحقة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">مبالغ مستحقة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($salesStats['unpaid_invoices'] + $purchaseStats['unpaid_invoices']); ?>
                                </dd>
                                <dd class="text-sm text-gray-600">
                                    <?php echo formatMoney($salesStats['unpaid_amount'] + $purchaseStats['unpaid_amount']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المبالغ المدفوعة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">مبالغ مدفوعة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($salesStats['paid_invoices'] + $purchaseStats['paid_invoices']); ?>
                                </dd>
                                <dd class="text-sm text-gray-600">
                                    <?php echo formatMoney($salesStats['paid_amount'] + $purchaseStats['paid_amount']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- قائمة الفواتير -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس القائمة -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    قائمة الفواتير (<?php echo number_format($totalInvoices); ?> فاتورة)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="customers.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-users ml-2"></i>
                        العملاء
                    </a>
                    <a href="invoices.php?action=add&type=purchase" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        فاتورة مشتريات
                    </a>
                    <a href="invoices.php?action=add&type=sales" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        فاتورة مبيعات
                    </a>
                </div>
            </div>

            <!-- شريط البحث والفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <form method="GET" action="invoices.php" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div>
                        <input
                            type="text"
                            name="search"
                            value="<?php echo htmlspecialchars($searchTerm); ?>"
                            placeholder="البحث (رقم الفاتورة، العميل)"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="sales" <?php echo $typeFilter === 'sales' ? 'selected' : ''; ?>>مبيعات</option>
                            <option value="purchase" <?php echo $typeFilter === 'purchase' ? 'selected' : ''; ?>>مشتريات</option>
                        </select>
                    </div>
                    <div>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="draft" <?php echo $statusFilter === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                            <option value="posted" <?php echo $statusFilter === 'posted' ? 'selected' : ''; ?>>مرحلة</option>
                            <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    <div>
                        <select name="customer" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['customer_id']; ?>"
                                        <?php echo $customerFilter == $customer['customer_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['customer_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <input
                            type="date"
                            name="date_from"
                            value="<?php echo htmlspecialchars($dateFrom); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="من تاريخ"
                        >
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <input
                            type="date"
                            name="date_to"
                            value="<?php echo htmlspecialchars($dateTo); ?>"
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="إلى تاريخ"
                        >
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($searchTerm) || !empty($typeFilter) || !empty($statusFilter) || $customerFilter || !empty($dateFrom) || !empty($dateTo)): ?>
                            <a href="invoices.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <!-- جدول الفواتير -->
            <div class="overflow-x-auto">
                <?php if (empty($invoices)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-file-invoice text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير</h3>
                        <p class="text-gray-500 mb-4">لم يتم العثور على أي فواتير مطابقة للبحث</p>
                        <div class="flex justify-center space-x-2 space-x-reverse">
                            <a href="invoices.php?action=add&type=sales" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                فاتورة مبيعات
                            </a>
                            <a href="invoices.php?action=add&type=purchase" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                فاتورة مشتريات
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الفاتورة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ الإجمالي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حالة الفاتورة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حالة الدفع</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($invoices as $invoice): ?>
                                <tr class="hover:bg-gray-50 invoice-type-<?php echo $invoice['invoice_type']; ?>">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($invoice['invoice_number']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo formatDate($invoice['created_at']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($invoice['invoice_type'] === 'sales'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                مبيعات
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                مشتريات
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo htmlspecialchars($invoice['customer_name']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($invoice['customer_code']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatDate($invoice['invoice_date']); ?>
                                        <?php if (!empty($invoice['due_date'])): ?>
                                            <div class="text-xs text-gray-500">
                                                استحقاق: <?php echo formatDate($invoice['due_date']); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo formatMoney($invoice['total_amount']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-<?php echo $invoice['status']; ?>">
                                            <?php
                                            $statusLabels = [
                                                'draft' => 'مسودة',
                                                'posted' => 'مرحلة',
                                                'cancelled' => 'ملغية'
                                            ];
                                            echo $statusLabels[$invoice['status']] ?? $invoice['status'];
                                            ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full payment-<?php echo $invoice['payment_status']; ?>">
                                            <?php
                                            $paymentLabels = [
                                                'paid' => 'مدفوعة',
                                                'unpaid' => 'غير مدفوعة',
                                                'partial' => 'مدفوعة جزئياً'
                                            ];
                                            echo $paymentLabels[$invoice['payment_status']] ?? $invoice['payment_status'];
                                            ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="invoices.php?action=view&id=<?php echo $invoice['invoice_id']; ?>"
                                               class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                               title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="print_invoice.php?id=<?php echo $invoice['invoice_id']; ?>"
                                               class="text-green-600 hover:text-green-900 transition-colors duration-200"
                                               title="طباعة" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <?php if ($invoice['status'] === 'draft'): ?>
                                                <a href="invoices.php?action=edit&id=<?php echo $invoice['invoice_id']; ?>"
                                                   class="text-yellow-600 hover:text-yellow-900 transition-colors duration-200"
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="invoices.php?action=delete&id=<?php echo $invoice['invoice_id']; ?>"
                                                   onclick="return confirm('هل أنت متأكد من حذف الفاتورة \'<?php echo htmlspecialchars($invoice['invoice_number'], ENT_QUOTES); ?>\'؟')"
                                                   class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                   title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

        </div>

        <?php endif; ?>

        <?php if ($action === 'add'): ?>

            <!-- نموذج إضافة فاتورة جديدة -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة فاتورة <?php echo ($_GET['type'] ?? 'sales') === 'sales' ? 'مبيعات' : 'مشتريات'; ?> جديدة
                    </h3>
                </div>

                <?php if ($error): ?>
                    <div class="mx-6 mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- معلومات تشخيصية (يمكن إزالتها لاحقاً) -->
                <?php if (isset($_GET['debug'])): ?>
                    <div class="mx-6 mt-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded text-sm">
                        <h4 class="font-bold mb-2">معلومات تشخيصية:</h4>
                        <?php
                        try {
                            $debugCustomers = $customerModel->getAllCustomers();
                            echo "<p>إجمالي العملاء: " . count($debugCustomers) . "</p>";

                            if (!empty($debugCustomers)) {
                                $types = array_count_values(array_column($debugCustomers, 'customer_type'));
                                echo "<p>الأنواع: ";
                                foreach ($types as $type => $count) {
                                    echo "$type ($count) ";
                                }
                                echo "</p>";

                                echo "<p>أول 3 عملاء:</p><ul>";
                                foreach (array_slice($debugCustomers, 0, 3) as $c) {
                                    echo "<li>" . $c['customer_name'] . " (" . $c['customer_type'] . ")</li>";
                                }
                                echo "</ul>";
                            }
                        } catch (Exception $e) {
                            echo "<p>خطأ في التشخيص: " . $e->getMessage() . "</p>";
                        }
                        ?>
                        <p class="mt-2"><a href="?action=add&type=<?php echo $_GET['type'] ?? 'sales'; ?>" class="text-blue-600">إخفاء التشخيص</a></p>
                    </div>
                <?php else: ?>
                    <div class="mx-6 mt-4 p-2 bg-gray-100 text-gray-600 text-sm rounded">
                        <a href="?action=add&type=<?php echo $_GET['type'] ?? 'sales'; ?>&debug=1" class="text-blue-600">عرض معلومات التشخيص</a>
                    </div>
                <?php endif; ?>

                <form method="POST" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="invoice_type" value="<?php echo htmlspecialchars($_GET['type'] ?? 'sales'); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- معلومات أساسية -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2">المعلومات الأساسية</h4>

                            <!-- رقم الفاتورة -->
                            <div>
                                <label for="invoice_number" class="block text-sm font-medium text-gray-700 mb-1">
                                    رقم الفاتورة *
                                </label>
                                <input type="text"
                                       id="invoice_number"
                                       name="invoice_number"
                                       value="<?php echo 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- تاريخ الفاتورة -->
                            <div>
                                <label for="invoice_date" class="block text-sm font-medium text-gray-700 mb-1">
                                    تاريخ الفاتورة *
                                </label>
                                <input type="date"
                                       id="invoice_date"
                                       name="invoice_date"
                                       value="<?php echo date('Y-m-d'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- تاريخ الاستحقاق -->
                            <div>
                                <label for="due_date" class="block text-sm font-medium text-gray-700 mb-1">
                                    تاريخ الاستحقاق
                                </label>
                                <input type="date"
                                       id="due_date"
                                       name="due_date"
                                       value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- معلومات العميل/المورد -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2">
                                <?php echo ($_GET['type'] ?? 'sales') === 'sales' ? 'معلومات العميل' : 'معلومات المورد'; ?>
                            </h4>

                            <!-- اختيار العميل/المورد -->
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-1">
                                    <?php echo ($_GET['type'] ?? 'sales') === 'sales' ? 'العميل *' : 'المورد *'; ?>
                                </label>
                                <select id="customer_id"
                                        name="customer_id"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">اختر...</option>
                                    <?php
                                    $invoiceType = $_GET['type'] ?? 'sales';
                                    $customerType = $invoiceType === 'sales' ? 'customer' : 'supplier';

                                    try {
                                        // التحقق من وجود نموذج العميل
                                        if (class_exists('Customer')) {
                                            // أولاً جرب النوع المحدد
                                            $availableCustomers = $customerModel->getAllCustomers($customerType);

                                            // إذا لم توجد، جرب النوع "both"
                                            if (empty($availableCustomers)) {
                                                $availableCustomers = $customerModel->getAllCustomers('both');
                                            }

                                            // إذا لم توجد، اعرض جميع العملاء
                                            if (empty($availableCustomers)) {
                                                $availableCustomers = $customerModel->getAllCustomers();
                                            }

                                            if (!empty($availableCustomers)) {
                                                foreach ($availableCustomers as $customer):
                                                    // تصفية العملاء حسب النوع المناسب
                                                    $showCustomer = false;
                                                    if ($invoiceType === 'sales' && in_array($customer['customer_type'], ['customer', 'both'])) {
                                                        $showCustomer = true;
                                                    } elseif ($invoiceType === 'purchase' && in_array($customer['customer_type'], ['supplier', 'both'])) {
                                                        $showCustomer = true;
                                                    }

                                                    if ($showCustomer):
                                    ?>
                                                        <option value="<?php echo $customer['customer_id']; ?>">
                                                            <?php echo htmlspecialchars($customer['customer_name']); ?>
                                                            (<?php echo htmlspecialchars($customer['customer_code']); ?>)
                                                            - <?php echo $customer['customer_type'] === 'both' ? 'عميل ومورد' : ($customer['customer_type'] === 'customer' ? 'عميل' : 'مورد'); ?>
                                                        </option>
                                    <?php
                                                    endif;
                                                endforeach;
                                            } else {
                                                echo '<option value="">لا توجد ' . ($customerType === 'customer' ? 'عملاء' : 'موردين') . ' متاحين</option>';
                                                echo '<option value="" disabled>--- تشخيص ---</option>';

                                                // عرض معلومات تشخيصية
                                                $allCustomers = $customerModel->getAllCustomers();
                                                echo '<option value="" disabled>إجمالي العملاء: ' . count($allCustomers) . '</option>';

                                                if (!empty($allCustomers)) {
                                                    $types = array_count_values(array_column($allCustomers, 'customer_type'));
                                                    foreach ($types as $type => $count) {
                                                        echo '<option value="" disabled>' . $type . ': ' . $count . '</option>';
                                                    }
                                                }
                                            }
                                        } else {
                                            echo '<option value="">نموذج العميل غير متاح</option>';
                                        }
                                    } catch (Exception $e) {
                                        echo '<option value="">خطأ: ' . htmlspecialchars($e->getMessage()) . '</option>';
                                        error_log("Customer loading error: " . $e->getMessage());
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- عرض الإجماليات (للقراءة فقط) -->
                            <?php
                            $invoiceItems = $_SESSION['invoice_items'] ?? [];
                            $subtotal = 0;
                            $totalDiscount = 0;
                            foreach ($invoiceItems as $item) {
                                $subtotal += $item['quantity'] * $item['unit_price'];
                                $totalDiscount += $item['discount_amount'];
                            }
                            ?>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h5 class="font-medium text-gray-700 mb-2">ملخص الفاتورة</h5>
                                <div class="space-y-1 text-sm">
                                    <div class="flex justify-between">
                                        <span>المجموع الفرعي:</span>
                                        <span><?php echo number_format($subtotal, 2); ?> ريال</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>إجمالي الخصم:</span>
                                        <span><?php echo number_format($totalDiscount, 2); ?> ريال</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>الضريبة:</span>
                                        <span><?php echo number_format(floatval($_POST['tax_amount'] ?? 0), 2); ?> ريال</span>
                                    </div>
                                    <div class="flex justify-between font-bold text-lg border-t pt-1">
                                        <span>الإجمالي النهائي:</span>
                                        <span><?php echo number_format($subtotal - $totalDiscount + floatval($_POST['tax_amount'] ?? 0), 2); ?> ريال</span>
                                    </div>
                                </div>
                            </div>

                            <!-- الضريبة -->
                            <form method="POST" class="inline">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="update_totals" value="1">
                            <div>
                                <label for="tax_amount" class="block text-sm font-medium text-gray-700 mb-1">
                                    مبلغ الضريبة
                                </label>
                                <input type="number"
                                       id="tax_amount"
                                       name="tax_amount"
                                       step="0.01"
                                       min="0"
                                       value="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button type="submit"
                                        class="mt-2 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200">
                                    تحديث الإجماليات
                                </button>
                            </form>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الفاتورة -->
                    <div class="mt-8">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-md font-medium text-gray-800">عناصر الفاتورة</h4>
                            <button type="button"
                                    id="addItemBtn"
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة عنصر
                            </button>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم العنصر</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر الوحدة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الخصم</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody" class="divide-y divide-gray-200">
                                    <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                </tbody>
                                <tfoot class="bg-gray-50">
                                    <tr>
                                        <td colspan="4" class="px-4 py-3 text-right font-medium text-gray-700">المجموع الفرعي:</td>
                                        <td class="px-4 py-3 text-right font-medium text-gray-900" id="subtotalDisplay">0.00</td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" class="px-4 py-3 text-right font-medium text-gray-700">الضريبة:</td>
                                        <td class="px-4 py-3 text-right font-medium text-gray-900" id="taxDisplay">0.00</td>
                                        <td></td>
                                    </tr>
                                    <tr class="border-t-2 border-gray-300">
                                        <td colspan="4" class="px-4 py-3 text-right font-bold text-gray-800">الإجمالي النهائي:</td>
                                        <td class="px-4 py-3 text-right font-bold text-gray-900" id="totalDisplay">0.00</td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- رسالة عدم وجود عناصر -->
                        <div id="noItemsMessage" class="text-center py-8 text-gray-500">
                            <i class="fas fa-box-open text-4xl mb-2"></i>
                            <p>لا توجد عناصر في الفاتورة</p>
                            <p class="text-sm">اضغط على "إضافة عنصر" لبدء إضافة العناصر</p>
                        </div>
                    </div>

                    <!-- إدارة عناصر الفاتورة -->
                    <div class="mt-8 border-t pt-6">
                        <h4 class="text-lg font-medium text-gray-800 mb-4">عناصر الفاتورة</h4>

                        <!-- نموذج إضافة عنصر جديد -->
                        <div class="bg-blue-50 p-4 rounded-lg mb-6">
                            <h5 class="font-medium text-blue-800 mb-3">إضافة عنصر جديد</h5>
                            <form method="POST" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="manage_items" value="1">

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم العنصر *</label>
                                    <input type="text"
                                           name="item_name"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="مثال: خدمة استشارية"
                                           required>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الكمية *</label>
                                    <input type="number"
                                           name="quantity"
                                           step="0.01"
                                           min="0.01"
                                           value="1"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           required>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">سعر الوحدة *</label>
                                    <input type="number"
                                           name="unit_price"
                                           step="0.01"
                                           min="0"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="0.00"
                                           required>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الخصم</label>
                                    <input type="number"
                                           name="discount_amount"
                                           step="0.01"
                                           min="0"
                                           value="0"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="0.00">
                                </div>

                                <div class="flex items-end">
                                    <button type="submit"
                                            name="add_item"
                                            class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- عرض العناصر المضافة -->
                        <?php if (!empty($invoiceItems)): ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم العنصر</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر الوحدة</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الخصم</th>
                                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                                            <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <?php foreach ($invoiceItems as $index => $item): ?>
                                            <tr>
                                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo htmlspecialchars($item['item_name']); ?></td>
                                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['quantity'], 2); ?></td>
                                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['unit_price'], 2); ?></td>
                                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['discount_amount'], 2); ?></td>
                                                <td class="px-4 py-3 text-sm font-medium text-gray-900"><?php echo number_format($item['total_price'], 2); ?></td>
                                                <td class="px-4 py-3 text-center">
                                                    <form method="POST" class="inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <input type="hidden" name="manage_items" value="1">
                                                        <input type="hidden" name="item_index" value="<?php echo $index; ?>">
                                                        <button type="submit"
                                                                name="remove_item"
                                                                class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا العنصر؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- أزرار إدارة العناصر -->
                            <div class="mt-4 flex justify-end space-x-2 space-x-reverse">
                                <form method="POST" class="inline">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="manage_items" value="1">
                                    <button type="submit"
                                            name="clear_items"
                                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                                            onclick="return confirm('هل أنت متأكد من مسح جميع العناصر؟')">
                                        <i class="fas fa-trash-alt ml-2"></i>
                                        مسح جميع العناصر
                                    </button>
                                </form>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                                <i class="fas fa-box-open text-4xl mb-2"></i>
                                <p class="text-lg font-medium">لا توجد عناصر في الفاتورة</p>
                                <p class="text-sm">استخدم النموذج أعلاه لإضافة عناصر للفاتورة</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mt-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
                            ملاحظات
                        </label>
                        <textarea id="notes"
                                  name="notes"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- أزرار العمل -->
                    <div class="mt-6 flex justify-between">
                        <a href="invoices.php"
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>

                        <div class="space-x-2 space-x-reverse">
                            <button type="submit"
                                    name="save_draft"
                                    class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ كمسودة
                            </button>

                            <button type="submit"
                                    name="save_and_post"
                                    class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-check ml-2"></i>
                                حفظ وترحيل
                            </button>
                        </div>
                    </div>
                </form>
            </div>

        <?php endif; ?>

        <?php if ($action === 'view' && $invoiceId): ?>

            <!-- عرض تفاصيل الفاتورة -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- تفاصيل الفاتورة -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow-lg rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-medium text-gray-900">
                                    تفاصيل الفاتورة #<?php echo htmlspecialchars($currentInvoice['invoice_number']); ?>
                                </h3>
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="print_invoice.php?id=<?php echo $invoiceId; ?>"
                                       target="_blank"
                                       class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        <i class="fas fa-print ml-2"></i>
                                        طباعة
                                    </a>
                                    <a href="?action=edit&id=<?php echo $invoiceId; ?>"
                                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        <i class="fas fa-edit ml-2"></i>
                                        تحرير
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="p-6">
                            <!-- معلومات أساسية -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-500">معلومات الفاتورة</h4>
                                    <div class="mt-2 space-y-2">
                                        <p><strong>النوع:</strong> <?php echo $currentInvoice['invoice_type'] === 'sales' ? 'مبيعات' : 'مشتريات'; ?></p>
                                        <p><strong>التاريخ:</strong> <?php echo formatDate($currentInvoice['invoice_date']); ?></p>
                                        <?php if ($currentInvoice['due_date']): ?>
                                            <p><strong>تاريخ الاستحقاق:</strong> <?php echo formatDate($currentInvoice['due_date']); ?></p>
                                        <?php endif; ?>
                                        <p><strong>الحالة:</strong>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?php
                                                echo $currentInvoice['status'] === 'completed' ? 'bg-green-100 text-green-800' :
                                                    ($currentInvoice['status'] === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800');
                                                ?>">
                                                <?php
                                                $statusLabels = ['pending' => 'معلقة', 'completed' => 'مكتملة', 'cancelled' => 'ملغية'];
                                                echo $statusLabels[$currentInvoice['status']] ?? $currentInvoice['status'];
                                                ?>
                                            </span>
                                        </p>
                                    </div>
                                </div>

                                <div>
                                    <h4 class="text-sm font-medium text-gray-500">
                                        <?php echo $currentInvoice['invoice_type'] === 'sales' ? 'معلومات العميل' : 'معلومات المورد'; ?>
                                    </h4>
                                    <div class="mt-2 space-y-2">
                                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($currentInvoice['customer_name'] ?? $currentInvoice['supplier_name'] ?? 'غير محدد'); ?></p>
                                        <?php if (!empty($currentInvoice['customer_phone']) || !empty($currentInvoice['supplier_phone'])): ?>
                                            <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($currentInvoice['customer_phone'] ?? $currentInvoice['supplier_phone']); ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($currentInvoice['customer_email']) || !empty($currentInvoice['supplier_email'])): ?>
                                            <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($currentInvoice['customer_email'] ?? $currentInvoice['supplier_email']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- عناصر الفاتورة -->
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-500 mb-3">عناصر الفاتورة</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php if (!empty($invoiceItems)): ?>
                                                <?php foreach ($invoiceItems as $item): ?>
                                                    <tr>
                                                        <td class="px-6 py-4 text-sm text-gray-900"><?php echo htmlspecialchars($item['item_name'] ?? 'غير محدد'); ?></td>
                                                        <td class="px-6 py-4 text-sm text-gray-900"><?php echo number_format($item['quantity'] ?? 0, 2); ?></td>
                                                        <td class="px-6 py-4 text-sm text-gray-900"><?php echo formatMoney($item['unit_price'] ?? 0); ?></td>
                                                        <td class="px-6 py-4 text-sm text-gray-900"><?php echo formatMoney($item['total_price'] ?? 0); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="px-6 py-4 text-sm text-gray-500 text-center">
                                                        لا توجد عناصر في هذه الفاتورة
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- المجاميع -->
                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex justify-end">
                                    <div class="w-64 space-y-2">
                                        <div class="flex justify-between">
                                            <span>المجموع الفرعي:</span>
                                            <span><?php echo formatMoney($currentInvoice['subtotal']); ?></span>
                                        </div>
                                        <?php if ($currentInvoice['tax_amount'] > 0): ?>
                                            <div class="flex justify-between">
                                                <span>الضريبة:</span>
                                                <span><?php echo formatMoney($currentInvoice['tax_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($currentInvoice['discount_amount'] > 0): ?>
                                            <div class="flex justify-between">
                                                <span>الخصم:</span>
                                                <span><?php echo formatMoney($currentInvoice['discount_amount']); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <div class="flex justify-between font-bold text-lg border-t pt-2">
                                            <span>الإجمالي النهائي:</span>
                                            <span><?php echo formatMoney($currentInvoice['total_amount']); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($currentInvoice['notes'])): ?>
                                <div class="mt-6 pt-4 border-t border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-500 mb-2">ملاحظات</h4>
                                    <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($currentInvoice['notes'])); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- المرفقات -->
                <div class="lg:col-span-1">
                    <?php
                    $entityType = 'invoice';
                    $entityId = $invoiceId;
                    include 'includes/attachment_widget.php';
                    ?>
                </div>

            </div>

        <?php endif; ?>

    </div>

</body>
</html>

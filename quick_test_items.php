<?php
/**
 * اختبار سريع لوظيفة إضافة العناصر
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// مسح العناصر السابقة للاختبار النظيف
if (isset($_GET['clear'])) {
    unset($_SESSION['invoice_items']);
    header('Location: quick_test_items.php');
    exit;
}

$pageTitle = 'اختبار سريع لإضافة العناصر';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-4xl">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-vial mr-2"></i>
                اختبار سريع لوظيفة إضافة العناصر
            </h1>

            <!-- عرض حالة الجلسة -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">حالة الجلسة:</h3>
                <?php
                $sessionItems = $_SESSION['invoice_items'] ?? [];
                echo "<p>عدد العناصر المحفوظة: <strong>" . count($sessionItems) . "</strong></p>";
                echo "<p>معرف الجلسة: <code>" . session_id() . "</code></p>";
                ?>
            </div>

            <!-- عرض الرسائل -->
            <?php if (isset($_SESSION['alert'])): ?>
                <div class="mb-4 p-4 rounded <?php echo $_SESSION['alert']['type'] === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?>">
                    <?php 
                    echo $_SESSION['alert']['message']; 
                    unset($_SESSION['alert']);
                    ?>
                </div>
            <?php endif; ?>

            <!-- نموذج اختبار إضافة عنصر -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">اختبار إضافة عنصر:</h3>
                
                <form method="POST" action="invoices.php?action=add&type=sales" class="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-yellow-50 rounded-lg">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="manage_items" value="1">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم العنصر *</label>
                        <input type="text" 
                               name="item_name" 
                               value="خدمة اختبار <?php echo rand(1, 100); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الكمية *</label>
                        <input type="number" 
                               name="quantity" 
                               value="<?php echo rand(1, 5); ?>"
                               step="0.01" 
                               min="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">سعر الوحدة *</label>
                        <input type="number" 
                               name="unit_price" 
                               value="<?php echo rand(50, 500); ?>"
                               step="0.01" 
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الخصم</label>
                        <input type="number" 
                               name="discount_amount" 
                               value="<?php echo rand(0, 20); ?>"
                               step="0.01" 
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" 
                                name="add_item"
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            إضافة عنصر
                        </button>
                    </div>
                </form>
            </div>

            <!-- عرض العناصر المحفوظة -->
            <?php if (!empty($sessionItems)): ?>
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">العناصر المحفوظة:</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">#</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم العنصر</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر الوحدة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الخصم</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">حذف</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php 
                                $grandTotal = 0;
                                foreach ($sessionItems as $index => $item): 
                                    $grandTotal += $item['total_price'];
                                ?>
                                    <tr>
                                        <td class="px-4 py-3 text-sm text-gray-900"><?php echo $index + 1; ?></td>
                                        <td class="px-4 py-3 text-sm text-gray-900"><?php echo htmlspecialchars($item['item_name']); ?></td>
                                        <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['unit_price'], 2); ?></td>
                                        <td class="px-4 py-3 text-sm text-gray-900"><?php echo number_format($item['discount_amount'], 2); ?></td>
                                        <td class="px-4 py-3 text-sm font-medium text-gray-900"><?php echo number_format($item['total_price'], 2); ?></td>
                                        <td class="px-4 py-3 text-center">
                                            <form method="POST" action="invoices.php?action=add&type=sales" class="inline">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="manage_items" value="1">
                                                <input type="hidden" name="item_index" value="<?php echo $index; ?>">
                                                <button type="submit" 
                                                        name="remove_item"
                                                        class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                        onclick="return confirm('هل أنت متأكد من حذف هذا العنصر؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                <tr class="bg-gray-50 font-bold">
                                    <td colspan="5" class="px-4 py-3 text-right">الإجمالي:</td>
                                    <td class="px-4 py-3 text-right"><?php echo number_format($grandTotal, 2); ?></td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php else: ?>
                <div class="text-center py-8 text-gray-500 bg-gray-50 rounded-lg mb-6">
                    <i class="fas fa-box-open text-4xl mb-2"></i>
                    <p class="text-lg font-medium">لا توجد عناصر محفوظة</p>
                    <p class="text-sm">استخدم النموذج أعلاه لإضافة عناصر</p>
                </div>
            <?php endif; ?>

            <!-- أزرار التحكم -->
            <div class="text-center space-x-4 space-x-reverse">
                <a href="?clear=1" 
                   class="inline-block bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-200"
                   onclick="return confirm('هل أنت متأكد من مسح جميع العناصر؟')">
                    <i class="fas fa-trash-alt mr-2"></i>
                    مسح جميع العناصر
                </a>
                
                <a href="invoices.php?action=add&type=sales" 
                   class="inline-block bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-file-invoice mr-2"></i>
                    فتح نموذج الفاتورة
                </a>
                
                <a href="debug_invoice_items.php" 
                   class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-bug mr-2"></i>
                    تشخيص متقدم
                </a>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg text-sm text-gray-600">
                <h4 class="font-semibold mb-2">ملاحظات:</h4>
                <ul class="list-disc list-inside space-y-1">
                    <li>هذه الصفحة تختبر وظيفة إضافة العناصر مباشرة</li>
                    <li>العناصر يتم حفظها في الجلسة مؤقتاً</li>
                    <li>يمكنك إضافة عدة عناصر ثم الانتقال لنموذج الفاتورة</li>
                    <li>استخدم زر "مسح جميع العناصر" لبدء اختبار جديد</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

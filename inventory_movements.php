<?php
/**
 * إدارة حركات المخزون
 * Inventory Movements Management
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// إنشاء كائن المخزون
$inventoryModel = new Inventory();

// معالجة المتغيرات
$action = $_GET['action'] ?? 'list';
$movementId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة إضافة حركة جديدة
if ($action === 'add' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        try {
            $data = [
                'movement_date' => $_POST['movement_date'] ?? date('Y-m-d'),
                'movement_type' => $_POST['movement_type'] ?? 'in',
                'item_id' => intval($_POST['item_id'] ?? 0),
                'warehouse_id' => intval($_POST['warehouse_id'] ?? 0),
                'quantity' => floatval($_POST['quantity'] ?? 0),
                'unit_cost' => floatval($_POST['unit_cost'] ?? 0),
                'reference_type' => $_POST['reference_type'] ?? 'manual',
                'reference_number' => $_POST['reference_number'] ?? '',
                'from_warehouse_id' => intval($_POST['from_warehouse_id'] ?? 0) ?: null,
                'to_warehouse_id' => intval($_POST['to_warehouse_id'] ?? 0) ?: null,
                'notes' => $_POST['notes'] ?? '',
                'batch_number' => $_POST['batch_number'] ?? '',
                'expiry_date' => $_POST['expiry_date'] ?: null,
                'serial_numbers' => $_POST['serial_numbers'] ?? ''
            ];
            
            // التحقق من البيانات المطلوبة
            if ($data['item_id'] <= 0 || $data['warehouse_id'] <= 0 || $data['quantity'] <= 0) {
                $error = 'يرجى ملء جميع الحقول المطلوبة';
            } else {
                $newMovementId = $inventoryModel->addInventoryMovement($data);
                if ($newMovementId) {
                    setAlert('تم إضافة حركة المخزون بنجاح', 'success');
                    redirect("inventory_movements.php?action=view&id={$newMovementId}");
                } else {
                    $error = 'فشل في إضافة حركة المخزون';
                }
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } else {
        $error = 'خطأ في التحقق من الأمان';
    }
}

// معالجة ترحيل الحركة
if ($action === 'post' && $movementId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        try {
            if ($inventoryModel->postInventoryMovement($movementId)) {
                setAlert('تم ترحيل حركة المخزون بنجاح', 'success');
                redirect("inventory_movements.php?action=view&id={$movementId}");
            } else {
                $error = 'فشل في ترحيل حركة المخزون';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// معالجة الفلترة والبحث
$filters = [
    'movement_type' => $_GET['movement_type'] ?? '',
    'warehouse_id' => intval($_GET['warehouse_id'] ?? 0),
    'item_id' => intval($_GET['item_id'] ?? 0),
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'is_posted' => $_GET['is_posted'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// الحصول على البيانات
$movements = [];
$currentMovement = null;
$totalRecords = 0;

// إعدادات الصفحات
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    if ($action === 'view' && $movementId) {
        $currentMovement = $inventoryModel->getInventoryMovementById($movementId);
        if (!$currentMovement) {
            setAlert('حركة المخزون غير موجودة', 'error');
            redirect('inventory_movements.php');
        }
    } else {
        $movements = $inventoryModel->getInventoryMovements($filters, $limit, $offset);
        $totalRecords = $inventoryModel->getInventoryMovementsCount($filters);
    }
    
    // الحصول على البيانات المساعدة
    $warehouses = $inventoryModel->getWarehouses();
    $items = $inventoryModel->getItems();
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $movements = [];
    $warehouses = [];
    $items = [];
}

// حساب معلومات الصفحات
$totalPages = ceil($totalRecords / $limit);

$pageTitle = 'إدارة حركات المخزون';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .print-hidden { display: none; }
        @media print {
            .no-print { display: none !important; }
            .print-hidden { display: block !important; }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    
    <!-- شريط التنقل -->
    <nav class="bg-blue-600 text-white p-4 no-print">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4 space-x-reverse">
                <h1 class="text-xl font-bold">
                    <i class="fas fa-boxes mr-2"></i>
                    <?php echo $pageTitle; ?>
                </h1>
            </div>
            
            <div class="flex items-center space-x-4 space-x-reverse">
                <a href="dashboard.php" class="hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
                <a href="inventory.php" class="hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-warehouse ml-2"></i>
                    المخزون
                </a>
                <a href="logout.php" class="hover:text-blue-200 transition-colors duration-200">
                    <i class="fas fa-sign-out-alt ml-2"></i>
                    خروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        
        <!-- عرض الرسائل -->
        <?php if ($error): ?>
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded no-print">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded no-print">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <!-- قسم الفلترة والبحث -->
        <?php if ($action === 'list'): ?>
            <div class="bg-white shadow-lg rounded-lg mb-6 no-print">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-filter mr-2"></i>
                        فلترة وبحث الحركات
                    </h3>
                </div>

                <form method="GET" class="p-6">
                    <input type="hidden" name="action" value="list">

                    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">

                        <!-- نوع الحركة -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">نوع الحركة</label>
                            <select name="movement_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الأنواع</option>
                                <option value="in" <?php echo $filters['movement_type'] === 'in' ? 'selected' : ''; ?>>دخول</option>
                                <option value="out" <?php echo $filters['movement_type'] === 'out' ? 'selected' : ''; ?>>خروج</option>
                                <option value="transfer" <?php echo $filters['movement_type'] === 'transfer' ? 'selected' : ''; ?>>نقل</option>
                                <option value="adjustment" <?php echo $filters['movement_type'] === 'adjustment' ? 'selected' : ''; ?>>تسوية</option>
                            </select>
                        </div>

                        <!-- المستودع -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المستودع</label>
                            <select name="warehouse_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع المستودعات</option>
                                <?php foreach ($warehouses as $warehouse): ?>
                                    <option value="<?php echo $warehouse['warehouse_id']; ?>" <?php echo $filters['warehouse_id'] == $warehouse['warehouse_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- الصنف -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الصنف</label>
                            <select name="item_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الأصناف</option>
                                <?php foreach ($items as $item): ?>
                                    <option value="<?php echo $item['item_id']; ?>" <?php echo $filters['item_id'] == $item['item_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($item['item_name']); ?> (<?php echo htmlspecialchars($item['item_code']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- حالة الترحيل -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">حالة الترحيل</label>
                            <select name="is_posted" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الحالات</option>
                                <option value="1" <?php echo $filters['is_posted'] === '1' ? 'selected' : ''; ?>>مرحل</option>
                                <option value="0" <?php echo $filters['is_posted'] === '0' ? 'selected' : ''; ?>>غير مرحل</option>
                            </select>
                        </div>

                        <!-- من تاريخ -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date"
                                   name="date_from"
                                   value="<?php echo htmlspecialchars($filters['date_from']); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- إلى تاريخ -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date"
                                   name="date_to"
                                   value="<?php echo htmlspecialchars($filters['date_to']); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- البحث -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">البحث</label>
                            <input type="text"
                                   name="search"
                                   value="<?php echo htmlspecialchars($filters['search']); ?>"
                                   placeholder="رقم الحركة أو المرجع..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- أزرار البحث -->
                        <div class="flex items-end space-x-2 space-x-reverse">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                                <i class="fas fa-search ml-2"></i>
                                بحث
                            </button>
                            <a href="inventory_movements.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- قائمة الحركات -->
        <?php if ($action === 'list'): ?>
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-list mr-2"></i>
                            حركات المخزون
                            <?php if ($totalRecords > 0): ?>
                                <span class="text-sm text-gray-500">(<?php echo number_format($totalRecords); ?> حركة)</span>
                            <?php endif; ?>
                        </h3>
                    </div>
                </div>

                <?php if (!empty($movements)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الحركة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستودع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التكلفة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase no-print">العمليات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($movements as $movement): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm font-medium text-blue-600">
                                            <a href="inventory_movements.php?action=view&id=<?php echo $movement['movement_id']; ?>" class="hover:text-blue-800">
                                                <?php echo htmlspecialchars($movement['movement_number']); ?>
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo date('Y-m-d', strtotime($movement['movement_date'])); ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm">
                                            <?php
                                            $typeColors = [
                                                'in' => 'bg-green-100 text-green-800',
                                                'out' => 'bg-red-100 text-red-800',
                                                'transfer' => 'bg-blue-100 text-blue-800',
                                                'adjustment' => 'bg-yellow-100 text-yellow-800'
                                            ];
                                            $typeNames = [
                                                'in' => 'دخول',
                                                'out' => 'خروج',
                                                'transfer' => 'نقل',
                                                'adjustment' => 'تسوية'
                                            ];
                                            $colorClass = $typeColors[$movement['movement_type']] ?? 'bg-gray-100 text-gray-800';
                                            $typeName = $typeNames[$movement['movement_type']] ?? $movement['movement_type'];
                                            ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $colorClass; ?>">
                                                <?php echo $typeName; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo htmlspecialchars($movement['item_name']); ?>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($movement['item_code']); ?></div>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo htmlspecialchars($movement['warehouse_name']); ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo number_format($movement['quantity'], 2); ?>
                                            <span class="text-xs text-gray-500"><?php echo htmlspecialchars($movement['unit_symbol'] ?? ''); ?></span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo number_format($movement['unit_cost'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm">
                                            <?php if ($movement['is_posted']): ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                    مرحل
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                                    غير مرحل
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 text-center text-sm no-print">
                                            <div class="flex justify-center space-x-2 space-x-reverse">
                                                <a href="inventory_movements.php?action=view&id=<?php echo $movement['movement_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                                   title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (!$movement['is_posted']): ?>
                                                    <form method="POST" action="inventory_movements.php?action=post&id=<?php echo $movement['movement_id']; ?>" class="inline">
                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                        <button type="submit"
                                                                class="text-green-600 hover:text-green-900 transition-colors duration-200"
                                                                title="ترحيل"
                                                                onclick="return confirm('هل أنت متأكد من ترحيل هذه الحركة؟')">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- صفحات التنقل -->
                    <?php if ($totalPages > 1): ?>
                        <div class="px-6 py-4 border-t border-gray-200 no-print">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-700">
                                    عرض <?php echo (($page - 1) * $limit) + 1; ?> إلى <?php echo min($page * $limit, $totalRecords); ?> من <?php echo number_format($totalRecords); ?> حركة
                                </div>

                                <div class="flex space-x-2 space-x-reverse">
                                    <?php if ($page > 1): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                           class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200">
                                            السابق
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                           class="px-3 py-2 rounded transition-colors duration-200 <?php echo $i === $page ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                           class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200">
                                            التالي
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div class="text-center py-12">
                        <i class="fas fa-boxes text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حركات مخزون</h3>
                        <p class="text-gray-500 mb-4">لم يتم العثور على حركات مخزون مطابقة للبحث</p>
                        <a href="inventory_movements.php?action=add" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة حركة جديدة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- نموذج إضافة حركة جديدة -->
        <?php if ($action === 'add'): ?>
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة حركة مخزون جديدة
                    </h3>
                </div>

                <form method="POST" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- المعلومات الأساسية -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2">المعلومات الأساسية</h4>

                            <!-- تاريخ الحركة -->
                            <div>
                                <label for="movement_date" class="block text-sm font-medium text-gray-700 mb-1">
                                    تاريخ الحركة *
                                </label>
                                <input type="date"
                                       id="movement_date"
                                       name="movement_date"
                                       value="<?php echo date('Y-m-d'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- نوع الحركة -->
                            <div>
                                <label for="movement_type" class="block text-sm font-medium text-gray-700 mb-1">
                                    نوع الحركة *
                                </label>
                                <select id="movement_type"
                                        name="movement_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">اختر نوع الحركة</option>
                                    <option value="in">دخول</option>
                                    <option value="out">خروج</option>
                                    <option value="transfer">نقل</option>
                                    <option value="adjustment">تسوية</option>
                                </select>
                            </div>

                            <!-- الصنف -->
                            <div>
                                <label for="item_id" class="block text-sm font-medium text-gray-700 mb-1">
                                    الصنف *
                                </label>
                                <select id="item_id"
                                        name="item_id"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">اختر الصنف</option>
                                    <?php foreach ($items as $item): ?>
                                        <option value="<?php echo $item['item_id']; ?>">
                                            <?php echo htmlspecialchars($item['item_name']); ?> (<?php echo htmlspecialchars($item['item_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- المستودع -->
                            <div>
                                <label for="warehouse_id" class="block text-sm font-medium text-gray-700 mb-1">
                                    المستودع *
                                </label>
                                <select id="warehouse_id"
                                        name="warehouse_id"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        required>
                                    <option value="">اختر المستودع</option>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <option value="<?php echo $warehouse['warehouse_id']; ?>">
                                            <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <!-- تفاصيل الحركة -->
                        <div class="space-y-4">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2">تفاصيل الحركة</h4>

                            <!-- الكمية -->
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700 mb-1">
                                    الكمية *
                                </label>
                                <input type="number"
                                       id="quantity"
                                       name="quantity"
                                       step="0.01"
                                       min="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- تكلفة الوحدة -->
                            <div>
                                <label for="unit_cost" class="block text-sm font-medium text-gray-700 mb-1">
                                    تكلفة الوحدة *
                                </label>
                                <input type="number"
                                       id="unit_cost"
                                       name="unit_cost"
                                       step="0.01"
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- نوع المرجع -->
                            <div>
                                <label for="reference_type" class="block text-sm font-medium text-gray-700 mb-1">
                                    نوع المرجع
                                </label>
                                <select id="reference_type"
                                        name="reference_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="manual">يدوي</option>
                                    <option value="invoice">فاتورة</option>
                                    <option value="purchase_order">أمر شراء</option>
                                    <option value="sales_order">أمر بيع</option>
                                    <option value="transfer">نقل</option>
                                </select>
                            </div>

                            <!-- رقم المرجع -->
                            <div>
                                <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-1">
                                    رقم المرجع
                                </label>
                                <input type="text"
                                       id="reference_number"
                                       name="reference_number"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- رقم الدفعة -->
                        <div>
                            <label for="batch_number" class="block text-sm font-medium text-gray-700 mb-1">
                                رقم الدفعة
                            </label>
                            <input type="text"
                                   id="batch_number"
                                   name="batch_number"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="رقم الدفعة (اختياري)">
                        </div>

                        <!-- تاريخ الانتهاء -->
                        <div>
                            <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-1">
                                تاريخ الانتهاء
                            </label>
                            <input type="date"
                                   id="expiry_date"
                                   name="expiry_date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="mt-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">
                            ملاحظات
                        </label>
                        <textarea id="notes"
                                  name="notes"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- أزرار العمل -->
                    <div class="mt-6 flex justify-between">
                        <a href="inventory_movements.php"
                           class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>

                        <button type="submit"
                                class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            حفظ الحركة
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- عرض تفاصيل الحركة -->
        <?php if ($action === 'view' && $currentMovement): ?>
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-eye mr-2"></i>
                            تفاصيل حركة المخزون
                        </h3>
                        <div class="flex space-x-2 space-x-reverse no-print">
                            <a href="inventory_movements.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة للقائمة
                            </a>
                            <?php if (!$currentMovement['is_posted']): ?>
                                <form method="POST" action="inventory_movements.php?action=post&id=<?php echo $currentMovement['movement_id']; ?>" class="inline">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <button type="submit"
                                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                                            onclick="return confirm('هل أنت متأكد من ترحيل هذه الحركة؟')">
                                        <i class="fas fa-check ml-2"></i>
                                        ترحيل الحركة
                                    </button>
                                </form>
                            <?php endif; ?>
                            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-print ml-2"></i>
                                طباعة
                            </button>
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- المعلومات الأساسية -->
                        <div>
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2 mb-4">المعلومات الأساسية</h4>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">رقم الحركة:</span>
                                    <span class="font-medium"><?php echo htmlspecialchars($currentMovement['movement_number']); ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">تاريخ الحركة:</span>
                                    <span class="font-medium"><?php echo date('Y-m-d', strtotime($currentMovement['movement_date'])); ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">نوع الحركة:</span>
                                    <span class="font-medium">
                                        <?php
                                        $typeNames = [
                                            'in' => 'دخول',
                                            'out' => 'خروج',
                                            'transfer' => 'نقل',
                                            'adjustment' => 'تسوية'
                                        ];
                                        echo $typeNames[$currentMovement['movement_type']] ?? $currentMovement['movement_type'];
                                        ?>
                                    </span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">الحالة:</span>
                                    <span class="font-medium">
                                        <?php if ($currentMovement['is_posted']): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                مرحل
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                                غير مرحل
                                            </span>
                                        <?php endif; ?>
                                    </span>
                                </div>

                                <?php if ($currentMovement['posted_at']): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">تاريخ الترحيل:</span>
                                        <span class="font-medium"><?php echo date('Y-m-d H:i', strtotime($currentMovement['posted_at'])); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- تفاصيل الصنف والمستودع -->
                        <div>
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2 mb-4">تفاصيل الصنف والمستودع</h4>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">الصنف:</span>
                                    <span class="font-medium"><?php echo htmlspecialchars($currentMovement['item_name']); ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">كود الصنف:</span>
                                    <span class="font-medium"><?php echo htmlspecialchars($currentMovement['item_code']); ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">المستودع:</span>
                                    <span class="font-medium"><?php echo htmlspecialchars($currentMovement['warehouse_name']); ?></span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">الكمية:</span>
                                    <span class="font-medium">
                                        <?php echo number_format($currentMovement['quantity'], 2); ?>
                                        <?php echo htmlspecialchars($currentMovement['unit_symbol'] ?? ''); ?>
                                    </span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">تكلفة الوحدة:</span>
                                    <span class="font-medium"><?php echo number_format($currentMovement['unit_cost'], 2); ?> ريال</span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-gray-600">إجمالي التكلفة:</span>
                                    <span class="font-medium text-lg">
                                        <?php echo number_format($currentMovement['quantity'] * $currentMovement['unit_cost'], 2); ?> ريال
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المرجع -->
                    <?php if ($currentMovement['reference_type'] || $currentMovement['reference_number']): ?>
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2 mb-4">معلومات المرجع</h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php if ($currentMovement['reference_type']): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">نوع المرجع:</span>
                                        <span class="font-medium"><?php echo htmlspecialchars($currentMovement['reference_type']); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($currentMovement['reference_number']): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">رقم المرجع:</span>
                                        <span class="font-medium"><?php echo htmlspecialchars($currentMovement['reference_number']); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات إضافية -->
                    <?php if ($currentMovement['batch_number'] || $currentMovement['expiry_date'] || $currentMovement['notes']): ?>
                        <div class="mt-6">
                            <h4 class="text-md font-medium text-gray-800 border-b pb-2 mb-4">معلومات إضافية</h4>

                            <div class="space-y-3">
                                <?php if ($currentMovement['batch_number']): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">رقم الدفعة:</span>
                                        <span class="font-medium"><?php echo htmlspecialchars($currentMovement['batch_number']); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($currentMovement['expiry_date']): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">تاريخ الانتهاء:</span>
                                        <span class="font-medium"><?php echo date('Y-m-d', strtotime($currentMovement['expiry_date'])); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($currentMovement['notes']): ?>
                                    <div>
                                        <span class="text-gray-600">الملاحظات:</span>
                                        <div class="mt-2 p-3 bg-gray-50 rounded-md">
                                            <?php echo nl2br(htmlspecialchars($currentMovement['notes'])); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات الإنشاء -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <span>تم الإنشاء بواسطة:</span>
                                <span class="font-medium"><?php echo htmlspecialchars($currentMovement['created_by_name'] ?? 'غير محدد'); ?></span>
                            </div>
                            <div>
                                <span>تاريخ الإنشاء:</span>
                                <span class="font-medium"><?php echo date('Y-m-d H:i', strtotime($currentMovement['created_at'])); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </div>
</body>
</html>

        <!-- عرض الرسائل من الجلسة -->
        <?php if (isset($_SESSION['alert'])): ?>
            <div class="mb-4 p-4 rounded no-print <?php echo $_SESSION['alert']['type'] === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?>">
                <i class="fas <?php echo $_SESSION['alert']['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                <?php 
                echo htmlspecialchars($_SESSION['alert']['message']); 
                unset($_SESSION['alert']);
                ?>
            </div>
        <?php endif; ?>

        <!-- أزرار العمل الرئيسية -->
        <?php if ($action === 'list'): ?>
            <div class="mb-6 flex justify-between items-center no-print">
                <div class="flex space-x-2 space-x-reverse">
                    <a href="inventory_movements.php?action=add" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة حركة جديدة
                    </a>
                    <a href="inventory.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-warehouse ml-2"></i>
                        أرصدة المخزون
                    </a>
                    <a href="inventory_report.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-chart-bar ml-2"></i>
                        تقارير المخزون
                    </a>
                </div>
            </div>
        <?php endif; ?>

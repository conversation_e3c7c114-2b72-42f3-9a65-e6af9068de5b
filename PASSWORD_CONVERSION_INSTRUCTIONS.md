# تعليمات تحويل كلمات المرور إلى نص عادي

## ⚠️ تحذير أمني خطير

**هذا التحويل يشكل خطراً أمنياً كبيراً جداً ولا يُنصح به في البيئات الإنتاجية**

تم تحويل النظام من استخدام تشفير bcrypt إلى تخزين كلمات المرور كنص عادي. هذا يعني:

- كلمات المرور مكشوفة لأي شخص يصل إلى قاعدة البيانات
- لا توجد حماية في حالة تسريب قاعدة البيانات
- يمكن لمديري قاعدة البيانات رؤية كلمات المرور
- هذا مخالف لأفضل الممارسات الأمنية

## الملفات التي تم تعديلها

### 1. ملفات PHP المُحدثة:
- `includes/functions.php` - تعديل دوال hashPassword و verifyPassword
- `models/User.php` - تعديل دوال تسجيل الدخول وإنشاء المستخدمين
- `create_admin.php` - إزالة تشفير كلمة مرور المدير
- `reset_admin_password.php` - إزالة تشفير كلمة المرور الجديدة

### 2. قاعدة البيانات:
- `database_schema.sql` - تغيير عمود `password_hash` إلى `password`
- `convert_passwords_to_plaintext.php` - سكريبت تحويل البيانات الموجودة

### 3. التوثيق:
- `README.md` - تحديث قسم الأمان
- `PASSWORD_CONVERSION_INSTRUCTIONS.md` - هذا الملف

## خطوات التحويل

### للأنظمة الجديدة:
1. استخدم `database_schema.sql` المُحدث لإنشاء قاعدة البيانات
2. النظام سيعمل بكلمات مرور غير مشفرة من البداية

### للأنظمة الموجودة:
1. قم بعمل نسخة احتياطية من قاعدة البيانات أولاً
2. افتح `convert_passwords_to_plaintext.php` في المتصفح
3. أدخل كلمة مرور افتراضية لجميع المستخدمين
4. اضغط "تحويل كلمات المرور"
5. سيتم تحويل هيكل الجدول وتعيين كلمة المرور الافتراضية

## التغييرات التقنية

### دالة hashPassword (قبل):
```php
function hashPassword($password) {
    return password_hash($password, PASSWORD_HASH_ALGO);
}
```

### دالة hashPassword (بعد):
```php
function hashPassword($password) {
    return $password; // تخزين كنص عادي
}
```

### دالة verifyPassword (قبل):
```php
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}
```

### دالة verifyPassword (بعد):
```php
function verifyPassword($password, $storedPassword) {
    return $password === $storedPassword; // مقارنة مباشرة
}
```

### هيكل قاعدة البيانات (قبل):
```sql
password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة'
```

### هيكل قاعدة البيانات (بعد):
```sql
password VARCHAR(255) NOT NULL COMMENT 'كلمة المرور (نص عادي - غير آمن)'
```

## المخاطر الأمنية

1. **تسريب قاعدة البيانات**: كلمات المرور مكشوفة بالكامل
2. **وصول المديرين**: يمكن لمديري قاعدة البيانات رؤية كلمات المرور
3. **سجلات النظام**: كلمات المرور قد تظهر في سجلات النظام
4. **النسخ الاحتياطية**: النسخ الاحتياطية تحتوي على كلمات مرور مكشوفة
5. **مخالفة القوانين**: قد يخالف قوانين حماية البيانات

## التوصيات

### للبيئات التطويرية فقط:
- استخدم كلمات مرور بسيطة وغير حساسة
- لا تستخدم كلمات مرور حقيقية
- تأكد من عدم وصول أشخاص غير مخولين لقاعدة البيانات

### للعودة للتشفير:
إذا كنت تريد العودة لاستخدام التشفير الآمن:

1. استعد النسخة الاحتياطية من قبل التحويل
2. أو أعد كتابة دوال التشفير
3. أعد إنشاء كلمات المرور لجميع المستخدمين

## معلومات إضافية

- تاريخ التحويل: <?php echo date('Y-m-d H:i:s'); ?>
- الإصدار: 1.0.0 (بدون تشفير)
- المطور: تم التحويل بواسطة Augment Agent

## الدعم

في حالة وجود مشاكل:
1. تحقق من سجلات الأخطاء في `logs/`
2. تأكد من صحة إعدادات قاعدة البيانات
3. راجع هذا الملف للتأكد من اتباع الخطوات الصحيحة

---

**تذكير أخير: هذا التحويل غير آمن ولا يُنصح به في البيئات الإنتاجية**

<?php
/**
 * فئة قاعدة البيانات
 * Database Class - Singleton Pattern
 */

class Database {
    private static $instance = null;
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    /**
     * الحصول على مثيل واحد من قاعدة البيانات
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            $this->logError("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
    }

    /**
     * الحصول على الاتصال
     */
    public function getConnection() {
        return $this->connection;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->logError("Select query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }

    /**
     * تنفيذ استعلام SELECT لسجل واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->logError("Select one query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->connection->lastInsertId() : false;
        } catch (PDOException $e) {
            $this->logError("Insert query failed: " . $e->getMessage());
            throw new Exception("فشل في إدراج البيانات");
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError("Update query failed: " . $e->getMessage());
            throw new Exception("فشل في تحديث البيانات");
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError("Delete query failed: " . $e->getMessage());
            throw new Exception("فشل في حذف البيانات");
        }
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }

    /**
     * تنفيذ استعلام عام (DDL/DML)
     */
    public function execute($query, $params = []) {
        try {
            if (empty($params)) {
                // للاستعلامات البسيطة بدون معاملات (مثل ALTER TABLE)
                return $this->connection->exec($query);
            } else {
                // للاستعلامات مع معاملات
                $stmt = $this->connection->prepare($query);
                return $stmt->execute($params);
            }
        } catch (PDOException $e) {
            $this->logError("Execute query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام");
        }
    }

    /**
     * تسجيل الأخطاء
     */
    private function logError($message) {
        if (DEBUG_MODE) {
            error_log(date('Y-m-d H:i:s') . " - Database Error: " . $message . PHP_EOL, 3, "logs/database_errors.log");
        }
    }

    /**
     * منع النسخ
     */
    private function __clone() {}

    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

?>

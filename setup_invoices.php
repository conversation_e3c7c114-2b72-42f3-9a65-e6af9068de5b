<?php
/**
 * سكريپت إعداد جداول الفواتير
 * Setup Invoices Tables Script
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
        
        // إنشاء جدول الفواتير الرئيسي
        $createInvoicesSQL = "
        CREATE TABLE IF NOT EXISTS invoices (
            invoice_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الفاتورة الفريد',
            invoice_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم الفاتورة',
            invoice_type ENUM('sales', 'purchase') NOT NULL COMMENT 'نوع الفاتورة',
            customer_id INT NOT NULL COMMENT 'معرف العميل/المورد',
            invoice_date DATE NOT NULL COMMENT 'تاريخ الفاتورة',
            due_date DATE COMMENT 'تاريخ الاستحقاق',
            subtotal DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المجموع الفرعي',
            tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'مبلغ الضريبة',
            discount_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'مبلغ الخصم',
            total_amount DECIMAL(12,2) NOT NULL COMMENT 'المبلغ الإجمالي',
            paid_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'المبلغ المدفوع',
            status ENUM('draft', 'posted', 'paid', 'cancelled') DEFAULT 'draft' COMMENT 'حالة الفاتورة',
            notes TEXT COMMENT 'ملاحظات',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
            created_by INT COMMENT 'المستخدم الذي أنشأ الفاتورة',
            
            INDEX idx_invoice_number (invoice_number),
            INDEX idx_invoice_type (invoice_type),
            INDEX idx_customer_id (customer_id),
            INDEX idx_invoice_date (invoice_date),
            INDEX idx_status (status),
            
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE RESTRICT,
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
        ) ENGINE=InnoDB COMMENT='جدول الفواتير الرئيسي'";
        
        $db->execute($createInvoicesSQL);
        
        // إنشاء جدول عناصر الفواتير
        $createInvoiceItemsSQL = "
        CREATE TABLE IF NOT EXISTS invoice_items (
            item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف العنصر الفريد',
            invoice_id INT NOT NULL COMMENT 'معرف الفاتورة',
            product_id INT COMMENT 'معرف المنتج (إذا كان موجوداً)',
            item_name VARCHAR(200) NOT NULL COMMENT 'اسم العنصر',
            item_description TEXT COMMENT 'وصف العنصر',
            quantity DECIMAL(10,3) NOT NULL DEFAULT 1 COMMENT 'الكمية',
            unit_price DECIMAL(12,2) NOT NULL COMMENT 'سعر الوحدة',
            total_price DECIMAL(12,2) NOT NULL COMMENT 'السعر الإجمالي',
            tax_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'معدل الضريبة',
            tax_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'مبلغ الضريبة',
            discount_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'معدل الخصم',
            discount_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT 'مبلغ الخصم',
            
            INDEX idx_invoice_id (invoice_id),
            INDEX idx_product_id (product_id),
            
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE CASCADE
        ) ENGINE=InnoDB COMMENT='جدول عناصر الفواتير'";
        
        $db->execute($createInvoiceItemsSQL);
        
        // إدراج بيانات تجريبية
        $insertSampleSQL = "
        INSERT IGNORE INTO invoices (
            invoice_number, invoice_type, customer_id, invoice_date, due_date,
            subtotal, tax_amount, total_amount, status, notes, created_by
        ) VALUES 
        ('INV-2024-0001', 'sales', 1, '2024-01-15', '2024-02-14', 
         1000.00, 150.00, 1150.00, 'posted', 'فاتورة مبيعات تجريبية', 1),
        
        ('INV-2024-0002', 'purchase', 2, '2024-01-20', '2024-02-19', 
         2000.00, 300.00, 2300.00, 'posted', 'فاتورة مشتريات تجريبية', 1),
        
        ('INV-2024-0003', 'sales', 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 
         500.00, 75.00, 575.00, 'draft', 'فاتورة مسودة', 1)";
        
        $db->execute($insertSampleSQL);
        
        // إدراج عناصر تجريبية
        $insertItemsSQL = "
        INSERT IGNORE INTO invoice_items (
            invoice_id, item_name, quantity, unit_price, total_price, tax_amount
        ) VALUES 
        (1, 'منتج تجريبي 1', 2, 500.00, 1000.00, 150.00),
        (2, 'مواد خام تجريبية', 1, 2000.00, 2000.00, 300.00),
        (3, 'خدمة استشارية', 1, 500.00, 500.00, 75.00)";
        
        $db->execute($insertItemsSQL);
        
        $message = 'تم إنشاء جداول الفواتير وإدراج البيانات التجريبية بنجاح!';
        $messageType = 'success';
    }
    
    // فحص وجود الجداول
    $invoicesTableExists = false;
    $itemsTableExists = false;
    
    try {
        $result = $db->select("SHOW TABLES LIKE 'invoices'");
        $invoicesTableExists = !empty($result);
        
        $result = $db->select("SHOW TABLES LIKE 'invoice_items'");
        $itemsTableExists = !empty($result);
    } catch (Exception $e) {
        // الجداول غير موجودة
    }
    
    // عدد الفواتير الموجودة
    $invoicesCount = 0;
    if ($invoicesTableExists) {
        try {
            $result = $db->selectOne("SELECT COUNT(*) as count FROM invoices");
            $invoicesCount = $result['count'];
        } catch (Exception $e) {
            // خطأ في العد
        }
    }
    
} catch (Exception $e) {
    $message = 'خطأ: ' . $e->getMessage();
    $messageType = 'error';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جداول الفواتير</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-file-invoice mr-2"></i>
                إعداد جداول الفواتير
            </h1>

            <?php if ($message): ?>
                <div class="mb-4 p-4 rounded <?php echo $messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجداول -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">حالة جداول الفواتير:</h3>
                
                <div class="space-y-2">
                    <p class="<?php echo $invoicesTableExists ? 'text-green-600' : 'text-red-600'; ?>">
                        <i class="fas <?php echo $invoicesTableExists ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                        جدول الفواتير الرئيسي: <?php echo $invoicesTableExists ? 'موجود' : 'غير موجود'; ?>
                    </p>
                    
                    <p class="<?php echo $itemsTableExists ? 'text-green-600' : 'text-red-600'; ?>">
                        <i class="fas <?php echo $itemsTableExists ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                        جدول عناصر الفواتير: <?php echo $itemsTableExists ? 'موجود' : 'غير موجود'; ?>
                    </p>
                    
                    <?php if ($invoicesTableExists): ?>
                        <p class="text-blue-600">
                            <i class="fas fa-info-circle mr-2"></i>
                            عدد الفواتير: <?php echo $invoicesCount; ?>
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- نموذج الإنشاء -->
            <?php if (!$invoicesTableExists || !$itemsTableExists): ?>
                <form method="POST" class="mb-6">
                    <button 
                        type="submit" 
                        name="create_tables"
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded transition duration-200"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        إنشاء جداول الفواتير وإدراج البيانات التجريبية
                    </button>
                </form>
            <?php endif; ?>

            <!-- روابط التنقل -->
            <div class="text-center space-x-4">
                <a href="invoices.php" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-file-invoice mr-2"></i>
                    إدارة الفواتير
                </a>
                <a href="index.php" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-home mr-2"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>

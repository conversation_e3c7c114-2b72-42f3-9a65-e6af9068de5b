<?php
/**
 * نموذج المخازن
 * Warehouse Model
 */

class Warehouse {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع المخازن
     */
    public function getAllWarehouses($activeOnly = true) {
        try {
            $query = "SELECT 
                        w.warehouse_id, w.warehouse_code, w.warehouse_name, 
                        w.warehouse_location, w.warehouse_type, w.manager_name,
                        w.phone, w.email, w.capacity, w.capacity_unit,
                        w.is_active, w.allow_negative_stock, w.notes,
                        w.created_at, b.branch_name
                      FROM warehouses w
                      LEFT JOIN branches b ON w.branch_id = b.branch_id";
            
            if ($activeOnly) {
                $query .= " WHERE w.is_active = 1";
            }
            
            $query .= " ORDER BY w.warehouse_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all warehouses error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على مخزن بالمعرف
     */
    public function getWarehouseById($warehouseId) {
        try {
            $query = "SELECT 
                        w.*, b.branch_name
                      FROM warehouses w
                      LEFT JOIN branches b ON w.branch_id = b.branch_id
                      WHERE w.warehouse_id = ?";
            
            return $this->db->selectOne($query, [$warehouseId]);
            
        } catch (Exception $e) {
            error_log("Get warehouse by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء مخزن جديد
     */
    public function createWarehouse($data) {
        try {
            // التحقق من عدم وجود كود المخزن
            if ($this->warehouseCodeExists($data['warehouse_code'])) {
                throw new Exception('كود المخزن موجود بالفعل');
            }
            
            $query = "INSERT INTO warehouses (
                        warehouse_code, warehouse_name, warehouse_location, warehouse_type,
                        manager_name, phone, email, branch_id, capacity, capacity_unit,
                        is_active, allow_negative_stock, notes, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['warehouse_code'],
                $data['warehouse_name'],
                $data['warehouse_location'] ?? null,
                $data['warehouse_type'] ?? 'main',
                $data['manager_name'] ?? null,
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['branch_id'] ?? null,
                $data['capacity'] ?? null,
                $data['capacity_unit'] ?? null,
                $data['is_active'] ?? 1,
                $data['allow_negative_stock'] ?? 0,
                $data['notes'] ?? null,
                $_SESSION['user_id'] ?? null
            ];
            
            $warehouseId = $this->db->insert($query, $params);
            
            if ($warehouseId) {
                logActivity('إنشاء مخزن', "تم إنشاء المخزن: {$data['warehouse_name']}");
            }
            
            return $warehouseId;
            
        } catch (Exception $e) {
            error_log("Create warehouse error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث المخزن
     */
    public function updateWarehouse($warehouseId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['warehouse_name'])) {
                $setParts[] = "warehouse_name = ?";
                $params[] = $data['warehouse_name'];
            }
            
            if (isset($data['warehouse_location'])) {
                $setParts[] = "warehouse_location = ?";
                $params[] = $data['warehouse_location'];
            }
            
            if (isset($data['warehouse_type'])) {
                $setParts[] = "warehouse_type = ?";
                $params[] = $data['warehouse_type'];
            }
            
            if (isset($data['manager_name'])) {
                $setParts[] = "manager_name = ?";
                $params[] = $data['manager_name'];
            }
            
            if (isset($data['phone'])) {
                $setParts[] = "phone = ?";
                $params[] = $data['phone'];
            }
            
            if (isset($data['email'])) {
                $setParts[] = "email = ?";
                $params[] = $data['email'];
            }
            
            if (isset($data['branch_id'])) {
                $setParts[] = "branch_id = ?";
                $params[] = $data['branch_id'];
            }
            
            if (isset($data['capacity'])) {
                $setParts[] = "capacity = ?";
                $params[] = $data['capacity'];
            }
            
            if (isset($data['capacity_unit'])) {
                $setParts[] = "capacity_unit = ?";
                $params[] = $data['capacity_unit'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (isset($data['allow_negative_stock'])) {
                $setParts[] = "allow_negative_stock = ?";
                $params[] = $data['allow_negative_stock'];
            }
            
            if (isset($data['notes'])) {
                $setParts[] = "notes = ?";
                $params[] = $data['notes'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $warehouseId;
            
            $query = "UPDATE warehouses SET " . implode(', ', $setParts) . " WHERE warehouse_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث مخزن', "تم تحديث المخزن ID: {$warehouseId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update warehouse error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف المخزن (إلغاء تفعيل)
     */
    public function deleteWarehouse($warehouseId) {
        try {
            // التحقق من عدم وجود أرصدة مخزون
            $balanceCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM inventory_balances WHERE warehouse_id = ? AND quantity > 0",
                [$warehouseId]
            );
            
            if ($balanceCount['count'] > 0) {
                throw new Exception('لا يمكن حذف المخزن لوجود أرصدة مخزون به');
            }
            
            $query = "UPDATE warehouses SET is_active = 0, updated_at = NOW() WHERE warehouse_id = ?";
            $result = $this->db->update($query, [$warehouseId]);
            
            if ($result) {
                logActivity('حذف مخزن', "تم حذف المخزن ID: {$warehouseId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete warehouse error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود المخزن
     */
    private function warehouseCodeExists($warehouseCode, $excludeWarehouseId = null) {
        $query = "SELECT COUNT(*) as count FROM warehouses WHERE warehouse_code = ?";
        $params = [$warehouseCode];
        
        if ($excludeWarehouseId) {
            $query .= " AND warehouse_id != ?";
            $params[] = $excludeWarehouseId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * الحصول على أرصدة المخزون للمخزن
     */
    public function getWarehouseInventory($warehouseId, $lowStockOnly = false) {
        try {
            $query = "SELECT 
                        ib.*, i.item_name, i.item_code, i.min_stock_level,
                        u.unit_name, u.unit_symbol, c.category_name
                      FROM inventory_balances ib
                      JOIN items i ON ib.item_id = i.item_id
                      JOIN units u ON ib.unit_id = u.unit_id
                      LEFT JOIN item_categories c ON i.category_id = c.category_id
                      WHERE ib.warehouse_id = ? AND i.is_active = 1";
            
            $params = [$warehouseId];
            
            if ($lowStockOnly) {
                $query .= " AND ib.quantity <= i.min_stock_level";
            }
            
            $query .= " ORDER BY i.item_name";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get warehouse inventory error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات المخزن
     */
    public function getWarehouseStats($warehouseId) {
        try {
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(DISTINCT ib.item_id) as total_items,
                    COALESCE(SUM(ib.total_value), 0) as total_value,
                    COUNT(CASE WHEN ib.quantity <= i.min_stock_level THEN 1 END) as low_stock_items,
                    COUNT(CASE WHEN ib.quantity = 0 THEN 1 END) as out_of_stock_items
                FROM inventory_balances ib
                JOIN items i ON ib.item_id = i.item_id
                WHERE ib.warehouse_id = ? AND i.is_active = 1
            ", [$warehouseId]);
            
            return $stats ?: [
                'total_items' => 0,
                'total_value' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get warehouse stats error: " . $e->getMessage());
            return [
                'total_items' => 0,
                'total_value' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0
            ];
        }
    }
    
    /**
     * البحث في المخازن
     */
    public function searchWarehouses($searchTerm, $activeOnly = true) {
        try {
            $query = "SELECT 
                        w.warehouse_id, w.warehouse_code, w.warehouse_name, 
                        w.warehouse_location, w.warehouse_type, b.branch_name
                      FROM warehouses w
                      LEFT JOIN branches b ON w.branch_id = b.branch_id
                      WHERE (w.warehouse_name LIKE ? OR w.warehouse_code LIKE ? OR w.warehouse_location LIKE ?)";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($activeOnly) {
                $query .= " AND w.is_active = 1";
            }
            
            $query .= " ORDER BY w.warehouse_name LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search warehouses error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على أنواع المخازن
     */
    public function getWarehouseTypes() {
        return [
            'main' => 'رئيسي',
            'branch' => 'فرع',
            'temporary' => 'مؤقت',
            'virtual' => 'افتراضي'
        ];
    }
}

?>

-- إن<PERSON><PERSON>ء جدول الأدوار البسيط
-- Create simple roles table

USE accounting_system;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الدور الفريد',
    role_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'اسم الدور',
    role_description TEXT COMMENT 'وصف الدور',
    permissions JSON COMMENT 'صلاحيات الدور',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'هل الدور نشط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'معرف المستخدم الذي أنشأ الدور',
    
    INDEX idx_role_name (role_name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الأدوار والصلاحيات';

-- إدراج الأدوار الأساسية
INSERT INTO roles (role_name, role_description, permissions, is_active, created_by) VALUES
('مدير النظام', 'صلاحيات كاملة على جميع وحدات النظام', 
 '{"users": {"create": true, "read": true, "update": true, "delete": true}, "roles": {"create": true, "read": true, "update": true, "delete": true}, "accounts": {"create": true, "read": true, "update": true, "delete": true}, "inventory": {"create": true, "read": true, "update": true, "delete": true}, "reports": {"create": true, "read": true, "update": true, "delete": true}, "settings": {"create": true, "read": true, "update": true, "delete": true}}',
 TRUE, 1),

('محاسب', 'صلاحيات المحاسبة والتقارير المالية',
 '{"accounts": {"create": true, "read": true, "update": true, "delete": false}, "reports": {"create": false, "read": true, "update": false, "delete": false}, "inventory": {"create": false, "read": true, "update": false, "delete": false}}',
 TRUE, 1),

('أمين المخزن', 'صلاحيات إدارة المخزون والأصناف',
 '{"inventory": {"create": true, "read": true, "update": true, "delete": true}, "reports": {"create": false, "read": true, "update": false, "delete": false}}',
 TRUE, 1),

('مندوب مبيعات', 'صلاحيات المبيعات والعملاء',
 '{"sales": {"create": true, "read": true, "update": true, "delete": false}, "customers": {"create": true, "read": true, "update": true, "delete": false}, "inventory": {"create": false, "read": true, "update": false, "delete": false}}',
 TRUE, 1),

('مستخدم عادي', 'صلاحيات أساسية للاستعلام والعرض',
 '{"reports": {"create": false, "read": true, "update": false, "delete": false}, "inventory": {"create": false, "read": true, "update": false, "delete": false}}',
 TRUE, 1);

-- تحديث جدول المستخدمين لإضافة حقل role_id
ALTER TABLE users ADD COLUMN IF NOT EXISTS role_id INT COMMENT 'معرف الدور';

-- إضافة مفتاح خارجي لربط المستخدمين بالأدوار
ALTER TABLE users ADD CONSTRAINT fk_users_role_id 
FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE SET NULL ON UPDATE CASCADE;

-- تحديث المستخدمين الموجودين لربطهم بالأدوار
UPDATE users SET role_id = (
    CASE 
        WHEN role = 'admin' THEN 1
        WHEN role = 'accountant' THEN 2
        WHEN role = 'warehouse_manager' THEN 3
        WHEN role = 'sales' THEN 4
        ELSE 5
    END
) WHERE role_id IS NULL;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_users_role_id ON users(role_id);

SELECT 'تم إنشاء جدول الأدوار وإدراج البيانات الأساسية بنجاح!' as message;

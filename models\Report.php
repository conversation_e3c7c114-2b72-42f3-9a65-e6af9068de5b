<?php
/**
 * نموذج التقارير
 * Reports Model
 */

class Report {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * قائمة الدخل (Income Statement)
     */
    public function getIncomeStatement($dateFrom, $dateTo) {
        try {
            // الحصول على الإيرادات
            $revenues = $this->db->select("
                SELECT 
                    coa.account_id, coa.account_code, coa.account_name,
                    COALESCE(SUM(at.credit_amount) - SUM(at.debit_amount), 0) as amount
                FROM chart_of_accounts coa
                LEFT JOIN account_transactions at ON coa.account_id = at.account_id 
                    AND at.is_posted = 1 
                    AND at.transaction_date BETWEEN ? AND ?
                WHERE coa.account_type = 'revenue' AND coa.is_active = 1
                GROUP BY coa.account_id, coa.account_code, coa.account_name
                HAVING amount != 0
                ORDER BY coa.account_code
            ", [$dateFrom, $dateTo]);
            
            // الحصول على المصروفات
            $expenses = $this->db->select("
                SELECT 
                    coa.account_id, coa.account_code, coa.account_name,
                    COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as amount
                FROM chart_of_accounts coa
                LEFT JOIN account_transactions at ON coa.account_id = at.account_id 
                    AND at.is_posted = 1 
                    AND at.transaction_date BETWEEN ? AND ?
                WHERE coa.account_type = 'expenses' AND coa.is_active = 1
                GROUP BY coa.account_id, coa.account_code, coa.account_name
                HAVING amount != 0
                ORDER BY coa.account_code
            ", [$dateFrom, $dateTo]);
            
            // حساب الإجماليات
            $totalRevenues = array_sum(array_column($revenues, 'amount'));
            $totalExpenses = array_sum(array_column($expenses, 'amount'));
            $netIncome = $totalRevenues - $totalExpenses;
            
            return [
                'revenues' => $revenues,
                'expenses' => $expenses,
                'total_revenues' => $totalRevenues,
                'total_expenses' => $totalExpenses,
                'net_income' => $netIncome,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ];
            
        } catch (Exception $e) {
            error_log("Get income statement error: " . $e->getMessage());
            return [
                'revenues' => [],
                'expenses' => [],
                'total_revenues' => 0,
                'total_expenses' => 0,
                'net_income' => 0,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ];
        }
    }
    
    /**
     * الميزانية العمومية (Balance Sheet)
     */
    public function getBalanceSheet($asOfDate) {
        try {
            // الأصول
            $assets = $this->db->select("
                SELECT 
                    coa.account_id, coa.account_code, coa.account_name,
                    COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as amount
                FROM chart_of_accounts coa
                LEFT JOIN account_transactions at ON coa.account_id = at.account_id 
                    AND at.is_posted = 1 
                    AND at.transaction_date <= ?
                WHERE coa.account_type = 'assets' AND coa.is_active = 1
                GROUP BY coa.account_id, coa.account_code, coa.account_name
                HAVING amount != 0
                ORDER BY coa.account_code
            ", [$asOfDate]);
            
            // الخصوم
            $liabilities = $this->db->select("
                SELECT 
                    coa.account_id, coa.account_code, coa.account_name,
                    COALESCE(SUM(at.credit_amount) - SUM(at.debit_amount), 0) as amount
                FROM chart_of_accounts coa
                LEFT JOIN account_transactions at ON coa.account_id = at.account_id 
                    AND at.is_posted = 1 
                    AND at.transaction_date <= ?
                WHERE coa.account_type = 'liabilities' AND coa.is_active = 1
                GROUP BY coa.account_id, coa.account_code, coa.account_name
                HAVING amount != 0
                ORDER BY coa.account_code
            ", [$asOfDate]);
            
            // حقوق الملكية
            $equity = $this->db->select("
                SELECT 
                    coa.account_id, coa.account_code, coa.account_name,
                    COALESCE(SUM(at.credit_amount) - SUM(at.debit_amount), 0) as amount
                FROM chart_of_accounts coa
                LEFT JOIN account_transactions at ON coa.account_id = at.account_id 
                    AND at.is_posted = 1 
                    AND at.transaction_date <= ?
                WHERE coa.account_type = 'equity' AND coa.is_active = 1
                GROUP BY coa.account_id, coa.account_code, coa.account_name
                HAVING amount != 0
                ORDER BY coa.account_code
            ", [$asOfDate]);
            
            // حساب الإجماليات
            $totalAssets = array_sum(array_column($assets, 'amount'));
            $totalLiabilities = array_sum(array_column($liabilities, 'amount'));
            $totalEquity = array_sum(array_column($equity, 'amount'));
            
            return [
                'assets' => $assets,
                'liabilities' => $liabilities,
                'equity' => $equity,
                'total_assets' => $totalAssets,
                'total_liabilities' => $totalLiabilities,
                'total_equity' => $totalEquity,
                'as_of_date' => $asOfDate
            ];
            
        } catch (Exception $e) {
            error_log("Get balance sheet error: " . $e->getMessage());
            return [
                'assets' => [],
                'liabilities' => [],
                'equity' => [],
                'total_assets' => 0,
                'total_liabilities' => 0,
                'total_equity' => 0,
                'as_of_date' => $asOfDate
            ];
        }
    }
    
    /**
     * تقرير المبيعات
     */
    public function getSalesReport($dateFrom, $dateTo, $groupBy = 'daily') {
        try {
            $dateFormat = $this->getDateFormat($groupBy);
            
            $salesData = $this->db->select("
                SELECT 
                    {$dateFormat} as period,
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_sales,
                    SUM(subtotal) as subtotal,
                    SUM(tax_amount) as total_tax,
                    SUM(discount_amount) as total_discount,
                    AVG(total_amount) as avg_invoice_value
                FROM invoices 
                WHERE invoice_type = 'sales' 
                    AND status = 'posted'
                    AND invoice_date BETWEEN ? AND ?
                GROUP BY {$dateFormat}
                ORDER BY period
            ", [$dateFrom, $dateTo]);
            
            // تقرير المبيعات حسب العميل
            $salesByCustomer = $this->db->select("
                SELECT 
                    c.customer_name, c.customer_code,
                    COUNT(i.invoice_id) as invoice_count,
                    SUM(i.total_amount) as total_sales
                FROM invoices i
                JOIN customers c ON i.customer_id = c.customer_id
                WHERE i.invoice_type = 'sales' 
                    AND i.status = 'posted'
                    AND i.invoice_date BETWEEN ? AND ?
                GROUP BY c.customer_id, c.customer_name, c.customer_code
                ORDER BY total_sales DESC
                LIMIT 10
            ", [$dateFrom, $dateTo]);
            
            // تقرير المبيعات حسب الصنف
            $salesByItem = $this->db->select("
                SELECT 
                    it.item_name, it.item_code,
                    SUM(ii.quantity) as total_quantity,
                    SUM(ii.total_amount) as total_sales,
                    AVG(ii.unit_price) as avg_price
                FROM invoice_items ii
                JOIN invoices i ON ii.invoice_id = i.invoice_id
                JOIN items it ON ii.item_id = it.item_id
                WHERE i.invoice_type = 'sales' 
                    AND i.status = 'posted'
                    AND i.invoice_date BETWEEN ? AND ?
                GROUP BY it.item_id, it.item_name, it.item_code
                ORDER BY total_sales DESC
                LIMIT 10
            ", [$dateFrom, $dateTo]);
            
            return [
                'sales_data' => $salesData,
                'sales_by_customer' => $salesByCustomer,
                'sales_by_item' => $salesByItem,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'group_by' => $groupBy
            ];
            
        } catch (Exception $e) {
            error_log("Get sales report error: " . $e->getMessage());
            return [
                'sales_data' => [],
                'sales_by_customer' => [],
                'sales_by_item' => [],
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'group_by' => $groupBy
            ];
        }
    }
    
    /**
     * تقرير المشتريات
     */
    public function getPurchaseReport($dateFrom, $dateTo, $groupBy = 'daily') {
        try {
            $dateFormat = $this->getDateFormat($groupBy);
            
            $purchaseData = $this->db->select("
                SELECT 
                    {$dateFormat} as period,
                    COUNT(*) as invoice_count,
                    SUM(total_amount) as total_purchases,
                    SUM(subtotal) as subtotal,
                    SUM(tax_amount) as total_tax,
                    SUM(discount_amount) as total_discount,
                    AVG(total_amount) as avg_invoice_value
                FROM invoices 
                WHERE invoice_type = 'purchase' 
                    AND status = 'posted'
                    AND invoice_date BETWEEN ? AND ?
                GROUP BY {$dateFormat}
                ORDER BY period
            ", [$dateFrom, $dateTo]);
            
            // تقرير المشتريات حسب المورد
            $purchaseBySupplier = $this->db->select("
                SELECT 
                    c.customer_name as supplier_name, c.customer_code as supplier_code,
                    COUNT(i.invoice_id) as invoice_count,
                    SUM(i.total_amount) as total_purchases
                FROM invoices i
                JOIN customers c ON i.customer_id = c.customer_id
                WHERE i.invoice_type = 'purchase' 
                    AND i.status = 'posted'
                    AND i.invoice_date BETWEEN ? AND ?
                GROUP BY c.customer_id, c.customer_name, c.customer_code
                ORDER BY total_purchases DESC
                LIMIT 10
            ", [$dateFrom, $dateTo]);
            
            return [
                'purchase_data' => $purchaseData,
                'purchase_by_supplier' => $purchaseBySupplier,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'group_by' => $groupBy
            ];
            
        } catch (Exception $e) {
            error_log("Get purchase report error: " . $e->getMessage());
            return [
                'purchase_data' => [],
                'purchase_by_supplier' => [],
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'group_by' => $groupBy
            ];
        }
    }
    
    /**
     * تقرير المخزون
     */
    public function getInventoryReport($warehouseId = null, $categoryId = null) {
        try {
            $query = "
                SELECT 
                    i.item_code, i.item_name, c.category_name, u.unit_symbol,
                    w.warehouse_name, w.warehouse_code,
                    ib.available_quantity, ib.reserved_quantity,
                    ib.average_cost, ib.total_value,
                    i.min_stock_level, i.max_stock_level,
                    CASE 
                        WHEN ib.available_quantity <= 0 THEN 'نفد'
                        WHEN ib.available_quantity <= i.min_stock_level THEN 'منخفض'
                        WHEN ib.available_quantity >= i.max_stock_level AND i.max_stock_level > 0 THEN 'مرتفع'
                        ELSE 'طبيعي'
                    END as stock_status
                FROM inventory_balances ib
                JOIN items i ON ib.item_id = i.item_id
                JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
                LEFT JOIN categories c ON i.category_id = c.category_id
                LEFT JOIN units u ON i.unit_id = u.unit_id
                WHERE i.is_active = 1 AND w.is_active = 1
            ";
            
            $params = [];
            
            if ($warehouseId) {
                $query .= " AND ib.warehouse_id = ?";
                $params[] = $warehouseId;
            }
            
            if ($categoryId) {
                $query .= " AND i.category_id = ?";
                $params[] = $categoryId;
            }
            
            $query .= " ORDER BY i.item_code, w.warehouse_code";
            
            $inventoryData = $this->db->select($query, $params);
            
            // إحصائيات المخزون
            $inventoryStats = $this->db->selectOne("
                SELECT 
                    COUNT(DISTINCT ib.item_id) as total_items,
                    COUNT(DISTINCT ib.warehouse_id) as total_warehouses,
                    SUM(ib.total_value) as total_inventory_value,
                    SUM(CASE WHEN ib.available_quantity <= 0 THEN 1 ELSE 0 END) as out_of_stock_items,
                    SUM(CASE WHEN ib.available_quantity <= i.min_stock_level THEN 1 ELSE 0 END) as low_stock_items
                FROM inventory_balances ib
                JOIN items i ON ib.item_id = i.item_id
                JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
                WHERE i.is_active = 1 AND w.is_active = 1
                " . ($warehouseId ? " AND ib.warehouse_id = {$warehouseId}" : "") .
                ($categoryId ? " AND i.category_id = {$categoryId}" : "")
            );
            
            return [
                'inventory_data' => $inventoryData,
                'inventory_stats' => $inventoryStats ?: [
                    'total_items' => 0,
                    'total_warehouses' => 0,
                    'total_inventory_value' => 0,
                    'out_of_stock_items' => 0,
                    'low_stock_items' => 0
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Get inventory report error: " . $e->getMessage());
            return [
                'inventory_data' => [],
                'inventory_stats' => [
                    'total_items' => 0,
                    'total_warehouses' => 0,
                    'total_inventory_value' => 0,
                    'out_of_stock_items' => 0,
                    'low_stock_items' => 0
                ]
            ];
        }
    }
    
    /**
     * تقرير أعمار الديون
     */
    public function getAgeingReport($asOfDate, $type = 'receivables') {
        try {
            $customerType = $type === 'receivables' ? 'customer' : 'supplier';
            $balanceCondition = $type === 'receivables' ? 'account_balance > 0' : 'account_balance < 0';
            
            $ageingData = $this->db->select("
                SELECT 
                    c.customer_code, c.customer_name, c.phone, c.email,
                    ABS(c.account_balance) as balance,
                    DATEDIFF(?, MAX(i.invoice_date)) as days_outstanding,
                    CASE 
                        WHEN DATEDIFF(?, MAX(i.invoice_date)) <= 30 THEN 'current'
                        WHEN DATEDIFF(?, MAX(i.invoice_date)) <= 60 THEN '31_60'
                        WHEN DATEDIFF(?, MAX(i.invoice_date)) <= 90 THEN '61_90'
                        ELSE 'over_90'
                    END as age_category
                FROM customers c
                LEFT JOIN invoices i ON c.customer_id = i.customer_id 
                    AND i.payment_status = 'unpaid'
                    AND i.invoice_date <= ?
                WHERE c.customer_type IN (?, 'both') 
                    AND c.is_active = 1 
                    AND {$balanceCondition}
                GROUP BY c.customer_id, c.customer_code, c.customer_name, c.phone, c.email, c.account_balance
                ORDER BY balance DESC
            ", [$asOfDate, $asOfDate, $asOfDate, $asOfDate, $asOfDate, $customerType]);
            
            return [
                'ageing_data' => $ageingData,
                'as_of_date' => $asOfDate,
                'type' => $type
            ];
            
        } catch (Exception $e) {
            error_log("Get ageing report error: " . $e->getMessage());
            return [
                'ageing_data' => [],
                'as_of_date' => $asOfDate,
                'type' => $type
            ];
        }
    }
    
    /**
     * تنسيق التاريخ حسب نوع التجميع
     */
    private function getDateFormat($groupBy) {
        switch ($groupBy) {
            case 'daily':
                return "DATE(invoice_date)";
            case 'weekly':
                return "YEARWEEK(invoice_date)";
            case 'monthly':
                return "DATE_FORMAT(invoice_date, '%Y-%m')";
            case 'yearly':
                return "YEAR(invoice_date)";
            default:
                return "DATE(invoice_date)";
        }
    }

    /**
     * ملخص أعمار الديون
     */
    public function getAgeingSummary($filters = []) {
        try {
            $query = "SELECT
                        COUNT(CASE WHEN DATEDIFF(CURDATE(), i.due_date) <= 0 THEN 1 END) as current_count,
                        SUM(CASE WHEN DATEDIFF(CURDATE(), i.due_date) <= 0 THEN (i.total_amount - i.paid_amount) ELSE 0 END) as current_amount,

                        COUNT(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 1 AND 30 THEN 1 END) as days_1_30_count,
                        SUM(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 1 AND 30 THEN (i.total_amount - i.paid_amount) ELSE 0 END) as days_1_30_amount,

                        COUNT(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 31 AND 60 THEN 1 END) as days_31_60_count,
                        SUM(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 31 AND 60 THEN (i.total_amount - i.paid_amount) ELSE 0 END) as days_31_60_amount,

                        COUNT(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 61 AND 90 THEN 1 END) as days_61_90_count,
                        SUM(CASE WHEN DATEDIFF(CURDATE(), i.due_date) BETWEEN 61 AND 90 THEN (i.total_amount - i.paid_amount) ELSE 0 END) as days_61_90_amount,

                        COUNT(CASE WHEN DATEDIFF(CURDATE(), i.due_date) > 90 THEN 1 END) as over_90_count,
                        SUM(CASE WHEN DATEDIFF(CURDATE(), i.due_date) > 90 THEN (i.total_amount - i.paid_amount) ELSE 0 END) as over_90_amount,

                        COUNT(*) as total_count,
                        SUM(i.total_amount - i.paid_amount) as total_amount
                      FROM invoices i
                      WHERE i.payment_status != 'paid'
                      AND i.status = 'active'
                      AND (i.total_amount - i.paid_amount) > 0";

            $params = [];

            // فلترة حسب العميل
            if (!empty($filters['customer_id'])) {
                $query .= " AND i.customer_id = ?";
                $params[] = $filters['customer_id'];
            }

            // فلترة حسب نوع الفاتورة
            if (!empty($filters['invoice_type'])) {
                $query .= " AND i.invoice_type = ?";
                $params[] = $filters['invoice_type'];
            }

            return $this->db->selectOne($query, $params);

        } catch (Exception $e) {
            error_log("Ageing summary error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * كشف حساب العميل
     */
    public function getCustomerStatement($customerId, $filters = []) {
        try {
            $query = "SELECT
                        'invoice' as transaction_type,
                        i.invoice_id as reference_id,
                        i.invoice_number as reference_number,
                        i.invoice_date as transaction_date,
                        i.description,
                        CASE WHEN i.invoice_type = 'sales' THEN i.total_amount ELSE 0 END as debit_amount,
                        CASE WHEN i.invoice_type = 'sales_return' THEN i.total_amount ELSE 0 END as credit_amount,
                        i.payment_status,
                        i.due_date
                      FROM invoices i
                      WHERE i.customer_id = ? AND i.status = 'active'";

            $params = [$customerId];

            // فلترة حسب التاريخ من
            if (!empty($filters['date_from'])) {
                $query .= " AND i.invoice_date >= ?";
                $params[] = $filters['date_from'];
            }

            // فلترة حسب التاريخ إلى
            if (!empty($filters['date_to'])) {
                $query .= " AND i.invoice_date <= ?";
                $params[] = $filters['date_to'];
            }

            $query .= " ORDER BY transaction_date DESC, reference_number";

            return $this->db->select($query, $params);

        } catch (Exception $e) {
            error_log("Customer statement error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * رصيد العميل
     */
    public function getCustomerBalance($customerId, $asOfDate = null) {
        try {
            $dateCondition = $asOfDate ? "AND i.invoice_date <= ?" : "";
            $params = [$customerId];
            if ($asOfDate) {
                $params[] = $asOfDate;
            }

            // إجمالي المبيعات
            $salesQuery = "SELECT COALESCE(SUM(total_amount), 0) as total_sales
                          FROM invoices
                          WHERE customer_id = ? AND invoice_type = 'sales' AND status = 'active' {$dateCondition}";

            $salesResult = $this->db->selectOne($salesQuery, $params);

            // إجمالي مرتجع المبيعات
            $returnsQuery = "SELECT COALESCE(SUM(total_amount), 0) as total_returns
                            FROM invoices
                            WHERE customer_id = ? AND invoice_type = 'sales_return' AND status = 'active' {$dateCondition}";

            $returnsResult = $this->db->selectOne($returnsQuery, $params);

            $totalSales = $salesResult['total_sales'] ?? 0;
            $totalReturns = $returnsResult['total_returns'] ?? 0;

            $balance = $totalSales - $totalReturns;

            return [
                'total_sales' => $totalSales,
                'total_returns' => $totalReturns,
                'balance' => $balance,
                'balance_type' => $balance >= 0 ? 'debit' : 'credit'
            ];

        } catch (Exception $e) {
            error_log("Customer balance error: " . $e->getMessage());
            return [
                'total_sales' => 0,
                'total_returns' => 0,
                'balance' => 0,
                'balance_type' => 'debit'
            ];
        }
    }
}

?>

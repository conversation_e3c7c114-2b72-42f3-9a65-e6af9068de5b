# نظام الحسابات والمخازن - دليل قاعدة البيانات

## نظرة عامة
هذا المشروع يحتوي على قاعدة بيانات MySQL كاملة لنظام الحسابات والمخازن مع دعم الأصناف متعددة الوحدات. تم تصميم القاعدة بترميز UTF-8 لدعم اللغة العربية بالكامل.

## متطلبات النظام
- MySQL 8.0 أو أحدث
- دعم UTF-8 (utf8mb4)
- مساحة تخزين: 100 ميجابايت على الأقل

## تثبيت قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```bash
mysql -u root -p < database_schema.sql
```

### 2. التحقق من التثبيت
```sql
USE accounting_inventory_system;
SHOW TABLES;
```

## هيكل قاعدة البيانات

### الجداول الأساسية

#### 1. جدول المستخدمين (users)
- **الغرض**: إدارة المستخدمين وصلاحياتهم
- **الحقول الرئيسية**: username, password_hash, full_name, role, permissions
- **الأدوار المتاحة**: admin, accountant, warehouse_manager, sales, user

#### 2. جدول الفروع (branches)
- **الغرض**: إدارة فروع الشركة
- **الحقول الرئيسية**: branch_code, branch_name, address, manager_name

#### 3. جدول شجرة الحسابات (chart_of_accounts)
- **الغرض**: الهيكل المحاسبي للشركة
- **الحقول الرئيسية**: account_code, account_name, account_type, parent_account_id
- **أنواع الحسابات**: assets, liabilities, equity, revenue, expenses

#### 4. جدول حركة الحسابات (account_transactions)
- **الغرض**: تسجيل جميع القيود المحاسبية
- **الحقول الرئيسية**: voucher_number, transaction_date, account_id, debit_amount, credit_amount

#### 5. جدول فئات الأصناف (item_categories)
- **الغرض**: تصنيف الأصناف في مجموعات
- **الحقول الرئيسية**: category_code, category_name, parent_category_id

#### 6. جدول الوحدات (units)
- **الغرض**: تعريف وحدات القياس المختلفة
- **الحقول الرئيسية**: unit_code, unit_name, unit_symbol, unit_type
- **أنواع الوحدات**: weight, length, volume, piece, area, time, other

#### 7. جدول الأصناف (items)
- **الغرض**: تعريف الأصناف الأساسية
- **الحقول الرئيسية**: item_code, item_name, category_id, base_unit_id
- **الميزات**: دعم الباركود، تواريخ الصلاحية، الأرقام التسلسلية

#### 8. جدول وحدات الأصناف (item_units)
- **الغرض**: ربط الأصناف بوحدات متعددة مع معاملات التحويل
- **الحقول الرئيسية**: item_id, unit_id, conversion_factor, purchase_price, sale_price

#### 9. جدول المخازن (warehouses)
- **الغرض**: إدارة المخازن المختلفة
- **الحقول الرئيسية**: warehouse_code, warehouse_name, warehouse_location, warehouse_type

#### 10. جدول أرصدة المخزون (inventory_balances)
- **الغرض**: تتبع أرصدة الأصناف في المخازن
- **الحقول الرئيسية**: item_id, warehouse_id, unit_id, quantity, average_cost

#### 11. جدول حركات المخزون (inventory_movements)
- **الغرض**: تسجيل جميع حركات المخزون
- **الحقول الرئيسية**: movement_number, movement_date, movement_type, item_id, warehouse_id

## البيانات التجريبية

### المستخدمون
- **admin**: المدير العام (كلمة المرور: password)

### الفروع
- **BR001**: الفرع الرئيسي - الرياض
- **BR002**: فرع جدة

### الأصناف التجريبية
1. **ITM001**: حليب كامل الدسم
2. **ITM002**: أرز بسمتي
3. **ITM003**: دجاج مجمد
4. **ITM004**: عصير برتقال
5. **ITM005**: خبز أبيض

### المخازن
- **WH001**: المخزن الرئيسي - الرياض
- **WH002**: مخزن جدة
- **WH003**: مخزن مؤقت

## Views المفيدة

### 1. v_items_with_units
عرض تفصيلي للأصناف مع وحداتها الأساسية
```sql
SELECT * FROM v_items_with_units;
```

### 2. v_current_inventory
عرض أرصدة المخزون الحالية مع حالة المخزون
```sql
SELECT * FROM v_current_inventory;
```

### 3. v_chart_of_accounts_with_balances
عرض شجرة الحسابات مع الأرصدة
```sql
SELECT * FROM v_chart_of_accounts_with_balances;
```

## Stored Procedures

### 1. sp_calculate_total_inventory_value
حساب إجمالي قيمة المخزون
```sql
CALL sp_calculate_total_inventory_value(1, CURDATE(), @total_value);
SELECT @total_value;
```

### 2. sp_update_inventory_balance
تحديث أرصدة المخزون بعد الحركة
```sql
CALL sp_update_inventory_balance(1, 1, 3, 10.000000, 3.50, 'in');
```

## استعلامات مفيدة

### 1. عرض الأصناف مع وحداتها المتعددة
```sql
SELECT 
    i.item_name,
    u.unit_name,
    iu.conversion_factor,
    iu.sale_price
FROM items i
JOIN item_units iu ON i.item_id = iu.item_id
JOIN units u ON iu.unit_id = u.unit_id
WHERE i.is_active = TRUE;
```

### 2. تقرير أرصدة المخزون
```sql
SELECT 
    i.item_name,
    w.warehouse_name,
    ib.quantity,
    u.unit_symbol,
    ib.total_value
FROM inventory_balances ib
JOIN items i ON ib.item_id = i.item_id
JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
JOIN units u ON ib.unit_id = u.unit_id
WHERE ib.quantity > 0;
```

### 3. ميزان المراجعة
```sql
SELECT 
    account_code,
    account_name,
    total_debit,
    total_credit,
    balance
FROM v_chart_of_accounts_with_balances
ORDER BY account_code;
```

## الأمان والصيانة

### النسخ الاحتياطي
```bash
mysqldump -u root -p accounting_inventory_system > backup_$(date +%Y%m%d).sql
```

### الاستعادة
```bash
mysql -u root -p accounting_inventory_system < backup_file.sql
```

### تحسين الأداء
- تم إنشاء فهارس مناسبة لجميع الجداول
- استخدام Triggers للتحديث التلقائي
- Views محسنة للاستعلامات المتكررة

## الدعم والتطوير
لأي استفسارات أو تطوير إضافي، يرجى مراجعة التوثيق التقني أو التواصل مع فريق التطوير.

<?php
/**
 * اختبار سريع لجدول العملاء
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار سريع لجدول العملاء</h1>";

try {
    $db = Database::getInstance();
    
    // فحص وجود الجدول
    echo "<h2>1. فحص وجود الجدول:</h2>";
    $tables = $db->select("SHOW TABLES LIKE 'customers'");
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ جدول العملاء غير موجود</p>";
        echo "<p><a href='setup_customers.php'>اضغط هنا لإنشاء الجدول</a></p>";
    } else {
        echo "<p style='color: green;'>✅ جدول العملاء موجود</p>";
        
        // عرض هيكل الجدول
        echo "<h2>2. هيكل الجدول:</h2>";
        $structure = $db->select("DESCRIBE customers");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th></tr>";
        foreach ($structure as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عدد السجلات
        echo "<h2>3. عدد السجلات:</h2>";
        $count = $db->selectOne("SELECT COUNT(*) as count FROM customers");
        echo "<p>عدد العملاء: " . $count['count'] . "</p>";
        
        // عرض البيانات
        if ($count['count'] > 0) {
            echo "<h2>4. البيانات الموجودة:</h2>";
            $customers = $db->select("SELECT customer_code, customer_name, customer_type, phone FROM customers LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>الكود</th><th>الاسم</th><th>النوع</th><th>الهاتف</th></tr>";
            foreach ($customers as $customer) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($customer['customer_code']) . "</td>";
                echo "<td>" . htmlspecialchars($customer['customer_name']) . "</td>";
                echo "<td>" . htmlspecialchars($customer['customer_type']) . "</td>";
                echo "<td>" . htmlspecialchars($customer['phone']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // اختبار نموذج العميل
    echo "<h2>5. اختبار نموذج العميل:</h2>";
    try {
        $customerModel = new Customer();
        echo "<p style='color: green;'>✅ نموذج العميل تم تحميله بنجاح</p>";
        
        $customers = $customerModel->getAllCustomers();
        echo "<p>عدد العملاء من النموذج: " . count($customers) . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في نموذج العميل: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='setup_customers.php'>إعداد جدول العملاء</a> | ";
echo "<a href='customers.php'>إدارة العملاء</a> | ";
echo "<a href='index.php'>الرئيسية</a></p>";
?>

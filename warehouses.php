<?php
/**
 * صفحة إدارة المخازن
 * Warehouses Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('warehouses_manage');

// إنشاء مثيلات النماذج
$warehouseModel = new Warehouse();
$inventoryModel = new Inventory();

// الحصول على قائمة الفروع للاختيار
$branches = [];
try {
    $branches = $database->select("SELECT branch_id, branch_name FROM branches WHERE is_active = 1 ORDER BY branch_name");
} catch (Exception $e) {
    error_log("Get branches error: " . $e->getMessage());
}

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$warehouseId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $warehouseId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($warehouseModel->deleteWarehouse($warehouseId)) {
                    setAlert('تم حذف المخزن بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف المخزن', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('warehouses.php');
    }
}

// معالجة إضافة/تعديل المخزن
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'warehouse_code' => sanitizeInput($_POST['warehouse_code'] ?? ''),
            'warehouse_name' => sanitizeInput($_POST['warehouse_name'] ?? ''),
            'warehouse_location' => sanitizeInput($_POST['warehouse_location'] ?? ''),
            'warehouse_type' => sanitizeInput($_POST['warehouse_type'] ?? 'main'),
            'manager_name' => sanitizeInput($_POST['manager_name'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'branch_id' => !empty($_POST['branch_id']) ? (int)$_POST['branch_id'] : null,
            'capacity' => !empty($_POST['capacity']) ? (float)$_POST['capacity'] : null,
            'capacity_unit' => sanitizeInput($_POST['capacity_unit'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'allow_negative_stock' => isset($_POST['allow_negative_stock']) ? 1 : 0,
            'notes' => sanitizeInput($_POST['notes'] ?? '')
        ];
        
        // التحقق من البيانات المطلوبة
        if (empty($data['warehouse_code']) || empty($data['warehouse_name'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                if ($action === 'add') {
                    $newWarehouseId = $warehouseModel->createWarehouse($data);
                    if ($newWarehouseId) {
                        setAlert('تم إضافة المخزن بنجاح', 'success');
                        redirect('warehouses.php');
                    } else {
                        $error = 'فشل في إضافة المخزن';
                    }
                } else {
                    if ($warehouseModel->updateWarehouse($warehouseId, $data)) {
                        setAlert('تم تحديث المخزن بنجاح', 'success');
                        redirect('warehouses.php');
                    } else {
                        $error = 'فشل في تحديث المخزن';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentWarehouse = null;
if (($action === 'edit' || $action === 'view') && $warehouseId) {
    $currentWarehouse = $warehouseModel->getWarehouseById($warehouseId);
    if (!$currentWarehouse) {
        setAlert('المخزن غير موجود', 'error');
        redirect('warehouses.php');
    }
}

// الحصول على قائمة المخازن للعرض
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$warehousesPerPage = 20;

if (!empty($searchTerm)) {
    $warehouses = $warehouseModel->searchWarehouses($searchTerm);
    $totalWarehouses = count($warehouses);
} else {
    $warehouses = $warehouseModel->getAllWarehouses();
    $totalWarehouses = count($warehouses);
}

// إحصائيات المخازن
$warehouseStats = [];
foreach ($warehouses as $warehouse) {
    $warehouseStats[$warehouse['warehouse_id']] = $warehouseModel->getWarehouseStats($warehouse['warehouse_id']);
}

$pageTitle = $action === 'add' ? 'إضافة مخزن جديد' : 
            ($action === 'edit' ? 'تعديل المخزن' : 
            ($action === 'view' ? 'عرض تفاصيل المخزن' : 'إدارة المخازن'));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-warehouse ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- قائمة المخازن -->
            <div class="bg-white shadow-lg rounded-lg">
                
                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قائمة المخازن (<?php echo number_format($totalWarehouses); ?> مخزن)
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="inventory.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-boxes ml-2"></i>
                            أرصدة المخزون
                        </a>
                        <a href="warehouses.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة مخزن جديد
                        </a>
                    </div>
                </div>
                
                <!-- شريط البحث -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <form method="GET" action="warehouses.php" class="flex gap-4">
                        <div class="flex-1">
                            <input 
                                type="text" 
                                name="search" 
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="البحث في المخازن (الاسم، الكود، الموقع)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm)): ?>
                            <a href="warehouses.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- بطاقات المخازن -->
                <div class="p-6">
                    <?php if (empty($warehouses)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-warehouse text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مخازن</h3>
                            <p class="text-gray-500 mb-4">لم يتم العثور على أي مخازن مطابقة للبحث</p>
                            <a href="warehouses.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول مخزن
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($warehouses as $warehouse): ?>
                                <?php $stats = $warehouseStats[$warehouse['warehouse_id']] ?? []; ?>
                                <div class="bg-white border border-gray-200 rounded-lg shadow-sm card-hover">

                                    <!-- رأس البطاقة -->
                                    <div class="p-4 border-b border-gray-200">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                                </h4>
                                                <p class="text-sm text-gray-500">
                                                    كود: <?php echo htmlspecialchars($warehouse['warehouse_code']); ?>
                                                </p>
                                            </div>
                                            <div class="flex space-x-1 space-x-reverse">
                                                <?php if ($warehouse['is_active']): ?>
                                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                        نشط
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                        غير نشط
                                                    </span>
                                                <?php endif; ?>

                                                <?php
                                                $typeLabels = [
                                                    'main' => 'رئيسي',
                                                    'branch' => 'فرع',
                                                    'temporary' => 'مؤقت',
                                                    'virtual' => 'افتراضي'
                                                ];
                                                $typeColors = [
                                                    'main' => 'bg-blue-100 text-blue-800',
                                                    'branch' => 'bg-purple-100 text-purple-800',
                                                    'temporary' => 'bg-yellow-100 text-yellow-800',
                                                    'virtual' => 'bg-gray-100 text-gray-800'
                                                ];
                                                ?>
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full <?php echo $typeColors[$warehouse['warehouse_type']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                                    <?php echo $typeLabels[$warehouse['warehouse_type']] ?? $warehouse['warehouse_type']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- محتوى البطاقة -->
                                    <div class="p-4">

                                        <!-- معلومات أساسية -->
                                        <div class="space-y-2 mb-4">
                                            <?php if (!empty($warehouse['warehouse_location'])): ?>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-map-marker-alt w-4 ml-2"></i>
                                                    <?php echo htmlspecialchars($warehouse['warehouse_location']); ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($warehouse['branch_name'])): ?>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-building w-4 ml-2"></i>
                                                    <?php echo htmlspecialchars($warehouse['branch_name']); ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($warehouse['manager_name'])): ?>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-user w-4 ml-2"></i>
                                                    <?php echo htmlspecialchars($warehouse['manager_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- إحصائيات -->
                                        <div class="grid grid-cols-2 gap-4 mb-4">
                                            <div class="text-center">
                                                <div class="text-2xl font-bold text-blue-600">
                                                    <?php echo number_format($stats['total_items'] ?? 0); ?>
                                                </div>
                                                <div class="text-xs text-gray-500">إجمالي الأصناف</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="text-2xl font-bold text-green-600">
                                                    <?php echo formatMoney($stats['total_value'] ?? 0); ?>
                                                </div>
                                                <div class="text-xs text-gray-500">قيمة المخزون</div>
                                            </div>
                                        </div>

                                        <!-- تنبيهات -->
                                        <?php if (($stats['low_stock_items'] ?? 0) > 0): ?>
                                            <div class="bg-yellow-50 border border-yellow-200 rounded p-2 mb-4">
                                                <div class="flex items-center text-sm text-yellow-800">
                                                    <i class="fas fa-exclamation-triangle ml-2"></i>
                                                    <?php echo number_format($stats['low_stock_items']); ?> صنف منخفض المخزون
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (($stats['out_of_stock_items'] ?? 0) > 0): ?>
                                            <div class="bg-red-50 border border-red-200 rounded p-2 mb-4">
                                                <div class="flex items-center text-sm text-red-800">
                                                    <i class="fas fa-times-circle ml-2"></i>
                                                    <?php echo number_format($stats['out_of_stock_items']); ?> صنف نفد من المخزون
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                    </div>

                                    <!-- أزرار الإجراءات -->
                                    <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                        <div class="flex justify-between items-center">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="inventory.php?warehouse=<?php echo $warehouse['warehouse_id']; ?>"
                                                   class="text-green-600 hover:text-green-800 text-sm">
                                                    <i class="fas fa-boxes ml-1"></i>
                                                    المخزون
                                                </a>
                                                <a href="warehouses.php?action=view&id=<?php echo $warehouse['warehouse_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                                    <i class="fas fa-eye ml-1"></i>
                                                    عرض
                                                </a>
                                            </div>
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="warehouses.php?action=edit&id=<?php echo $warehouse['warehouse_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="warehouses.php?action=delete&id=<?php echo $warehouse['warehouse_id']; ?>"
                                                   onclick="return confirm('هل أنت متأكد من حذف المخزن \'<?php echo htmlspecialchars($warehouse['warehouse_name'], ENT_QUOTES); ?>\'؟')"
                                                   class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

            </div>

        <?php elseif ($action === 'view' && $currentWarehouse): ?>
            <!-- عرض تفاصيل المستودع -->
            <div class="bg-white shadow-lg rounded-lg">

                <!-- رأس التفاصيل -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-eye ml-2"></i>
                        تفاصيل المستودع: <?php echo htmlspecialchars($currentWarehouse['warehouse_name']); ?>
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="warehouses.php?action=edit&id=<?php echo $currentWarehouse['warehouse_id']; ?>"
                           class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit ml-2"></i>
                            تعديل
                        </a>
                        <a href="warehouses.php"
                           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

                        <!-- المعلومات الأساسية -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                                <i class="fas fa-info-circle ml-2"></i>
                                المعلومات الأساسية
                            </h4>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم المستودع</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['warehouse_name']); ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">كود المستودع</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['warehouse_code']); ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الموقع</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['warehouse_location'] ?: 'غير محدد'); ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">نوع المستودع</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php
                                        $typeLabels = [
                                            'main' => 'رئيسي',
                                            'branch' => 'فرع',
                                            'temporary' => 'مؤقت',
                                            'virtual' => 'افتراضي'
                                        ];
                                        echo $typeLabels[$currentWarehouse['warehouse_type']] ?? $currentWarehouse['warehouse_type'];
                                        ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php if ($currentWarehouse['is_active']): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle ml-1"></i>
                                                نشط
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                <i class="fas fa-times-circle ml-1"></i>
                                                غير نشط
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال والإدارة -->
                        <div class="space-y-6">
                            <h4 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                                <i class="fas fa-user-tie ml-2"></i>
                                معلومات الإدارة والاتصال
                            </h4>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم المدير</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['manager_name'] ?: 'غير محدد'); ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['phone'] ?: 'غير محدد'); ?>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo htmlspecialchars($currentWarehouse['email'] ?: 'غير محدد'); ?>
                                    </p>
                                </div>

                                <?php if (!empty($currentWarehouse['capacity'])): ?>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">السعة التخزينية</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php echo number_format($currentWarehouse['capacity'], 2) . ' ' . htmlspecialchars($currentWarehouse['capacity_unit'] ?: ''); ?>
                                    </p>
                                </div>
                                <?php endif; ?>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">السماح بالمخزون السالب</label>
                                    <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                                        <?php if ($currentWarehouse['allow_negative_stock']): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                <i class="fas fa-check ml-1"></i>
                                                مسموح
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                <i class="fas fa-times ml-1"></i>
                                                غير مسموح
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <?php if (!empty($currentWarehouse['notes'])): ?>
                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4">
                            <i class="fas fa-sticky-note ml-2"></i>
                            الملاحظات
                        </h4>
                        <div class="bg-gray-50 px-4 py-3 rounded-lg">
                            <p class="text-gray-700 whitespace-pre-wrap"><?php echo htmlspecialchars($currentWarehouse['notes']); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- إحصائيات المستودع -->
                    <?php
                    $warehouseStats = $warehouseModel->getWarehouseStats($currentWarehouse['warehouse_id']);
                    if ($warehouseStats):
                    ?>
                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4">
                            <i class="fas fa-chart-bar ml-2"></i>
                            إحصائيات المستودع
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-boxes text-blue-600 text-2xl ml-3"></i>
                                    <div>
                                        <p class="text-sm text-blue-600 font-medium">إجمالي الأصناف</p>
                                        <p class="text-2xl font-bold text-blue-800"><?php echo number_format($warehouseStats['total_items'] ?? 0); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-cubes text-green-600 text-2xl ml-3"></i>
                                    <div>
                                        <p class="text-sm text-green-600 font-medium">إجمالي الكمية</p>
                                        <p class="text-2xl font-bold text-green-800"><?php echo number_format($warehouseStats['total_quantity'] ?? 0, 2); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-dollar-sign text-yellow-600 text-2xl ml-3"></i>
                                    <div>
                                        <p class="text-sm text-yellow-600 font-medium">إجمالي القيمة</p>
                                        <p class="text-2xl font-bold text-yellow-800"><?php echo number_format($warehouseStats['total_value'] ?? 0, 2); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- روابط سريعة -->
                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4">
                            <i class="fas fa-link ml-2"></i>
                            روابط سريعة
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <a href="inventory.php?warehouse=<?php echo $currentWarehouse['warehouse_id']; ?>"
                               class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-boxes ml-2"></i>
                                عرض المخزون
                            </a>
                            <a href="inventory_movements.php?warehouse_id=<?php echo $currentWarehouse['warehouse_id']; ?>"
                               class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-exchange-alt ml-2"></i>
                                حركات المخزون
                            </a>
                            <a href="reports.php?type=warehouse&id=<?php echo $currentWarehouse['warehouse_id']; ?>"
                               class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-chart-line ml-2"></i>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- نموذج إضافة/تعديل المخزن -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> ml-2"></i>
                        <?php echo $action === 'add' ? 'إضافة مخزن جديد' : 'تعديل المخزن'; ?>
                    </h3>
                </div>

                <form method="POST" action="" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- كود المخزن -->
                        <div>
                            <label for="warehouse_code" class="block text-sm font-medium text-gray-700 mb-2">
                                كود المخزن <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="warehouse_code"
                                name="warehouse_code"
                                value="<?php echo htmlspecialchars($currentWarehouse['warehouse_code'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                <?php echo $action === 'edit' ? 'readonly' : ''; ?>
                            >
                        </div>

                        <!-- اسم المخزن -->
                        <div>
                            <label for="warehouse_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم المخزن <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="warehouse_name"
                                name="warehouse_name"
                                value="<?php echo htmlspecialchars($currentWarehouse['warehouse_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                        </div>

                        <!-- نوع المخزن -->
                        <div>
                            <label for="warehouse_type" class="block text-sm font-medium text-gray-700 mb-2">
                                نوع المخزن
                            </label>
                            <select
                                id="warehouse_type"
                                name="warehouse_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="main" <?php echo ($currentWarehouse['warehouse_type'] ?? 'main') === 'main' ? 'selected' : ''; ?>>رئيسي</option>
                                <option value="branch" <?php echo ($currentWarehouse['warehouse_type'] ?? '') === 'branch' ? 'selected' : ''; ?>>فرع</option>
                                <option value="temporary" <?php echo ($currentWarehouse['warehouse_type'] ?? '') === 'temporary' ? 'selected' : ''; ?>>مؤقت</option>
                                <option value="virtual" <?php echo ($currentWarehouse['warehouse_type'] ?? '') === 'virtual' ? 'selected' : ''; ?>>افتراضي</option>
                            </select>
                        </div>

                        <!-- الفرع -->
                        <div>
                            <label for="branch_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الفرع التابع له
                            </label>
                            <select
                                id="branch_id"
                                name="branch_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">اختر الفرع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['branch_id']; ?>"
                                            <?php echo ($currentWarehouse['branch_id'] ?? '') == $branch['branch_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['branch_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- موقع المخزن -->
                        <div class="md:col-span-2">
                            <label for="warehouse_location" class="block text-sm font-medium text-gray-700 mb-2">
                                موقع المخزن
                            </label>
                            <input
                                type="text"
                                id="warehouse_location"
                                name="warehouse_location"
                                value="<?php echo htmlspecialchars($currentWarehouse['warehouse_location'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="العنوان التفصيلي للمخزن"
                            >
                        </div>

                        <!-- مسؤول المخزن -->
                        <div>
                            <label for="manager_name" class="block text-sm font-medium text-gray-700 mb-2">
                                مسؤول المخزن
                            </label>
                            <input
                                type="text"
                                id="manager_name"
                                name="manager_name"
                                value="<?php echo htmlspecialchars($currentWarehouse['manager_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <!-- رقم الهاتف -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                رقم الهاتف
                            </label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                value="<?php echo htmlspecialchars($currentWarehouse['phone'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value="<?php echo htmlspecialchars($currentWarehouse['email'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <!-- السعة التخزينية -->
                        <div>
                            <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">
                                السعة التخزينية
                            </label>
                            <input
                                type="number"
                                id="capacity"
                                name="capacity"
                                value="<?php echo $currentWarehouse['capacity'] ?? ''; ?>"
                                step="0.01"
                                min="0"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <!-- وحدة السعة -->
                        <div>
                            <label for="capacity_unit" class="block text-sm font-medium text-gray-700 mb-2">
                                وحدة قياس السعة
                            </label>
                            <select
                                id="capacity_unit"
                                name="capacity_unit"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">اختر الوحدة</option>
                                <option value="متر مربع" <?php echo ($currentWarehouse['capacity_unit'] ?? '') === 'متر مربع' ? 'selected' : ''; ?>>متر مربع</option>
                                <option value="متر مكعب" <?php echo ($currentWarehouse['capacity_unit'] ?? '') === 'متر مكعب' ? 'selected' : ''; ?>>متر مكعب</option>
                                <option value="طن" <?php echo ($currentWarehouse['capacity_unit'] ?? '') === 'طن' ? 'selected' : ''; ?>>طن</option>
                                <option value="كيلوجرام" <?php echo ($currentWarehouse['capacity_unit'] ?? '') === 'كيلوجرام' ? 'selected' : ''; ?>>كيلوجرام</option>
                            </select>
                        </div>

                    </div>

                    <!-- الملاحظات -->
                    <div class="mt-6">
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            ملاحظات إضافية
                        </label>
                        <textarea
                            id="notes"
                            name="notes"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="ملاحظات أو تعليقات إضافية"
                        ><?php echo htmlspecialchars($currentWarehouse['notes'] ?? ''); ?></textarea>
                    </div>

                    <!-- الخيارات الإضافية -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">خيارات إضافية</h4>
                        <div class="space-y-3">

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    value="1"
                                    <?php echo ($currentWarehouse['is_active'] ?? 1) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    المخزن نشط
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="allow_negative_stock"
                                    name="allow_negative_stock"
                                    value="1"
                                    <?php echo ($currentWarehouse['allow_negative_stock'] ?? 0) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="allow_negative_stock" class="mr-2 block text-sm text-gray-900">
                                    السماح بالمخزون السالب
                                </label>
                            </div>

                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-8 flex justify-end space-x-4 space-x-reverse">
                        <a href="warehouses.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            <?php echo $action === 'add' ? 'إضافة المخزن' : 'حفظ التغييرات'; ?>
                        </button>
                    </div>

                </form>
            </div>

        <?php endif; ?>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر النماذج */
        input:focus, select:focus, textarea:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين مظهر البطاقات */
        .card-hover:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تأثير التحميل للنماذج */
        form:target {
            opacity: 0.7;
            pointer-events: none;
        }
    </style>

</body>
</html>

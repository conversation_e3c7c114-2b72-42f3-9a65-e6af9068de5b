<?php
/**
 * صفحة الإشعارات
 * Notifications Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// إنشاء مثيل نموذج الإشعارات
$notificationModel = new Notification();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$notificationId = $_GET['id'] ?? null;

// معالجة تحديد كمقروء
if ($action === 'mark_read' && $notificationId) {
    if ($notificationModel->markAsRead($notificationId, $_SESSION['user_id'])) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false]);
    }
    exit;
}

// معالجة تحديد الكل كمقروء
if ($action === 'mark_all_read') {
    if ($notificationModel->markAllAsRead($_SESSION['user_id'])) {
        setAlert('تم تحديد جميع الإشعارات كمقروءة', 'success');
    } else {
        setAlert('فشل في تحديث الإشعارات', 'error');
    }
    redirect('notifications.php');
}

// معالجة الحذف
if ($action === 'delete' && $notificationId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            if ($notificationModel->deleteNotification($notificationId, $_SESSION['user_id'])) {
                setAlert('تم حذف الإشعار بنجاح', 'success');
            } else {
                setAlert('فشل في حذف الإشعار', 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('notifications.php');
    }
}

// معالجة تشغيل فحص الإشعارات
if ($action === 'run_checks') {
    $results = $notificationModel->runAllChecks();
    $totalNew = array_sum(array_filter($results));
    
    if ($totalNew > 0) {
        setAlert("تم إنشاء {$totalNew} إشعار جديد", 'success');
    } else {
        setAlert('لا توجد إشعارات جديدة', 'info');
    }
    
    redirect('notifications.php');
}

// إعداد الفلترة
$showUnreadOnly = isset($_GET['unread_only']) && $_GET['unread_only'] === '1';

// الحصول على البيانات
$notifications = $notificationModel->getUserNotifications($_SESSION['user_id'], $showUnreadOnly);
$notificationStats = $notificationModel->getNotificationStats($_SESSION['user_id']);

$pageTitle = 'الإشعارات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض الإشعارات */
        .notification-item {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }
        
        .notification-unread {
            background-color: #f0f9ff;
            border-right-color: #3b82f6;
        }
        
        .notification-read {
            background-color: #f9fafb;
            border-right-color: #d1d5db;
        }
        
        .priority-high { border-right-color: #ef4444; }
        .priority-medium { border-right-color: #f59e0b; }
        .priority-low { border-right-color: #10b981; }
        
        .notification-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
        }
        
        .icon-low_stock { background-color: #ef4444; }
        .icon-overdue_invoice { background-color: #f59e0b; }
        .icon-backup_warning { background-color: #8b5cf6; }
        .icon-no_backup { background-color: #dc2626; }
        .icon-system { background-color: #6b7280; }
        .icon-default { background-color: #3b82f6; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-bell ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- إجمالي الإشعارات -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-bell text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الإشعارات</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($notificationStats['general']['total_notifications']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- غير المقروءة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">غير مقروءة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($notificationStats['general']['unread_notifications']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- عالية الأولوية -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">عالية الأولوية</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($notificationStats['general']['high_priority_unread']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إشعارات اليوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-day text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إشعارات اليوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($notificationStats['general']['today_notifications']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- قائمة الإشعارات -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس القائمة -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    الإشعارات (<?php echo count($notifications); ?> إشعار)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="notifications.php?action=run_checks" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-sync ml-2"></i>
                        فحص جديد
                    </a>
                    <?php if ($notificationStats['general']['unread_notifications'] > 0): ?>
                        <a href="notifications.php?action=mark_all_read" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-check-double ml-2"></i>
                            تحديد الكل كمقروء
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- شريط الفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex space-x-4 space-x-reverse">
                    <a href="notifications.php" 
                       class="px-4 py-2 rounded-lg transition-colors duration-200 <?php echo !$showUnreadOnly ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'; ?>">
                        جميع الإشعارات
                    </a>
                    <a href="notifications.php?unread_only=1" 
                       class="px-4 py-2 rounded-lg transition-colors duration-200 <?php echo $showUnreadOnly ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'; ?>">
                        غير المقروءة فقط
                    </a>
                </div>
            </div>
            
            <!-- قائمة الإشعارات -->
            <div class="p-6">
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-bell-slash text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إشعارات</h3>
                        <p class="text-gray-500">
                            <?php echo $showUnreadOnly ? 'لا توجد إشعارات غير مقروءة' : 'لم يتم إنشاء أي إشعارات بعد'; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($notifications as $notification): ?>
                            <?php
                            // تحديد نوع الإشعار للتصميم
                            $notificationClass = $notification['is_read'] ? 'notification-read' : 'notification-unread';
                            $priorityClass = 'priority-' . $notification['priority'];
                            $iconClass = 'icon-' . ($notification['notification_type'] ?? 'default');
                            
                            $typeIcons = [
                                'low_stock' => 'fas fa-box',
                                'overdue_invoice' => 'fas fa-file-invoice-dollar',
                                'backup_warning' => 'fas fa-exclamation-triangle',
                                'no_backup' => 'fas fa-database',
                                'system' => 'fas fa-cog'
                            ];
                            
                            $iconName = $typeIcons[$notification['notification_type']] ?? 'fas fa-bell';
                            ?>
                            
                            <div class="notification-item <?php echo $notificationClass; ?> <?php echo $priorityClass; ?> p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-start space-x-4 space-x-reverse">
                                    
                                    <!-- أيقونة الإشعار -->
                                    <div class="notification-icon <?php echo $iconClass; ?>">
                                        <i class="<?php echo $iconName; ?>"></i>
                                    </div>
                                    
                                    <!-- محتوى الإشعار -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between">
                                            <h4 class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($notification['title']); ?>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                                <?php endif; ?>
                                            </h4>
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <span class="text-xs text-gray-500">
                                                    <?php echo formatDate($notification['created_at']); ?>
                                                </span>
                                                <span class="text-xs text-gray-500">
                                                    <?php echo date('H:i', strtotime($notification['created_at'])); ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <p class="mt-1 text-sm text-gray-600">
                                            <?php echo htmlspecialchars($notification['message']); ?>
                                        </p>
                                        
                                        <div class="mt-3 flex items-center justify-between">
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <?php if (!empty($notification['action_url'])): ?>
                                                    <a href="<?php echo htmlspecialchars($notification['action_url']); ?>" 
                                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                        عرض التفاصيل
                                                        <i class="fas fa-arrow-left mr-1"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <?php if (!$notification['is_read']): ?>
                                                    <button onclick="markAsRead(<?php echo $notification['notification_id']; ?>)" 
                                                            class="text-green-600 hover:text-green-800 text-sm" 
                                                            title="تحديد كمقروء">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <a href="notifications.php?action=delete&id=<?php echo $notification['notification_id']; ?>" 
                                                   onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')"
                                                   class="text-red-600 hover:text-red-800 text-sm" 
                                                   title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                            </div>
                            
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
        </div>
        
    </div>

    <!-- JavaScript -->
    <script>
        function markAsRead(notificationId) {
            fetch(`notifications.php?action=mark_read&id=${notificationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('فشل في تحديث الإشعار');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في تحديث الإشعار');
                });
        }
    </script>

</body>
</html>

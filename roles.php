<?php
/**
 * صفحة إدارة الأدوار والصلاحيات
 * Roles and Permissions Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('roles_manage');

// إنشاء مثيل نموذج الأدوار
$roleModel = new Role();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$roleId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $roleId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($roleModel->deleteRole($roleId)) {
                    setAlert('تم حذف الدور بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف الدور', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('roles.php');
    }
}

// معالجة إضافة/تعديل الدور
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'role_name' => sanitizeInput($_POST['role_name'] ?? ''),
            'role_description' => sanitizeInput($_POST['role_description'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        $permissions = $_POST['permissions'] ?? [];
        
        // التحقق من البيانات المطلوبة
        if (empty($data['role_name'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                if ($action === 'add') {
                    $newRoleId = $roleModel->createRole($data);
                    if ($newRoleId) {
                        // تحديث الصلاحيات
                        $roleModel->updateRolePermissions($newRoleId, $permissions);
                        setAlert('تم إضافة الدور بنجاح', 'success');
                        redirect('roles.php');
                    } else {
                        $error = 'فشل في إضافة الدور';
                    }
                } else {
                    if ($roleModel->updateRole($roleId, $data)) {
                        // تحديث الصلاحيات
                        $roleModel->updateRolePermissions($roleId, $permissions);
                        setAlert('تم تحديث الدور بنجاح', 'success');
                        redirect('roles.php');
                    } else {
                        $error = 'فشل في تحديث الدور';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentRole = null;
$currentPermissions = [];
if ($action === 'edit' && $roleId) {
    $currentRole = $roleModel->getRoleById($roleId);
    if (!$currentRole) {
        setAlert('الدور غير موجود', 'error');
        redirect('roles.php');
    }
    $currentPermissions = $roleModel->getRolePermissions($roleId);
}

// الحصول على قائمة الأدوار للعرض
$roles = $roleModel->getAllRoles(false);

// الحصول على الإحصائيات
$rolesStats = $roleModel->getRolesStats();

// الحصول على الصلاحيات المجمعة
$permissionsByCategory = $roleModel->getPermissionsByCategory();

$pageTitle = $action === 'add' ? 'إضافة دور جديد' : 
            ($action === 'edit' ? 'تعديل الدور' : 'إدارة الأدوار والصلاحيات');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض البطاقات */
        .role-card {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .role-active { border-right: 4px solid #10b981; }
        .role-inactive { border-right: 4px solid #ef4444; }

        /* تحسين الجدول */
        .roles-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .roles-table tbody tr:hover {
            background-color: #f8fafc;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* تحسين التمرير */
        .table-container {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* تحسين المساحة والتخطيط */
        .main-container {
            min-height: calc(100vh - 200px);
        }

        /* تحسين البطاقات المتجاوبة */
        @media (max-width: 768px) {
            .role-card {
                margin-bottom: 1rem;
            }
        }
        
        /* تحسين عرض الصلاحيات */
        .permission-category {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .permission-header {
            background-color: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
        }
        
        .permission-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
            padding: 1rem;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-user-shield ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8 main-container">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- بطاقات الإحصائيات -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                
                <!-- إجمالي الأدوار -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-shield text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الأدوار</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($rolesStats['total_roles']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الأدوار النشطة -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">الأدوار النشطة</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($rolesStats['active_roles']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إجمالي المستخدمين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المستخدمين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($rolesStats['total_users']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- أدوار بمستخدمين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-tag text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">أدوار مستخدمة</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($rolesStats['roles_with_users']); ?>
                                    </dd>
                                </dl>
                            </div>

            <!-- قائمة الأدوار -->
            <div class="bg-white shadow-lg rounded-lg mb-8">

                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قائمة الأدوار (<?php echo count($roles); ?> دور)
                    </h3>
                    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse w-full sm:w-auto">
                        <a href="users.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-users ml-2"></i>
                            إدارة المستخدمين
                        </a>
                        <a href="roles.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-center">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة دور جديد
                        </a>
                    </div>
                </div>

                <!-- جدول الأدوار -->
                <div class="overflow-x-auto max-h-96 overflow-y-auto table-container">
                    <?php if (empty($roles)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-user-shield text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أدوار</h3>
                            <p class="text-gray-500 mb-4">لم يتم إنشاء أي أدوار بعد</p>
                            <a href="roles.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول دور
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- عرض الجدول للشاشات الكبيرة -->
                        <div class="hidden md:block">
                            <table class="min-w-full divide-y divide-gray-200 roles-table">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الدور</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">الوصف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدمين</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">تاريخ الإنشاء</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($roles as $role): ?>
                                    <tr class="hover:bg-gray-50 role-card <?php echo $role['is_active'] ? 'role-active' : 'role-inactive'; ?>">
                                        <td class="px-4 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <i class="fas fa-user-shield text-blue-500 ml-2"></i>
                                                <?php echo htmlspecialchars($role['role_name']); ?>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 hidden sm:table-cell">
                                            <div class="text-sm text-gray-900 max-w-xs truncate" title="<?php echo htmlspecialchars($role['role_description'] ?? 'لا يوجد وصف'); ?>">
                                                <?php echo htmlspecialchars($role['role_description'] ?? 'لا يوجد وصف'); ?>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center">
                                                <i class="fas fa-users text-gray-400 ml-2"></i>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <?php echo number_format($role['users_count']); ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 whitespace-nowrap">
                                            <?php if ($role['is_active']): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle ml-1"></i>
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    <i class="fas fa-times-circle ml-1"></i>
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar-alt text-gray-400 ml-2"></i>
                                                <?php echo formatDate($role['created_at']); ?>
                                            </div>
                                        </td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-1 space-x-reverse">
                                                <a href="roles.php?action=permissions&id=<?php echo $role['role_id']; ?>"
                                                   class="bg-purple-100 hover:bg-purple-200 text-purple-600 p-2 rounded-lg transition-colors duration-200"
                                                   title="إدارة الصلاحيات">
                                                    <i class="fas fa-key text-sm"></i>
                                                </a>
                                                <a href="roles.php?action=edit&id=<?php echo $role['role_id']; ?>"
                                                   class="bg-blue-100 hover:bg-blue-200 text-blue-600 p-2 rounded-lg transition-colors duration-200"
                                                   title="تعديل">
                                                    <i class="fas fa-edit text-sm"></i>
                                                </a>
                                                <?php if ($role['users_count'] == 0): ?>
                                                    <a href="roles.php?action=delete&id=<?php echo $role['role_id']; ?>"
                                                       onclick="return confirm('هل أنت متأكد من حذف الدور \'<?php echo htmlspecialchars($role['role_name'], ENT_QUOTES); ?>\'؟')"
                                                       class="bg-red-100 hover:bg-red-200 text-red-600 p-2 rounded-lg transition-colors duration-200"
                                                       title="حذف">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="bg-gray-100 text-gray-400 p-2 rounded-lg" title="لا يمكن حذف دور يحتوي على مستخدمين">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            </table>
                        </div>

                        <!-- عرض البطاقات للشاشات الصغيرة -->
                        <div class="md:hidden space-y-4 p-4">
                            <?php foreach ($roles as $role): ?>
                                <div class="bg-white border border-gray-200 rounded-lg p-4 role-card <?php echo $role['is_active'] ? 'role-active' : 'role-inactive'; ?>">
                                    <div class="flex justify-between items-start mb-3">
                                        <div class="flex items-center">
                                            <i class="fas fa-user-shield text-blue-500 ml-2"></i>
                                            <h4 class="text-lg font-medium text-gray-900">
                                                <?php echo htmlspecialchars($role['role_name']); ?>
                                            </h4>
                                        </div>
                                        <div class="flex space-x-1 space-x-reverse">
                                            <a href="roles.php?action=permissions&id=<?php echo $role['role_id']; ?>"
                                               class="bg-purple-100 hover:bg-purple-200 text-purple-600 p-2 rounded-lg transition-colors duration-200">
                                                <i class="fas fa-key text-sm"></i>
                                            </a>
                                            <a href="roles.php?action=edit&id=<?php echo $role['role_id']; ?>"
                                               class="bg-blue-100 hover:bg-blue-200 text-blue-600 p-2 rounded-lg transition-colors duration-200">
                                                <i class="fas fa-edit text-sm"></i>
                                            </a>
                                            <?php if ($role['users_count'] == 0): ?>
                                                <a href="roles.php?action=delete&id=<?php echo $role['role_id']; ?>"
                                                   onclick="return confirm('هل أنت متأكد من حذف الدور \'<?php echo htmlspecialchars($role['role_name'], ENT_QUOTES); ?>\'؟')"
                                                   class="bg-red-100 hover:bg-red-200 text-red-600 p-2 rounded-lg transition-colors duration-200">
                                                    <i class="fas fa-trash text-sm"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <p class="text-sm text-gray-600 mb-3">
                                        <?php echo htmlspecialchars($role['role_description'] ?? 'لا يوجد وصف'); ?>
                                    </p>

                                    <div class="flex justify-between items-center text-sm">
                                        <div class="flex items-center space-x-4 space-x-reverse">
                                            <div class="flex items-center">
                                                <i class="fas fa-users text-gray-400 ml-1"></i>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <?php echo number_format($role['users_count']); ?> مستخدم
                                                </span>
                                            </div>
                                            <div>
                                                <?php if ($role['is_active']): ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                        <i class="fas fa-check-circle ml-1"></i>
                                                        نشط
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                        <i class="fas fa-times-circle ml-1"></i>
                                                        غير نشط
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="flex items-center text-gray-500">
                                            <i class="fas fa-calendar-alt ml-1"></i>
                                            <?php echo formatDate($role['created_at']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- نموذج إضافة/تعديل الدور -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> ml-2"></i>
                        <?php echo $action === 'add' ? 'إضافة دور جديد' : 'تعديل الدور'; ?>
                    </h3>
                </div>

                <form method="POST" action="" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">

                        <!-- اسم الدور -->
                        <div>
                            <label for="role_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الدور <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="role_name"
                                name="role_name"
                                value="<?php echo htmlspecialchars($currentRole['role_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                placeholder="مثال: مدير المبيعات"
                            >
                        </div>

                        <!-- حالة الدور -->
                        <div class="flex items-center pt-8">
                            <input
                                type="checkbox"
                                id="is_active"
                                name="is_active"
                                value="1"
                                <?php echo ($currentRole['is_active'] ?? 1) ? 'checked' : ''; ?>
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            >
                            <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                الدور نشط
                            </label>
                        </div>

                    </div>

                    <!-- وصف الدور -->
                    <div class="mb-6">
                        <label for="role_description" class="block text-sm font-medium text-gray-700 mb-2">
                            وصف الدور
                        </label>
                        <textarea
                            id="role_description"
                            name="role_description"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="وصف مختصر لمهام ومسؤوليات هذا الدور"
                        ><?php echo htmlspecialchars($currentRole['role_description'] ?? ''); ?></textarea>
                    </div>

                    <!-- الصلاحيات -->
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">صلاحيات الدور</h4>

                        <?php foreach ($permissionsByCategory as $categoryName => $permissions): ?>
                            <div class="permission-category">
                                <div class="permission-header">
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            class="category-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            data-category="<?php echo htmlspecialchars($categoryName); ?>"
                                        >
                                        <span class="mr-2"><?php echo htmlspecialchars($categoryName); ?></span>
                                    </label>
                                </div>
                                <div class="permission-grid">
                                    <?php foreach ($permissions as $permissionKey => $permissionName): ?>
                                        <label class="flex items-center">
                                            <input
                                                type="checkbox"
                                                name="permissions[]"
                                                value="<?php echo htmlspecialchars($permissionKey); ?>"
                                                <?php echo in_array($permissionKey, $currentPermissions) ? 'checked' : ''; ?>
                                                class="permission-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                data-category="<?php echo htmlspecialchars($categoryName); ?>"
                                            >
                                            <span class="mr-2 text-sm"><?php echo htmlspecialchars($permissionName); ?></span>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="flex justify-end space-x-4 space-x-reverse">
                        <a href="roles.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            <?php echo $action === 'add' ? 'إضافة الدور' : 'حفظ التغييرات'; ?>
                        </button>
                    </div>

                </form>
            </div>

        <?php endif; ?>

    </div>

    <!-- JavaScript للتحكم في الصلاحيات -->
    <script>
        // تحديد/إلغاء تحديد جميع صلاحيات الفئة
        document.querySelectorAll('.category-checkbox').forEach(function(categoryCheckbox) {
            categoryCheckbox.addEventListener('change', function() {
                const category = this.dataset.category;
                const permissionCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);

                permissionCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = categoryCheckbox.checked;
                });
            });
        });

        // تحديث حالة checkbox الفئة عند تغيير الصلاحيات الفردية
        document.querySelectorAll('.permission-checkbox').forEach(function(permissionCheckbox) {
            permissionCheckbox.addEventListener('change', function() {
                const category = this.dataset.category;
                const categoryCheckbox = document.querySelector(`.category-checkbox[data-category="${category}"]`);
                const permissionCheckboxes = document.querySelectorAll(`.permission-checkbox[data-category="${category}"]`);

                const checkedCount = Array.from(permissionCheckboxes).filter(cb => cb.checked).length;
                const totalCount = permissionCheckboxes.length;

                if (checkedCount === 0) {
                    categoryCheckbox.checked = false;
                    categoryCheckbox.indeterminate = false;
                } else if (checkedCount === totalCount) {
                    categoryCheckbox.checked = true;
                    categoryCheckbox.indeterminate = false;
                } else {
                    categoryCheckbox.checked = false;
                    categoryCheckbox.indeterminate = true;
                }
            });
        });

        // تحديث حالة checkboxes الفئات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.category-checkbox').forEach(function(categoryCheckbox) {
                categoryCheckbox.dispatchEvent(new Event('change'));
            });
        });
    </script>

</body>
</html>
                        </div>
                    </div>
                </div>
                
            </div>

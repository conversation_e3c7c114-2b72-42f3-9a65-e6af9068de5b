<?php
/**
 * صفحة إدارة المستخدمين
 * Users Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('users_manage');

// إنشاء مثيلات النماذج
$userModel = new User();
$roleModel = new Role();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$userId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $userId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($userModel->deleteUser($userId)) {
                    setAlert('تم حذف المستخدم بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف المستخدم', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('users.php');
    }
}

// معالجة إضافة/تعديل المستخدم
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'username' => sanitizeInput($_POST['username'] ?? ''),
            'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'role_id' => !empty($_POST['role_id']) ? (int)$_POST['role_id'] : null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // كلمة المرور (مطلوبة للإضافة فقط)
        if ($action === 'add') {
            $data['password'] = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
        } else {
            // تحديث كلمة المرور فقط إذا تم إدخالها
            if (!empty($_POST['password'])) {
                $data['password'] = $_POST['password'];
                $confirmPassword = $_POST['confirm_password'] ?? '';
            }
        }
        
        // التحقق من البيانات المطلوبة
        if (empty($data['username']) || empty($data['full_name']) || empty($data['email'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } elseif ($action === 'add' && empty($data['password'])) {
            $error = 'كلمة المرور مطلوبة';
        } elseif (isset($data['password']) && $data['password'] !== $confirmPassword) {
            $error = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
        } elseif (isset($data['password']) && strlen($data['password']) < 6) {
            $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else {
            try {
                if ($action === 'add') {
                    $newUserId = $userModel->createUser($data);
                    if ($newUserId) {
                        setAlert('تم إضافة المستخدم بنجاح', 'success');
                        redirect('users.php');
                    } else {
                        $error = 'فشل في إضافة المستخدم';
                    }
                } else {
                    if ($userModel->updateUser($userId, $data)) {
                        setAlert('تم تحديث المستخدم بنجاح', 'success');
                        redirect('users.php');
                    } else {
                        $error = 'فشل في تحديث المستخدم';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentUser = null;
if ($action === 'edit' && $userId) {
    $currentUser = $userModel->getUserById($userId);
    if (!$currentUser) {
        setAlert('المستخدم غير موجود', 'error');
        redirect('users.php');
    }
}

// الحصول على قائمة المستخدمين للعرض
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$roleFilter = !empty($_GET['role']) ? (int)$_GET['role'] : null;

if (!empty($searchTerm)) {
    $users = $userModel->searchUsers($searchTerm);
} else {
    $users = $userModel->getAllUsers();
    
    // فلترة حسب الدور إذا تم تحديده
    if ($roleFilter) {
        $users = array_filter($users, function($user) use ($roleFilter) {
            return $user['role_id'] == $roleFilter;
        });
    }
}

// الحصول على الأدوار للقوائم المنسدلة
$roles = $roleModel->getAllRoles();

// الحصول على الإحصائيات
$usersStats = $userModel->getUsersStats();

$pageTitle = $action === 'add' ? 'إضافة مستخدم جديد' : 
            ($action === 'edit' ? 'تعديل المستخدم' : 'إدارة المستخدمين');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض البطاقات */
        .user-card {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .user-active { border-right: 4px solid #10b981; }
        .user-inactive { border-right: 4px solid #ef4444; }
        .user-online { border-right: 4px solid #3b82f6; }
        
        .status-online { background-color: #d1fae5; color: #065f46; }
        .status-offline { background-color: #fee2e2; color: #991b1b; }
        .status-inactive { background-color: #f3f4f6; color: #6b7280; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-users ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- بطاقات الإحصائيات -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                
                <!-- إجمالي المستخدمين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المستخدمين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($usersStats['total_users'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- المستخدمين النشطين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">المستخدمين النشطين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($usersStats['active_users'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- المتصلين اليوم -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-sign-in-alt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">دخول اليوم</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($usersStats['logged_in_today'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- المديرين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-shield text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">المديرين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($usersStats['admin_users'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- قائمة المستخدمين -->
            <div class="bg-white shadow-lg rounded-lg">

                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قائمة المستخدمين (<?php echo count($users); ?> مستخدم)
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="roles.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-user-shield ml-2"></i>
                            إدارة الأدوار
                        </a>
                        <a href="users.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة مستخدم جديد
                        </a>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <form method="GET" action="users.php" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input
                                type="text"
                                name="search"
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="البحث في المستخدمين (الاسم، اسم المستخدم، البريد الإلكتروني)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <div class="min-w-48">
                            <select name="role" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الأدوار</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?php echo $role['role_id']; ?>"
                                            <?php echo $roleFilter == $role['role_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($role['role_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm) || !empty($roleFilter)): ?>
                            <a href="users.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- جدول المستخدمين -->
                <div class="overflow-x-auto">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد مستخدمين</h3>
                            <p class="text-gray-500 mb-4">لم يتم العثور على أي مستخدمين مطابقين للبحث</p>
                            <a href="users.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول مستخدم
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معلومات الاتصال</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر دخول</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($users as $user): ?>
                                    <tr class="hover:bg-gray-50 user-card <?php echo $user['is_active'] ? 'user-active' : 'user-inactive'; ?>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                        <i class="fas fa-user text-gray-600"></i>
                                                    </div>
                                                </div>
                                                <div class="mr-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        @<?php echo htmlspecialchars($user['username']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if (!empty($user['role_name'])): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                    <?php echo htmlspecialchars($user['role_name']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    غير محدد
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-envelope text-gray-400 ml-1"></i>
                                                    <?php echo htmlspecialchars($user['email']); ?>
                                                </div>
                                                <?php if (!empty($user['phone'])): ?>
                                                    <div class="flex items-center mt-1">
                                                        <i class="fas fa-phone text-gray-400 ml-1"></i>
                                                        <?php echo htmlspecialchars($user['phone']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php if (!empty($user['last_login'])): ?>
                                                <div>
                                                    <?php echo formatDate($user['last_login']); ?>
                                                </div>
                                                <div class="text-xs">
                                                    <?php echo date('H:i', strtotime($user['last_login'])); ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400">لم يدخل بعد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($user['is_active']): ?>
                                                <?php
                                                $isOnlineToday = !empty($user['last_login']) &&
                                                               date('Y-m-d', strtotime($user['last_login'])) === date('Y-m-d');
                                                ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $isOnlineToday ? 'status-online' : 'bg-green-100 text-green-800'; ?>">
                                                    <?php echo $isOnlineToday ? 'متصل اليوم' : 'نشط'; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full status-inactive">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="users.php?action=edit&id=<?php echo $user['user_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($user['user_id'] != $_SESSION['user_id']): ?>
                                                    <a href="users.php?action=delete&id=<?php echo $user['user_id']; ?>"
                                                       onclick="return confirm('هل أنت متأكد من حذف المستخدم \'<?php echo htmlspecialchars($user['full_name'], ENT_QUOTES); ?>\'؟')"
                                                       class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                       title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-gray-400" title="لا يمكن حذف حسابك الشخصي">
                                                        <i class="fas fa-trash"></i>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>

            </div>

        <?php endif; ?>

    </div>

</body>
</html>

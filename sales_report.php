<?php
/**
 * صفحة تقرير المبيعات
 * Sales Report Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيل نموذج التقارير
$reportModel = new Report();

// معالجة المرشحات
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');
$groupBy = sanitizeInput($_GET['group_by'] ?? 'daily');

// تعيين تواريخ افتراضية إذا لم تكن محددة
if (empty($dateFrom)) {
    $dateFrom = date('Y-m-01'); // بداية الشهر الحالي
}
if (empty($dateTo)) {
    $dateTo = date('Y-m-d'); // اليوم الحالي
}

// الحصول على تقرير المبيعات
$salesReport = $reportModel->getSalesReport($dateFrom, $dateTo, $groupBy);

$pageTitle = 'تقرير المبيعات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض التقرير */
        .report-table {
            font-size: 0.875rem;
        }
        
        .section-header {
            background-color: #f3f4f6;
            font-weight: 600;
        }
        
        .total-row {
            background-color: #1f2937;
            color: white;
            font-weight: 700;
        }
        
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
            .report-table { font-size: 11px; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-chart-bar ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- تقرير المبيعات -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس التقرير -->
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-chart-bar ml-2"></i>
                        تقرير المبيعات
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                        <a href="inventory_report.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-boxes ml-2"></i>
                            تقرير المخزون
                        </a>
                        <a href="reports.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-line ml-2"></i>
                            جميع التقارير
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلترة التواريخ -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 no-print">
                <form method="GET" action="sales_report.php" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                        <input 
                            type="date" 
                            name="date_from" 
                            value="<?php echo htmlspecialchars($dateFrom); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                        <input 
                            type="date" 
                            name="date_to" 
                            value="<?php echo htmlspecialchars($dateTo); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تجميع حسب</label>
                        <select name="group_by" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="daily" <?php echo $groupBy === 'daily' ? 'selected' : ''; ?>>يومي</option>
                            <option value="weekly" <?php echo $groupBy === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                            <option value="monthly" <?php echo $groupBy === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                            <option value="yearly" <?php echo $groupBy === 'yearly' ? 'selected' : ''; ?>>سنوي</option>
                        </select>
                    </div>
                    <div class="pt-6">
                        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- معلومات التقرير -->
            <div class="px-6 py-4 bg-blue-50 border-b border-gray-200">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-900 mb-2"><?php echo APP_NAME; ?></h2>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">تقرير المبيعات</h3>
                    <p class="text-sm text-gray-600">
                        من <?php echo formatDate($dateFrom); ?> إلى <?php echo formatDate($dateTo); ?>
                        (تجميع <?php 
                        $groupLabels = [
                            'daily' => 'يومي',
                            'weekly' => 'أسبوعي', 
                            'monthly' => 'شهري',
                            'yearly' => 'سنوي'
                        ];
                        echo $groupLabels[$groupBy] ?? $groupBy;
                        ?>)
                    </p>
                    <p class="text-xs text-gray-500 mt-2">
                        تاريخ الطباعة: <?php echo formatDate(date('Y-m-d')); ?> - الوقت: <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>
            
            <!-- المبيعات حسب الفترة -->
            <div class="p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-calendar-alt ml-2"></i>
                    المبيعات حسب الفترة
                </h4>
                
                <div class="overflow-x-auto">
                    <?php if (empty($salesReport['sales_data'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-chart-bar text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد مبيعات في الفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200 report-table">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفترة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الفواتير</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المبيعات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الضرائب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصومات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط الفاتورة</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php 
                                $totalInvoices = 0;
                                $totalSales = 0;
                                $totalTax = 0;
                                $totalDiscount = 0;
                                ?>
                                <?php foreach ($salesReport['sales_data'] as $data): ?>
                                    <?php
                                    $totalInvoices += $data['invoice_count'];
                                    $totalSales += $data['total_sales'];
                                    $totalTax += $data['total_tax'];
                                    $totalDiscount += $data['total_discount'];
                                    ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($data['period']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo number_format($data['invoice_count']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                            <?php echo formatMoney($data['total_sales']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatMoney($data['total_tax']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatMoney($data['total_discount']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatMoney($data['avg_invoice_value']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="total-row">
                                    <td class="px-6 py-3 text-sm font-bold">الإجمالي</td>
                                    <td class="px-6 py-3 text-sm font-bold"><?php echo number_format($totalInvoices); ?></td>
                                    <td class="px-6 py-3 text-sm font-bold"><?php echo formatMoney($totalSales); ?></td>
                                    <td class="px-6 py-3 text-sm font-bold"><?php echo formatMoney($totalTax); ?></td>
                                    <td class="px-6 py-3 text-sm font-bold"><?php echo formatMoney($totalDiscount); ?></td>
                                    <td class="px-6 py-3 text-sm font-bold">
                                        <?php echo $totalInvoices > 0 ? formatMoney($totalSales / $totalInvoices) : formatMoney(0); ?>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- المبيعات حسب العميل -->
            <div class="p-6 border-t border-gray-200">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-users ml-2"></i>
                    أفضل 10 عملاء
                </h4>
                
                <div class="overflow-x-auto">
                    <?php if (empty($salesReport['sales_by_customer'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد مبيعات للعملاء في الفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200 report-table">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الفواتير</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المبيعات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النسبة %</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($salesReport['sales_by_customer'] as $customer): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($customer['customer_name']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php echo htmlspecialchars($customer['customer_code']); ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo number_format($customer['invoice_count']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                            <?php echo formatMoney($customer['total_sales']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php 
                                            $percentage = $totalSales > 0 ? ($customer['total_sales'] / $totalSales) * 100 : 0;
                                            echo number_format($percentage, 1) . '%';
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- المبيعات حسب الصنف -->
            <div class="p-6 border-t border-gray-200">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-box ml-2"></i>
                    أفضل 10 أصناف
                </h4>
                
                <div class="overflow-x-auto">
                    <?php if (empty($salesReport['sales_by_item'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-box text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد مبيعات للأصناف في الفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200 report-table">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصنف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المباعة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المبيعات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط السعر</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($salesReport['sales_by_item'] as $item): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($item['item_name']); ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <?php echo htmlspecialchars($item['item_code']); ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatNumber($item['total_quantity'], 3); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                            <?php echo formatMoney($item['total_sales']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatMoney($item['avg_price']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
            
        </div>
        
    </div>

</body>
</html>

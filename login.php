<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// إعادة توجيه المستخدم المسجل دخوله إلى لوحة التحكم
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$error = '';
$username = '';

// معالجة طلب تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrfToken)) {
        $error = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
    }
    // التحقق من البيانات المطلوبة
    elseif (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور.';
    }
    else {
        $userModel = new User();
        
        if ($userModel->login($username, $password)) {
            // تسجيل دخول ناجح
            setAlert('مرحباً بك، تم تسجيل الدخول بنجاح!', 'success');
            redirect('dashboard.php');
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
            
            // تسجيل محاولة دخول فاشلة
            error_log("Failed login attempt for username: {$username} from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        }
    }
}

// إنشاء رمز CSRF
$csrfToken = generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- تخصيص Tailwind للغة العربية -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'Arial', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .input-focus:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center p-4">
    
    <!-- خلفية متحركة -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: 2s;"></div>
    </div>
    
    <!-- نموذج تسجيل الدخول -->
    <div class="relative z-10 w-full max-w-md">
        
        <!-- بطاقة تسجيل الدخول -->
        <div class="glass-effect rounded-2xl shadow-2xl p-8">
            
            <!-- شعار النظام -->
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mb-4">
                    <i class="fas fa-calculator text-2xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2"><?php echo APP_NAME; ?></h1>
                <p class="text-white text-opacity-80">نظام إدارة محترف ومتكامل</p>
            </div>
            
            <!-- رسائل الخطأ -->
            <?php if (!empty($error)): ?>
                <div class="bg-red-500 bg-opacity-20 border border-red-400 text-white px-4 py-3 rounded-lg mb-6 text-center">
                    <i class="fas fa-exclamation-triangle ml-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- نموذج تسجيل الدخول -->
            <form method="POST" action="" class="space-y-6">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <!-- اسم المستخدم -->
                <div>
                    <label for="username" class="block text-white text-sm font-medium mb-2">
                        <i class="fas fa-user ml-2"></i>
                        اسم المستخدم
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        value="<?php echo htmlspecialchars($username); ?>"
                        class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 input-focus focus:outline-none transition-all duration-300"
                        placeholder="أدخل اسم المستخدم"
                        required
                        autocomplete="username"
                        dir="ltr"
                    >
                </div>
                
                <!-- كلمة المرور -->
                <div>
                    <label for="password" class="block text-white text-sm font-medium mb-2">
                        <i class="fas fa-lock ml-2"></i>
                        كلمة المرور
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-70 input-focus focus:outline-none transition-all duration-300"
                        placeholder="أدخل كلمة المرور"
                        required
                        autocomplete="current-password"
                        dir="ltr"
                    >
                </div>
                
                <!-- زر تسجيل الدخول -->
                <button 
                    type="submit" 
                    class="w-full btn-primary text-white font-medium py-3 px-6 rounded-lg focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50"
                >
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
                
            </form>
            
            <!-- معلومات إضافية -->
            <div class="mt-8 text-center">
                <p class="text-white text-opacity-60 text-sm">
                    <i class="fas fa-shield-alt ml-1"></i>
                    جلسة آمنة ومحمية
                </p>
            </div>
            
        </div>
        
        <!-- معلومات النظام -->
        <div class="text-center mt-6">
            <p class="text-white text-opacity-60 text-sm">
                الإصدار <?php echo APP_VERSION; ?> | 
                <i class="fas fa-code ml-1"></i>
                تطوير فريق فيزي سوفت
            </p>
        </div>
        
    </div>
    
    <!-- تحسينات CSS إضافية -->
    <style>
        /* تأثير التركيز التلقائي على حقل اسم المستخدم */
        #username:focus {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }
            50% { box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.2); }
        }

        /* تأثير الإرسال */
        .form-submitting {
            opacity: 0.7;
            pointer-events: none;
        }

        .form-submitting button {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
    
</body>
</html>

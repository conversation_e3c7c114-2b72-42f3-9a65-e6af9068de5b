<?php
/**
 * نموذج المستخدم
 * User Model
 */

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * تسجيل دخول المستخدم
     * ⚠️ تحذير: يستخدم مقارنة كلمة مرور غير مشفرة (غير آمن)
     */
    public function login($username, $password) {
        try {
            $query = "SELECT user_id, username, password, full_name, email, role,
                            permissions, is_active, last_login
                     FROM users
                     WHERE username = ? AND is_active = 1";

            $user = $this->db->selectOne($query, [$username]);

            if ($user && $password === $user['password']) {
                // تحديث آخر تسجيل دخول
                $this->updateLastLogin($user['user_id']);

                // حفظ بيانات المستخدم في الجلسة
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['permissions'] = json_decode($user['permissions'], true) ?? [];
                $_SESSION['last_login'] = $user['last_login'];

                // تسجيل النشاط
                logActivity('تسجيل دخول', 'تم تسجيل الدخول بنجاح');

                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isLoggedIn()) {
            logActivity('تسجيل خروج', 'تم تسجيل الخروج');
            
            // مسح بيانات الجلسة
            session_unset();
            session_destroy();
            
            return true;
        }
        return false;
    }
    
    /**
     * الحصول على بيانات المستخدم
     */
    public function getUserById($userId) {
        try {
            $query = "SELECT u.user_id, u.username, u.full_name, u.email, u.phone,
                            u.role_id, u.permissions, u.is_active, u.last_login, u.created_at,
                            r.role_name
                     FROM users u
                     LEFT JOIN roles r ON u.role_id = r.role_id
                     WHERE u.user_id = ?";

            return $this->db->selectOne($query, [$userId]);

        } catch (Exception $e) {
            error_log("Get user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على جميع المستخدمين
     */
    public function getAllUsers($activeOnly = false) {
        try {
            $query = "SELECT
                        u.user_id, u.username, u.full_name, u.email, u.phone,
                        u.is_active, u.last_login, u.created_at, u.updated_at,
                        r.role_name, r.role_id
                      FROM users u
                      LEFT JOIN roles r ON u.role_id = r.role_id";

            $params = [];

            if ($activeOnly) {
                $query .= " WHERE u.is_active = 1";
            }

            $query .= " ORDER BY u.full_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all users error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        try {
            // التحقق من عدم وجود اسم المستخدم
            if ($this->usernameExists($data['username'])) {
                throw new Exception('اسم المستخدم موجود بالفعل');
            }
            
            // التحقق من عدم وجود البريد الإلكتروني
            if (!empty($data['email']) && $this->emailExists($data['email'])) {
                throw new Exception('البريد الإلكتروني موجود بالفعل');
            }
            
            $query = "INSERT INTO users (username, password, full_name, email, phone,
                                       role, permissions, is_active, created_by, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $data['username'],
                $data['password'], // تخزين كنص عادي (غير آمن)
                $data['full_name'],
                $data['email'] ?? null,
                $data['phone'] ?? null,
                $data['role'] ?? 'user',
                json_encode($data['permissions'] ?? []),
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $userId = $this->db->insert($query, $params);
            
            if ($userId) {
                logActivity('إنشاء مستخدم', "تم إنشاء المستخدم: {$data['username']}");
                return $userId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Create user error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    public function updateUser($userId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['full_name'])) {
                $setParts[] = "full_name = ?";
                $params[] = $data['full_name'];
            }
            
            if (isset($data['email'])) {
                // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
                if (!empty($data['email']) && $this->emailExists($data['email'], $userId)) {
                    throw new Exception('البريد الإلكتروني موجود بالفعل');
                }
                $setParts[] = "email = ?";
                $params[] = $data['email'];
            }
            
            if (isset($data['phone'])) {
                $setParts[] = "phone = ?";
                $params[] = $data['phone'];
            }
            
            if (isset($data['role'])) {
                $setParts[] = "role = ?";
                $params[] = $data['role'];
            }
            
            if (isset($data['permissions'])) {
                $setParts[] = "permissions = ?";
                $params[] = json_encode($data['permissions']);
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (isset($data['password'])) {
                $setParts[] = "password_hash = ?";
                $params[] = hashPassword($data['password']);
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $userId;
            
            $query = "UPDATE users SET " . implode(', ', $setParts) . " WHERE user_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث مستخدم', "تم تحديث المستخدم ID: {$userId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update user error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف المستخدم (إلغاء تفعيل)
     */
    public function deleteUser($userId) {
        try {
            $query = "UPDATE users SET is_active = 0, updated_at = NOW() WHERE user_id = ?";
            $result = $this->db->update($query, [$userId]);
            
            if ($result) {
                logActivity('حذف مستخدم', "تم حذف المستخدم ID: {$userId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete user error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * فحص وجود اسم المستخدم
     */
    private function usernameExists($username, $excludeUserId = null) {
        $query = "SELECT COUNT(*) as count FROM users WHERE username = ?";
        $params = [$username];
        
        if ($excludeUserId) {
            $query .= " AND user_id != ?";
            $params[] = $excludeUserId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * فحص وجود البريد الإلكتروني
     */
    private function emailExists($email, $excludeUserId = null) {
        $query = "SELECT COUNT(*) as count FROM users WHERE email = ?";
        $params = [$email];
        
        if ($excludeUserId) {
            $query .= " AND user_id != ?";
            $params[] = $excludeUserId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        try {
            $query = "UPDATE users SET last_login = NOW() WHERE user_id = ?";
            $this->db->update($query, [$userId]);
        } catch (Exception $e) {
            error_log("Update last login error: " . $e->getMessage());
        }
    }
    
    /**
     * تغيير كلمة المرور
     * ⚠️ تحذير: يستخدم تخزين كلمة مرور غير مشفرة (غير آمن)
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // التحقق من كلمة المرور الحالية
            $user = $this->getUserById($userId);
            if (!$user || $currentPassword !== $user['password']) {
                throw new Exception('كلمة المرور الحالية غير صحيحة');
            }

            // تحديث كلمة المرور
            $query = "UPDATE users SET password = ?, updated_at = NOW() WHERE user_id = ?";
            $result = $this->db->update($query, [$newPassword, $userId]);

            if ($result) {
                logActivity('تغيير كلمة المرور', 'تم تغيير كلمة المرور بنجاح');
            }

            return $result > 0;

        } catch (Exception $e) {
            error_log("Change password error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * البحث في المستخدمين
     */
    public function searchUsers($searchTerm) {
        try {
            $searchTerm = '%' . $searchTerm . '%';

            $query = "SELECT
                        u.user_id, u.username, u.full_name, u.email, u.phone,
                        u.is_active, u.last_login, u.created_at, u.updated_at,
                        r.role_name, r.role_id
                      FROM users u
                      LEFT JOIN roles r ON u.role_id = r.role_id
                      WHERE u.username LIKE ?
                         OR u.full_name LIKE ?
                         OR u.email LIKE ?
                      ORDER BY u.full_name";

            return $this->db->select($query, [$searchTerm, $searchTerm, $searchTerm]);

        } catch (Exception $e) {
            error_log("Search users error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على إحصائيات المستخدمين
     */
    public function getUsersStats() {
        try {
            $stats = [];

            // إجمالي المستخدمين
            $result = $this->db->selectOne("SELECT COUNT(*) as total FROM users");
            $stats['total_users'] = $result['total'] ?? 0;

            // المستخدمين النشطين
            $result = $this->db->selectOne("SELECT COUNT(*) as active FROM users WHERE is_active = 1");
            $stats['active_users'] = $result['active'] ?? 0;

            // المستخدمين غير النشطين
            $result = $this->db->selectOne("SELECT COUNT(*) as inactive FROM users WHERE is_active = 0");
            $stats['inactive_users'] = $result['inactive'] ?? 0;

            // المستخدمين الذين سجلوا دخول اليوم
            $result = $this->db->selectOne("SELECT COUNT(*) as today FROM users WHERE DATE(last_login) = CURDATE()");
            $stats['logged_in_today'] = $result['today'] ?? 0;

            // المستخدمين الذين سجلوا دخول هذا الأسبوع
            $result = $this->db->selectOne("SELECT COUNT(*) as week FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
            $stats['logged_in_week'] = $result['week'] ?? 0;

            // المستخدمين الجدد هذا الشهر
            $result = $this->db->selectOne("SELECT COUNT(*) as month FROM users WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
            $stats['new_this_month'] = $result['month'] ?? 0;

            // عدد المديرين (المستخدمين بدور admin أو مدير)
            $result = $this->db->selectOne("
                SELECT COUNT(*) as admin_count
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE r.role_name IN ('admin', 'مدير', 'Administrator', 'مدير النظام')
                   OR u.role_id = 1
            ");
            $stats['admin_users'] = $result['admin_count'] ?? 0;

            // إحصائيات الأدوار
            $roleStats = $this->db->select("
                SELECT
                    COALESCE(r.role_name, 'غير محدد') as role_name,
                    COUNT(u.user_id) as count
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                GROUP BY u.role_id, r.role_name
                ORDER BY count DESC
            ");
            $stats['role_stats'] = $roleStats ?? [];

            return $stats;

        } catch (Exception $e) {
            error_log("Get users stats error: " . $e->getMessage());
            return [
                'total_users' => 0,
                'active_users' => 0,
                'inactive_users' => 0,
                'logged_in_today' => 0,
                'logged_in_week' => 0,
                'new_this_month' => 0,
                'admin_users' => 0,
                'role_stats' => []
            ];
        }
    }
}

?>

<?php
/**
 * اختبار إنشاء الفاتورة بعد الإصلاح
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار إنشاء الفاتورة</h1>";
echo "<style>
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
</style>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. فحص الجداول:</h2>";
    
    // فحص جدول الفواتير
    $invoicesTable = $db->select("SHOW TABLES LIKE 'invoices'");
    echo "<p class='" . (!empty($invoicesTable) ? 'success' : 'error') . "'>";
    echo (!empty($invoicesTable) ? '✅' : '❌') . " جدول الفواتير</p>";
    
    // فحص جدول عناصر الفواتير
    $itemsTable = $db->select("SHOW TABLES LIKE 'invoice_items'");
    echo "<p class='" . (!empty($itemsTable) ? 'success' : 'error') . "'>";
    echo (!empty($itemsTable) ? '✅' : '❌') . " جدول عناصر الفواتير</p>";
    
    // فحص جدول العملاء
    $customersTable = $db->select("SHOW TABLES LIKE 'customers'");
    echo "<p class='" . (!empty($customersTable) ? 'success' : 'error') . "'>";
    echo (!empty($customersTable) ? '✅' : '❌') . " جدول العملاء</p>";
    
    if (empty($invoicesTable) || empty($itemsTable) || empty($customersTable)) {
        echo "<p class='error'>❌ بعض الجداول مفقودة. يرجى إنشاؤها أولاً.</p>";
        echo "<p><a href='setup_invoices.php'>إنشاء جداول الفواتير</a> | ";
        echo "<a href='setup_customers.php'>إنشاء جدول العملاء</a></p>";
        exit;
    }
    
    echo "<h2>2. فحص هيكل الجداول:</h2>";
    
    // هيكل جدول الفواتير
    echo "<h3>جدول الفواتير:</h3>";
    $invoiceColumns = $db->select("DESCRIBE invoices");
    echo "<pre>";
    foreach ($invoiceColumns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . "\n";
    }
    echo "</pre>";
    
    // هيكل جدول عناصر الفواتير
    echo "<h3>جدول عناصر الفواتير:</h3>";
    $itemColumns = $db->select("DESCRIBE invoice_items");
    echo "<pre>";
    foreach ($itemColumns as $col) {
        echo $col['Field'] . " - " . $col['Type'] . "\n";
    }
    echo "</pre>";
    
    echo "<h2>3. فحص العملاء:</h2>";
    
    $customers = $db->select("SELECT customer_id, customer_name, customer_type FROM customers LIMIT 5");
    if (!empty($customers)) {
        echo "<p class='success'>✅ العملاء متوفرون (" . count($customers) . ")</p>";
        echo "<pre>";
        foreach ($customers as $customer) {
            echo "ID: " . $customer['customer_id'] . " - " . $customer['customer_name'] . " (" . $customer['customer_type'] . ")\n";
        }
        echo "</pre>";
    } else {
        echo "<p class='error'>❌ لا توجد عملاء</p>";
        echo "<p><a href='add_sample_customers.php'>إضافة عملاء تجريبيين</a></p>";
        exit;
    }
    
    echo "<h2>4. اختبار إنشاء فاتورة:</h2>";
    
    if (isset($_POST['create_test_invoice'])) {
        try {
            $invoiceModel = new Invoice();
            
            // بيانات الفاتورة
            $invoiceData = [
                'invoice_number' => 'TEST-' . time(),
                'invoice_type' => 'sales',
                'customer_id' => $customers[0]['customer_id'], // أول عميل
                'invoice_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'total_amount' => 115.00,
                'tax_amount' => 15.00,
                'discount_amount' => 0.00,
                'notes' => 'فاتورة اختبار تم إنشاؤها في ' . date('Y-m-d H:i:s'),
                'status' => 'draft'
            ];
            
            // عناصر الفاتورة
            $items = [
                [
                    'item_name' => 'منتج اختبار 1',
                    'quantity' => 2,
                    'unit_price' => 50.00,
                    'total_price' => 100.00,
                    'discount_amount' => 0.00
                ]
            ];
            
            echo "<p class='info'>بيانات الفاتورة:</p>";
            echo "<pre>" . print_r($invoiceData, true) . "</pre>";
            
            echo "<p class='info'>عناصر الفاتورة:</p>";
            echo "<pre>" . print_r($items, true) . "</pre>";
            
            // إنشاء الفاتورة
            $newInvoiceId = $invoiceModel->createInvoice($invoiceData, $items);
            
            if ($newInvoiceId) {
                echo "<p class='success'>✅ تم إنشاء الفاتورة بنجاح! معرف الفاتورة: $newInvoiceId</p>";
                
                // عرض الفاتورة المنشأة
                $createdInvoice = $db->selectOne("
                    SELECT i.*, c.customer_name 
                    FROM invoices i 
                    LEFT JOIN customers c ON i.customer_id = c.customer_id 
                    WHERE i.invoice_id = ?
                ", [$newInvoiceId]);
                
                echo "<p class='info'>تفاصيل الفاتورة المنشأة:</p>";
                echo "<pre>" . print_r($createdInvoice, true) . "</pre>";
                
                // عرض عناصر الفاتورة
                $createdItems = $db->select("SELECT * FROM invoice_items WHERE invoice_id = ?", [$newInvoiceId]);
                echo "<p class='info'>عناصر الفاتورة المنشأة:</p>";
                echo "<pre>" . print_r($createdItems, true) . "</pre>";
                
                echo "<p class='success'>🎉 النظام يعمل بشكل صحيح!</p>";
                echo "<p><a href='invoices.php?action=view&id=$newInvoiceId'>عرض الفاتورة</a> | ";
                echo "<a href='invoices.php?action=add&type=sales'>إنشاء فاتورة جديدة</a></p>";
                
            } else {
                echo "<p class='error'>❌ فشل في إنشاء الفاتورة</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ خطأ في إنشاء الفاتورة: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='create_test_invoice' style='background: green; color: white; padding: 10px 20px; border: none; cursor: pointer; font-size: 16px;'>";
        echo "إنشاء فاتورة اختبار";
        echo "</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p>";
echo "<a href='invoices.php'>إدارة الفواتير</a> | ";
echo "<a href='invoices.php?action=add&type=sales'>إنشاء فاتورة مبيعات</a> | ";
echo "<a href='invoices.php?action=add&type=purchase'>إنشاء فاتورة مشتريات</a>";
echo "</p>";
?>

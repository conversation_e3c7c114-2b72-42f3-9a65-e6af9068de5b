<?php
require_once 'config/database.php';
require_once 'models/Role.php';

$roleModel = new Role();

// Check if we have roles
$roles = $roleModel->getAllRoles(false);
echo "<h2>Current Roles Count: " . count($roles) . "</h2>";

if (count($roles) < 5) {
    echo "<p>Adding sample roles for testing...</p>";
    
    // Sample roles to add
    $sampleRoles = [
        ['role_name' => 'مدير النظام', 'role_description' => 'مدير عام للنظام مع جميع الصلاحيات', 'is_active' => 1],
        ['role_name' => 'مدير المبيعات', 'role_description' => 'مسؤول عن إدارة فريق المبيعات والعملاء', 'is_active' => 1],
        ['role_name' => 'محاسب', 'role_description' => 'مسؤول عن الحسابات والتقارير المالية', 'is_active' => 1],
        ['role_name' => 'موظف استقبال', 'role_description' => 'مسؤول عن استقبال العملاء والاستفسارات', 'is_active' => 1],
        ['role_name' => 'مشرف المخزن', 'role_description' => 'مسؤول عن إدارة المخزون والمواد', 'is_active' => 1],
        ['role_name' => 'مندوب مبيعات', 'role_description' => 'مسؤول عن المبيعات الخارجية والعملاء', 'is_active' => 1],
        ['role_name' => 'دعم فني', 'role_description' => 'مسؤول عن الدعم الفني وحل المشاكل التقنية', 'is_active' => 1],
        ['role_name' => 'مراجع حسابات', 'role_description' => 'مسؤول عن مراجعة الحسابات والتدقيق', 'is_active' => 0],
    ];
    
    foreach ($sampleRoles as $role) {
        try {
            $result = $roleModel->createRole($role['role_name'], $role['role_description'], $role['is_active']);
            if ($result) {
                echo "<p>✓ تم إضافة الدور: " . $role['role_name'] . "</p>";
            } else {
                echo "<p>✗ فشل في إضافة الدور: " . $role['role_name'] . "</p>";
            }
        } catch (Exception $e) {
            echo "<p>✗ خطأ في إضافة الدور " . $role['role_name'] . ": " . $e->getMessage() . "</p>";
        }
    }
}

// Display current roles
$roles = $roleModel->getAllRoles(false);
echo "<h3>All Roles:</h3>";
echo "<ul>";
foreach ($roles as $role) {
    echo "<li>" . $role['role_name'] . " - " . ($role['is_active'] ? 'نشط' : 'غير نشط') . "</li>";
}
echo "</ul>";

echo '<p><a href="roles.php">عرض صفحة الأدوار</a></p>';
?>

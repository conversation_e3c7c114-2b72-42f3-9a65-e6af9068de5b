<?php
/**
 * سكريبت إعداد جدول العملاء
 * Setup Customers Table Script
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';

try {
    $db = Database::getInstance();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_table'])) {
        
        // إنشاء جدول العملاء
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS customers (
            customer_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف العميل الفريد',
            customer_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'كود العميل',
            customer_name VARCHAR(150) NOT NULL COMMENT 'اسم العميل',
            customer_type ENUM('customer', 'supplier', 'both') 
                          NOT NULL DEFAULT 'customer' COMMENT 'نوع العميل',
            phone VARCHAR(20) COMMENT 'رقم الهاتف',
            email VARCHAR(100) COMMENT 'البريد الإلكتروني',
            address TEXT COMMENT 'العنوان',
            city VARCHAR(50) COMMENT 'المدينة',
            tax_number VARCHAR(50) COMMENT 'الرقم الضريبي',
            credit_limit DECIMAL(12,2) DEFAULT 0.00 COMMENT 'حد الائتمان',
            payment_terms INT DEFAULT 30 COMMENT 'شروط الدفع بالأيام',
            account_balance DECIMAL(12,2) DEFAULT 0.00 COMMENT 'رصيد الحساب',
            is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط العميل',
            notes TEXT COMMENT 'ملاحظات إضافية',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
            created_by INT COMMENT 'المستخدم الذي أنشأ السجل',
            
            INDEX idx_customer_code (customer_code),
            INDEX idx_customer_name (customer_name),
            INDEX idx_customer_type (customer_type),
            INDEX idx_is_active (is_active),
            
            FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
        ) ENGINE=InnoDB COMMENT='جدول العملاء والموردين'";
        
        $db->execute($createTableSQL);
        
        // إدراج بيانات تجريبية
        $insertSQL = "
        INSERT IGNORE INTO customers (
            customer_code, customer_name, customer_type, phone, email, 
            address, city, credit_limit, payment_terms, is_active, created_by
        ) VALUES 
        ('C001', 'شركة الرياض التجارية', 'customer', '0112345678', '<EMAIL>', 
         'شارع الملك فهد، الرياض', 'الرياض', 50000.00, 30, 1, 1),
        
        ('C002', 'مؤسسة جدة للمقاولات', 'customer', '0123456789', '<EMAIL>', 
         'شارع الأمير سلطان، جدة', 'جدة', 75000.00, 45, 1, 1),
        
        ('S001', 'شركة النخيل للمواد الغذائية', 'supplier', '0167890123', '<EMAIL>', 
         'المنطقة الصناعية، الرياض', 'الرياض', 0.00, 30, 1, 1),
        
        ('S002', 'مؤسسة البحر الأحمر للاستيراد', 'supplier', '0178901234', '<EMAIL>', 
         'ميناء جدة، جدة', 'جدة', 0.00, 45, 1, 1)";
        
        $db->execute($insertSQL);
        
        $message = 'تم إنشاء جدول العملاء وإدراج البيانات التجريبية بنجاح!';
        $messageType = 'success';
    }
    
    // فحص وجود الجدول
    $tableExists = false;
    try {
        $result = $db->select("SHOW TABLES LIKE 'customers'");
        $tableExists = !empty($result);
    } catch (Exception $e) {
        // الجدول غير موجود
    }
    
    // عدد العملاء الموجودين
    $customersCount = 0;
    if ($tableExists) {
        try {
            $result = $db->selectOne("SELECT COUNT(*) as count FROM customers");
            $customersCount = $result['count'];
        } catch (Exception $e) {
            // خطأ في العد
        }
    }
    
} catch (Exception $e) {
    $message = 'خطأ: ' . $e->getMessage();
    $messageType = 'error';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جدول العملاء</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto px-4 max-w-2xl">
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-users mr-2"></i>
                إعداد جدول العملاء
            </h1>

            <?php if ($message): ?>
                <div class="mb-4 p-4 rounded <?php echo $messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'; ?>">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-times-circle'; ?> mr-2"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجدول -->
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">حالة جدول العملاء:</h3>
                <?php if ($tableExists): ?>
                    <p class="text-green-600">
                        <i class="fas fa-check-circle mr-2"></i>
                        الجدول موجود - يحتوي على <?php echo $customersCount; ?> عميل/مورد
                    </p>
                <?php else: ?>
                    <p class="text-red-600">
                        <i class="fas fa-times-circle mr-2"></i>
                        الجدول غير موجود - يحتاج إلى إنشاء
                    </p>
                <?php endif; ?>
            </div>

            <!-- نموذج الإنشاء -->
            <?php if (!$tableExists || $customersCount == 0): ?>
                <form method="POST" class="mb-6">
                    <button 
                        type="submit" 
                        name="create_table"
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded transition duration-200"
                    >
                        <i class="fas fa-plus mr-2"></i>
                        إنشاء جدول العملاء وإدراج البيانات التجريبية
                    </button>
                </form>
            <?php endif; ?>

            <!-- روابط التنقل -->
            <div class="text-center space-x-4">
                <a href="customers.php" class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-users mr-2"></i>
                    إدارة العملاء
                </a>
                <a href="index.php" class="inline-block bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-home mr-2"></i>
                    الرئيسية
                </a>
            </div>
        </div>
    </div>
</body>
</html>

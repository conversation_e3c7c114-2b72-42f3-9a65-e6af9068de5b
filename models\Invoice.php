<?php
/**
 * نموذج الفواتير
 * Invoices Model
 */

class Invoice {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الفواتير
     */
    public function getAllInvoices($type = null, $status = null, $dateFrom = null, $dateTo = null, $limit = 100, $offset = 0) {
        try {
            $query = "SELECT 
                        i.invoice_id, i.invoice_number, i.invoice_type, i.invoice_date,
                        i.customer_id, i.subtotal, i.tax_amount, i.discount_amount,
                        i.total_amount, i.status, i.payment_status, i.due_date,
                        i.notes, i.created_at, i.updated_at,
                        c.customer_name, c.customer_code,
                        u.full_name as created_by_name
                      FROM invoices i
                      LEFT JOIN customers c ON i.customer_id = c.customer_id
                      LEFT JOIN users u ON i.created_by = u.user_id
                      WHERE 1=1";
            
            $params = [];
            
            if ($type) {
                $query .= " AND i.invoice_type = ?";
                $params[] = $type;
            }
            
            if ($status) {
                $query .= " AND i.status = ?";
                $params[] = $status;
            }
            
            if ($dateFrom) {
                $query .= " AND i.invoice_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $query .= " AND i.invoice_date <= ?";
                $params[] = $dateTo;
            }
            
            $query .= " ORDER BY i.invoice_date DESC, i.invoice_number DESC";
            
            if ($limit) {
                $query .= " LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
            }
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Get all invoices error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على فاتورة بالمعرف
     */
    public function getInvoiceById($invoiceId) {
        try {
            $query = "SELECT 
                        i.*, c.customer_name, c.customer_code, c.address, c.phone,
                        u.full_name as created_by_name
                      FROM invoices i
                      LEFT JOIN customers c ON i.customer_id = c.customer_id
                      LEFT JOIN users u ON i.created_by = u.user_id
                      WHERE i.invoice_id = ?";
            
            return $this->db->selectOne($query, [$invoiceId]);
            
        } catch (Exception $e) {
            error_log("Get invoice by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * الحصول على بنود الفاتورة
     */
    public function getInvoiceItems($invoiceId) {
        try {
            $query = "SELECT 
                        ii.*, i.item_name, i.item_code, u.unit_name, u.unit_symbol
                      FROM invoice_items ii
                      JOIN items i ON ii.item_id = i.item_id
                      LEFT JOIN units u ON i.unit_id = u.unit_id
                      WHERE ii.invoice_id = ?
                      ORDER BY ii.line_number";
            
            return $this->db->select($query, [$invoiceId]);
            
        } catch (Exception $e) {
            error_log("Get invoice items error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إنشاء فاتورة جديدة
     */
    public function createInvoice($invoiceData, $items) {
        try {
            $this->db->beginTransaction();
            
            // إنتاج رقم الفاتورة إذا لم يكن موجوداً
            $invoiceNumber = $invoiceData['invoice_number'] ?? $this->generateInvoiceNumber($invoiceData['invoice_type']);
            
            // حساب الإجماليات
            $subtotal = 0;
            foreach ($items as $item) {
                $subtotal += $item['quantity'] * $item['unit_price'];
            }
            
            $discountAmount = $invoiceData['discount_amount'] ?? 0;
            $taxAmount = $invoiceData['tax_amount'] ?? 0;
            $totalAmount = $subtotal - $discountAmount + $taxAmount;
            
            // إدراج الفاتورة
            $query = "INSERT INTO invoices (
                        invoice_number, invoice_type, invoice_date, customer_id,
                        subtotal, discount_amount, tax_amount, total_amount,
                        status, due_date, notes, created_by
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $params = [
                $invoiceNumber,
                $invoiceData['invoice_type'],
                $invoiceData['invoice_date'] ?? date('Y-m-d'),
                $invoiceData['customer_id'],
                $subtotal,
                $discountAmount,
                $taxAmount,
                $totalAmount,
                $invoiceData['status'] ?? 'draft',
                $invoiceData['due_date'] ?? null,
                $invoiceData['notes'] ?? null,
                $_SESSION['user_id'] ?? null
            ];
            
            $invoiceId = $this->db->insert($query, $params);
            
            if (!$invoiceId) {
                throw new Exception('فشل في إنشاء الفاتورة');
            }
            
            // إدراج بنود الفاتورة
            foreach ($items as $item) {
                $itemQuery = "INSERT INTO invoice_items (
                                invoice_id, item_name, quantity, unit_price,
                                total_price, discount_amount
                              ) VALUES (?, ?, ?, ?, ?, ?)";

                $totalPrice = $item['quantity'] * $item['unit_price'];
                $discountAmount = $item['discount_amount'] ?? 0;
                $finalPrice = $totalPrice - $discountAmount;

                $itemParams = [
                    $invoiceId,
                    $item['item_name'] ?? 'عنصر',
                    $item['quantity'],
                    $item['unit_price'],
                    $finalPrice,
                    $discountAmount
                ];

                $this->db->insert($itemQuery, $itemParams);
            }
            
            $this->db->commit();
            
            logActivity('إنشاء فاتورة', "تم إنشاء الفاتورة رقم: {$invoiceNumber}");
            
            return $invoiceId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create invoice error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الفاتورة
     */
    public function updateInvoice($invoiceId, $invoiceData, $items = null) {
        try {
            $this->db->beginTransaction();
            
            // تحديث بيانات الفاتورة
            $setParts = [];
            $params = [];
            
            if (isset($invoiceData['invoice_date'])) {
                $setParts[] = "invoice_date = ?";
                $params[] = $invoiceData['invoice_date'];
            }
            
            if (isset($invoiceData['customer_id'])) {
                $setParts[] = "customer_id = ?";
                $params[] = $invoiceData['customer_id'];
            }
            
            if (isset($invoiceData['status'])) {
                $setParts[] = "status = ?";
                $params[] = $invoiceData['status'];
            }
            
            if (isset($invoiceData['payment_status'])) {
                $setParts[] = "payment_status = ?";
                $params[] = $invoiceData['payment_status'];
            }
            
            if (isset($invoiceData['due_date'])) {
                $setParts[] = "due_date = ?";
                $params[] = $invoiceData['due_date'];
            }
            
            if (isset($invoiceData['notes'])) {
                $setParts[] = "notes = ?";
                $params[] = $invoiceData['notes'];
            }
            
            if (!empty($setParts)) {
                $setParts[] = "updated_at = NOW()";
                $params[] = $invoiceId;
                
                $query = "UPDATE invoices SET " . implode(', ', $setParts) . " WHERE invoice_id = ?";
                $this->db->update($query, $params);
            }
            
            // تحديث البنود إذا تم تمريرها
            if ($items !== null) {
                // حذف البنود القديمة
                $this->db->delete("DELETE FROM invoice_items WHERE invoice_id = ?", [$invoiceId]);
                
                // إضافة البنود الجديدة
                $subtotal = 0;
                $lineNumber = 1;
                
                foreach ($items as $item) {
                    $itemQuery = "INSERT INTO invoice_items (
                                    invoice_id, line_number, item_id, quantity,
                                    unit_price, discount_amount, total_amount
                                  ) VALUES (?, ?, ?, ?, ?, ?, ?)";
                    
                    $itemTotal = $item['quantity'] * $item['unit_price'] - ($item['discount_amount'] ?? 0);
                    $subtotal += $itemTotal;
                    
                    $itemParams = [
                        $invoiceId,
                        $lineNumber,
                        $item['item_id'],
                        $item['quantity'],
                        $item['unit_price'],
                        $item['discount_amount'] ?? 0,
                        $itemTotal
                    ];
                    
                    $this->db->insert($itemQuery, $itemParams);
                    $lineNumber++;
                }
                
                // تحديث إجماليات الفاتورة
                $discountAmount = $invoiceData['discount_amount'] ?? 0;
                $taxAmount = $invoiceData['tax_amount'] ?? 0;
                $totalAmount = $subtotal - $discountAmount + $taxAmount;
                
                $this->db->update(
                    "UPDATE invoices SET subtotal = ?, discount_amount = ?, tax_amount = ?, total_amount = ? WHERE invoice_id = ?",
                    [$subtotal, $discountAmount, $taxAmount, $totalAmount, $invoiceId]
                );
            }
            
            $this->db->commit();
            
            logActivity('تحديث فاتورة', "تم تحديث الفاتورة ID: {$invoiceId}");
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Update invoice error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الفاتورة
     */
    public function deleteInvoice($invoiceId) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من حالة الفاتورة
            $invoice = $this->getInvoiceById($invoiceId);
            if (!$invoice) {
                throw new Exception('الفاتورة غير موجودة');
            }
            
            if ($invoice['status'] === 'posted') {
                throw new Exception('لا يمكن حذف فاتورة مرحلة');
            }
            
            // حذف بنود الفاتورة
            $this->db->delete("DELETE FROM invoice_items WHERE invoice_id = ?", [$invoiceId]);
            
            // حذف الفاتورة
            $this->db->delete("DELETE FROM invoices WHERE invoice_id = ?", [$invoiceId]);
            
            $this->db->commit();
            
            logActivity('حذف فاتورة', "تم حذف الفاتورة رقم: {$invoice['invoice_number']}");
            
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Delete invoice error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * إنتاج رقم فاتورة تلقائي
     */
    private function generateInvoiceNumber($invoiceType) {
        $prefix = $invoiceType === 'sales' ? 'INV' : 'PUR';
        $date = date('Ymd');
        
        // الحصول على آخر رقم فاتورة لهذا النوع واليوم
        $lastInvoice = $this->db->selectOne("
            SELECT invoice_number FROM invoices 
            WHERE invoice_type = ? AND invoice_number LIKE ? 
            ORDER BY invoice_number DESC LIMIT 1
        ", [$invoiceType, "{$prefix}-{$date}-%"]);
        
        $sequence = 1;
        if ($lastInvoice) {
            $parts = explode('-', $lastInvoice['invoice_number']);
            if (count($parts) >= 3) {
                $sequence = intval($parts[2]) + 1;
            }
        }
        
        return sprintf("%s-%s-%04d", $prefix, $date, $sequence);
    }
    
    /**
     * البحث في الفواتير
     */
    public function searchInvoices($searchTerm, $type = null, $dateFrom = null, $dateTo = null) {
        try {
            $query = "SELECT 
                        i.invoice_id, i.invoice_number, i.invoice_type, i.invoice_date,
                        i.total_amount, i.status, c.customer_name
                      FROM invoices i
                      LEFT JOIN customers c ON i.customer_id = c.customer_id
                      WHERE (i.invoice_number LIKE ? OR c.customer_name LIKE ? OR i.notes LIKE ?)";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($type) {
                $query .= " AND i.invoice_type = ?";
                $params[] = $type;
            }
            
            if ($dateFrom) {
                $query .= " AND i.invoice_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $query .= " AND i.invoice_date <= ?";
                $params[] = $dateTo;
            }
            
            $query .= " ORDER BY i.invoice_date DESC LIMIT 100";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search invoices error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الفواتير
     */
    public function getInvoicesStats($type = null, $dateFrom = null, $dateTo = null) {
        try {
            $query = "SELECT 
                        COUNT(*) as total_invoices,
                        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_invoices,
                        COUNT(CASE WHEN status = 'posted' THEN 1 END) as posted_invoices,
                        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_invoices,
                        COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_invoices,
                        COALESCE(SUM(total_amount), 0) as total_amount,
                        COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN total_amount END), 0) as paid_amount,
                        COALESCE(SUM(CASE WHEN payment_status = 'unpaid' THEN total_amount END), 0) as unpaid_amount
                      FROM invoices WHERE 1=1";
            
            $params = [];
            
            if ($type) {
                $query .= " AND invoice_type = ?";
                $params[] = $type;
            }
            
            if ($dateFrom) {
                $query .= " AND invoice_date >= ?";
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $query .= " AND invoice_date <= ?";
                $params[] = $dateTo;
            }
            
            $result = $this->db->selectOne($query, $params);
            
            return $result ?: [
                'total_invoices' => 0,
                'draft_invoices' => 0,
                'posted_invoices' => 0,
                'paid_invoices' => 0,
                'unpaid_invoices' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'unpaid_amount' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get invoices stats error: " . $e->getMessage());
            return [
                'total_invoices' => 0,
                'draft_invoices' => 0,
                'posted_invoices' => 0,
                'paid_invoices' => 0,
                'unpaid_invoices' => 0,
                'total_amount' => 0,
                'paid_amount' => 0,
                'unpaid_amount' => 0
            ];
        }
    }
}

?>

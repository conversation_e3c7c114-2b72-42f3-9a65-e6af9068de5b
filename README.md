# نظام الحسابات والمخازن - فيزي سوفت

## نظرة عامة
نظام إدارة محاسبي ومخزني متكامل يدعم الأصناف متعددة الوحدات مع واجهات مستخدم حديثة ومتجاوبة.

## الميزات الرئيسية

### 🏗️ **الهيكل التقني**
- **Backend**: PHP 7.4+ مع نمط MVC
- **Frontend**: HTML5 + Tailwind CSS (بدون JavaScript)
- **Database**: MySQL 8.0+ مع دعم UTF-8
- **Security**: حماية CSRF، تشفير كلمات المرور، جلسات آمنة

### 📦 **إدارة الأصناف**
- أصناف متعددة الوحدات مع معاملات تحويل
- دعم الباركود والأرقام التسلسلية
- تواريخ انتهاء الصلاحية
- مستويات المخزون (حد أدنى، أقصى، إعادة طلب)
- أسعار متعددة (شراء، بيع، جملة)
- فئات هرمية للأصناف

### 🏪 **إدارة المخازن**
- مخازن متعددة مع أرصدة منفصلة
- حركات المخزون (إدخال، إخراج، تحويل)
- تتبع التكلفة بطرق متعددة (FIFO, LIFO, متوسط مرجح)
- تقارير المخزون والحركات

### 💰 **النظام المحاسبي**
- شجرة حسابات متكاملة
- قيود محاسبية تلقائية ويدوية
- ميزان المراجعة والتقارير المالية
- ربط المخزون بالحسابات

### 👥 **إدارة المستخدمين**
- نظام صلاحيات متقدم
- أدوار متعددة (مدير، محاسب، أمين مخزن، مبيعات)
- سجل أنشطة المستخدمين
- جلسات آمنة

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 8.0 أو أحدث
- Apache/Nginx
- مساحة تخزين: 500 ميجابايت على الأقل

### المتصفح
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## التثبيت

### 1. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p < database_schema.sql
```

### 2. إعداد الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /var/www/html/fizisoft/

# تعيين الصلاحيات
chmod 755 -R /var/www/html/fizisoft/
chmod 777 logs/ uploads/ reports/ temp/
```

### 3. إعداد قاعدة البيانات
```php
// تعديل config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'accounting_inventory_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 4. الوصول للنظام
- URL: `http://your-domain/fizisoft/`
- اسم المستخدم: `admin`
- كلمة المرور: `password`

## هيكل المشروع

```
fizisoft/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   ├── init.php             # ملف التهيئة الرئيسي
│   ├── functions.php        # الدوال المساعدة
│   └── Database.php         # فئة قاعدة البيانات
├── models/
│   ├── User.php            # نموذج المستخدمين
│   ├── Item.php            # نموذج الأصناف
│   ├── Category.php        # نموذج الفئات
│   └── Unit.php            # نموذج الوحدات
├── assets/
│   └── css/
│       └── style.css       # الأنماط المخصصة
├── logs/                   # ملفات السجلات
├── uploads/               # الملفات المرفوعة
├── reports/               # التقارير المُنتجة
├── temp/                  # الملفات المؤقتة
├── index.php             # الصفحة الرئيسية
├── login.php             # صفحة تسجيل الدخول
├── dashboard.php         # لوحة التحكم
├── items.php             # إدارة الأصناف
└── database_schema.sql   # هيكل قاعدة البيانات
```

## الاستخدام

### تسجيل الدخول
1. انتقل إلى صفحة تسجيل الدخول
2. أدخل اسم المستخدم وكلمة المرور
3. سيتم توجيهك إلى لوحة التحكم

### إدارة الأصناف
1. من لوحة التحكم، اختر "إدارة الأصناف"
2. لإضافة صنف جديد: اضغط "إضافة صنف جديد"
3. املأ البيانات المطلوبة (الكود، الاسم، الوحدة الأساسية)
4. حدد الفئة والأسعار ومستويات المخزون
5. احفظ الصنف

### البحث والفلترة
- استخدم شريط البحث للبحث بالاسم أو الكود أو الباركود
- اختر فئة محددة من القائمة المنسدلة
- استخدم الترقيم للتنقل بين الصفحات

## الأمان

### الحماية المُطبقة
- **CSRF Protection**: حماية من هجمات Cross-Site Request Forgery
- **XSS Prevention**: تنظيف جميع المدخلات
- **SQL Injection**: استخدام Prepared Statements
- **Session Security**: جلسات آمنة مع انتهاء صلاحية
- **Password Hashing**: تشفير كلمات المرور بـ bcrypt

### أفضل الممارسات
- تغيير كلمة مرور المدير الافتراضية
- تحديث النظام بانتظام
- عمل نسخ احتياطية دورية
- مراقبة سجلات الأنشطة

## الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p accounting_inventory_system > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/fizisoft/
```

### تنظيف السجلات
```sql
-- حذف السجلات القديمة (أكثر من 6 أشهر)
DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### مراقبة الأداء
- مراقبة حجم قاعدة البيانات
- فحص سجلات الأخطاء في مجلد `logs/`
- مراقبة استخدام مساحة التخزين

## التطوير

### إضافة ميزات جديدة
1. إنشاء النموذج في مجلد `models/`
2. إضافة الجداول المطلوبة في قاعدة البيانات
3. إنشاء الواجهات في الملفات الرئيسية
4. تحديث نظام الصلاحيات إذا لزم الأمر

### معايير الكود
- استخدام نمط MVC
- تعليقات باللغة العربية
- التحقق من الأمان في جميع المدخلات
- استخدام Prepared Statements لقاعدة البيانات

## الدعم والمساعدة

### الأخطاء الشائعة
1. **خطأ الاتصال بقاعدة البيانات**: تحقق من إعدادات `config/database.php`
2. **صفحة بيضاء**: فعّل عرض الأخطاء في `config/database.php`
3. **مشاكل الصلاحيات**: تحقق من صلاحيات المجلدات

### سجلات الأخطاء
- سجلات قاعدة البيانات: `logs/database_errors.log`
- سجلات PHP: `logs/php_errors.log`
- سجلات الأنشطة: جدول `activity_logs`

## الترخيص
هذا النظام مطور بواسطة فريق فيزي سوفت لأغراض تعليمية وتجارية.

## معلومات الاتصال
- **المطور**: فريق فيزي سوفت
- **الإصدار**: 1.0.0
- **التاريخ**: 2025-06-30

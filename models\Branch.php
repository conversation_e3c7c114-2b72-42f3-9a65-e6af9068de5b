<?php
/**
 * نموذج الفروع
 * Branch Model
 */

class Branch {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * الحصول على جميع الفروع
     */
    public function getAllBranches($activeOnly = true) {
        try {
            $query = "SELECT 
                        branch_id, branch_code, branch_name, address, city, 
                        phone, email, manager_name, is_active, created_at
                      FROM branches";
            
            if ($activeOnly) {
                $query .= " WHERE is_active = 1";
            }
            
            $query .= " ORDER BY branch_name";
            
            return $this->db->select($query);
            
        } catch (Exception $e) {
            error_log("Get all branches error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على فرع بالمعرف
     */
    public function getBranchById($branchId) {
        try {
            $query = "SELECT * FROM branches WHERE branch_id = ?";
            return $this->db->selectOne($query, [$branchId]);
            
        } catch (Exception $e) {
            error_log("Get branch by ID error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء فرع جديد
     */
    public function createBranch($data) {
        try {
            // التحقق من عدم وجود كود الفرع
            if ($this->branchCodeExists($data['branch_code'])) {
                throw new Exception('كود الفرع موجود بالفعل');
            }
            
            $query = "INSERT INTO branches (
                        branch_code, branch_name, address, city, phone, 
                        email, manager_name, is_active, created_by, created_at
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $data['branch_code'],
                $data['branch_name'],
                $data['address'] ?? null,
                $data['city'] ?? null,
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['manager_name'] ?? null,
                $data['is_active'] ?? 1,
                $_SESSION['user_id'] ?? null
            ];
            
            $branchId = $this->db->insert($query, $params);
            
            if ($branchId) {
                logActivity('إنشاء فرع', "تم إنشاء الفرع: {$data['branch_name']}");
            }
            
            return $branchId;
            
        } catch (Exception $e) {
            error_log("Create branch error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * تحديث الفرع
     */
    public function updateBranch($branchId, $data) {
        try {
            $setParts = [];
            $params = [];
            
            if (isset($data['branch_name'])) {
                $setParts[] = "branch_name = ?";
                $params[] = $data['branch_name'];
            }
            
            if (isset($data['address'])) {
                $setParts[] = "address = ?";
                $params[] = $data['address'];
            }
            
            if (isset($data['city'])) {
                $setParts[] = "city = ?";
                $params[] = $data['city'];
            }
            
            if (isset($data['phone'])) {
                $setParts[] = "phone = ?";
                $params[] = $data['phone'];
            }
            
            if (isset($data['email'])) {
                $setParts[] = "email = ?";
                $params[] = $data['email'];
            }
            
            if (isset($data['manager_name'])) {
                $setParts[] = "manager_name = ?";
                $params[] = $data['manager_name'];
            }
            
            if (isset($data['is_active'])) {
                $setParts[] = "is_active = ?";
                $params[] = $data['is_active'];
            }
            
            if (empty($setParts)) {
                return false;
            }
            
            $setParts[] = "updated_at = NOW()";
            $params[] = $branchId;
            
            $query = "UPDATE branches SET " . implode(', ', $setParts) . " WHERE branch_id = ?";
            
            $result = $this->db->update($query, $params);
            
            if ($result) {
                logActivity('تحديث فرع', "تم تحديث الفرع ID: {$branchId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Update branch error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * حذف الفرع (إلغاء تفعيل)
     */
    public function deleteBranch($branchId) {
        try {
            // التحقق من عدم وجود مخازن مرتبطة
            $warehouseCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM warehouses WHERE branch_id = ? AND is_active = 1",
                [$branchId]
            );
            
            if ($warehouseCount['count'] > 0) {
                throw new Exception('لا يمكن حذف الفرع لوجود مخازن مرتبطة به');
            }
            
            $query = "UPDATE branches SET is_active = 0, updated_at = NOW() WHERE branch_id = ?";
            $result = $this->db->update($query, [$branchId]);
            
            if ($result) {
                logActivity('حذف فرع', "تم حذف الفرع ID: {$branchId}");
            }
            
            return $result > 0;
            
        } catch (Exception $e) {
            error_log("Delete branch error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * فحص وجود كود الفرع
     */
    private function branchCodeExists($branchCode, $excludeBranchId = null) {
        $query = "SELECT COUNT(*) as count FROM branches WHERE branch_code = ?";
        $params = [$branchCode];
        
        if ($excludeBranchId) {
            $query .= " AND branch_id != ?";
            $params[] = $excludeBranchId;
        }
        
        $result = $this->db->selectOne($query, $params);
        return $result['count'] > 0;
    }
    
    /**
     * البحث في الفروع
     */
    public function searchBranches($searchTerm, $activeOnly = true) {
        try {
            $query = "SELECT 
                        branch_id, branch_code, branch_name, address, city, manager_name
                      FROM branches 
                      WHERE (branch_name LIKE ? OR branch_code LIKE ? OR city LIKE ?)";
            
            $params = ["%{$searchTerm}%", "%{$searchTerm}%", "%{$searchTerm}%"];
            
            if ($activeOnly) {
                $query .= " AND is_active = 1";
            }
            
            $query .= " ORDER BY branch_name LIMIT 50";
            
            return $this->db->select($query, $params);
            
        } catch (Exception $e) {
            error_log("Search branches error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على إحصائيات الفرع
     */
    public function getBranchStats($branchId) {
        try {
            $stats = $this->db->selectOne("
                SELECT 
                    COUNT(DISTINCT w.warehouse_id) as total_warehouses,
                    COUNT(DISTINCT ib.item_id) as total_items,
                    COALESCE(SUM(ib.total_value), 0) as total_inventory_value
                FROM warehouses w
                LEFT JOIN inventory_balances ib ON w.warehouse_id = ib.warehouse_id
                WHERE w.branch_id = ? AND w.is_active = 1
            ", [$branchId]);
            
            return $stats ?: [
                'total_warehouses' => 0,
                'total_items' => 0,
                'total_inventory_value' => 0
            ];
            
        } catch (Exception $e) {
            error_log("Get branch stats error: " . $e->getMessage());
            return [
                'total_warehouses' => 0,
                'total_items' => 0,
                'total_inventory_value' => 0
            ];
        }
    }
}

?>

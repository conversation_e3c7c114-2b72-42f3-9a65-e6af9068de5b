<?php
/**
 * صفحة طباعة التقرير
 * Report Print Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيلات النماذج
$templateModel = new PrintTemplate();
$settingModel = new Setting();

// الحصول على معاملات التقرير
$reportType = $_GET['type'] ?? null;
$templateId = $_GET['template_id'] ?? null;
$reportTitle = $_GET['title'] ?? 'تقرير';
$reportContent = $_GET['content'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

if (!$reportType || !$reportContent) {
    header('Location: reports.php');
    exit;
}

// فك تشفير محتوى التقرير
$reportContent = base64_decode($reportContent);

// الحصول على القالب
if ($templateId) {
    $template = $templateModel->getTemplateById($templateId);
} else {
    $template = $templateModel->getDefaultTemplate('report');
}

if (!$template) {
    // إنشاء قالب افتراضي إذا لم يوجد
    $templateModel->createDefaultTemplates();
    $template = $templateModel->getDefaultTemplate('report');
}

// الحصول على معلومات الشركة
$companyInfo = $settingModel->getCompanySettings();

// إعداد متغيرات القالب
$variables = [
    // معلومات الشركة
    'company_name' => $companyInfo['name'] ?? 'اسم الشركة',
    'company_address' => $companyInfo['address'] ?? '',
    'company_phone' => $companyInfo['phone'] ?? '',
    'company_email' => $companyInfo['email'] ?? '',
    'company_website' => $companyInfo['website'] ?? '',
    
    // معلومات التقرير
    'report_title' => $reportTitle,
    'report_type' => $reportType,
    'report_date' => formatDate(date('Y-m-d')),
    'report_content' => $reportContent,
    'date_from' => $dateFrom ? formatDate($dateFrom) : '',
    'date_to' => $dateTo ? formatDate($dateTo) : '',
    'generated_at' => formatDateTime(date('Y-m-d H:i:s')),
    'generated_by' => $_SESSION['full_name'] ?? ''
];

// معالجة القالب
$processedContent = $templateModel->processTemplate($template['template_content'], $variables);

$pageTitle = 'طباعة التقرير - ' . $reportTitle;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- تنسيق القالب -->
    <style>
        <?php echo $template['template_css']; ?>
        
        /* إعدادات الطباعة */
        @page {
            size: <?php echo $template['paper_size']; ?> <?php echo $template['orientation']; ?>;
            margin: <?php echo $template['margins']; ?>;
        }
        
        /* إخفاء أزرار التحكم عند الطباعة */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
        }
        
        /* أزرار التحكم */
        .print-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }
        
        .print-controls button {
            margin: 0 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Tajawal', Arial, sans-serif;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn-print {
            background: #3b82f6;
            color: white;
        }
        
        .btn-print:hover {
            background: #2563eb;
        }
        
        .btn-back {
            background: #6b7280;
            color: white;
        }
        
        .btn-back:hover {
            background: #4b5563;
        }
        
        .btn-template {
            background: #8b5cf6;
            color: white;
        }
        
        .btn-template:hover {
            background: #7c3aed;
        }
    </style>
</head>
<body>

    <!-- أزرار التحكم -->
    <div class="print-controls no-print">
        <button onclick="window.print()" class="btn-print">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button onclick="window.history.back()" class="btn-back">
            <i class="fas fa-arrow-right"></i> رجوع
        </button>
        <button onclick="showTemplateSelector()" class="btn-template">
            <i class="fas fa-palette"></i> تغيير القالب
        </button>
    </div>

    <!-- محتوى التقرير -->
    <div class="report-content">
        <?php echo $processedContent; ?>
    </div>

    <!-- نافذة اختيار القالب -->
    <div id="templateModal" class="no-print" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 500px; width: 90%;">
            <h3 style="margin-top: 0; text-align: center;">اختيار قالب الطباعة</h3>
            <div id="templateList">
                <!-- سيتم تحميل القوالب هنا -->
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="hideTemplateSelector()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // طباعة تلقائية إذا كان المعامل موجود
        <?php if (isset($_GET['auto_print'])): ?>
        window.onload = function() {
            window.print();
        };
        <?php endif; ?>
        
        // عرض نافذة اختيار القالب
        function showTemplateSelector() {
            document.getElementById('templateModal').style.display = 'block';
            loadTemplates();
        }
        
        // إخفاء نافذة اختيار القالب
        function hideTemplateSelector() {
            document.getElementById('templateModal').style.display = 'none';
        }
        
        // تحميل قائمة القوالب
        function loadTemplates() {
            fetch('ajax/get_templates.php?type=report')
                .then(response => response.json())
                .then(data => {
                    const templateList = document.getElementById('templateList');
                    templateList.innerHTML = '';
                    
                    if (data.success && data.templates.length > 0) {
                        data.templates.forEach(template => {
                            const templateDiv = document.createElement('div');
                            templateDiv.style.cssText = 'padding: 10px; margin: 5px 0; border: 1px solid #e5e7eb; border-radius: 4px; cursor: pointer; transition: all 0.2s;';
                            templateDiv.innerHTML = `
                                <strong>${template.template_name}</strong>
                                ${template.is_default ? '<span style="color: #10b981; font-size: 12px;">(افتراضي)</span>' : ''}
                                <br>
                                <small style="color: #6b7280;">${template.paper_size} - ${template.orientation === 'portrait' ? 'عمودي' : 'أفقي'}</small>
                            `;
                            
                            templateDiv.addEventListener('click', () => {
                                const currentUrl = new URL(window.location.href);
                                currentUrl.searchParams.set('template_id', template.template_id);
                                window.location.href = currentUrl.toString();
                            });
                            
                            templateDiv.addEventListener('mouseenter', () => {
                                templateDiv.style.backgroundColor = '#f3f4f6';
                            });
                            
                            templateDiv.addEventListener('mouseleave', () => {
                                templateDiv.style.backgroundColor = 'white';
                            });
                            
                            templateList.appendChild(templateDiv);
                        });
                    } else {
                        templateList.innerHTML = '<p style="text-align: center; color: #6b7280;">لا توجد قوالب متاحة</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading templates:', error);
                    document.getElementById('templateList').innerHTML = '<p style="text-align: center; color: #ef4444;">خطأ في تحميل القوالب</p>';
                });
        }
        
        // إغلاق النافذة عند النقر خارجها
        document.getElementById('templateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideTemplateSelector();
            }
        });
    </script>

</body>
</html>

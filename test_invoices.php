<?php
/**
 * ملف اختبار لصفحة الفواتير
 * Test file for invoices page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

echo "<h1>اختبار متغيرات صفحة الفواتير</h1>";

// اختبار المتغيرات الأساسية
$action = $_GET['action'] ?? 'list';
$invoiceId = $_GET['id'] ?? null;

echo "<h2>المتغيرات:</h2>";
echo "<p><strong>Action:</strong> " . htmlspecialchars($action) . "</p>";
echo "<p><strong>Invoice ID:</strong> " . htmlspecialchars($invoiceId ?? 'غير محدد') . "</p>";

// اختبار النماذج
try {
    echo "<h2>اختبار النماذج:</h2>";
    
    $invoiceModel = new Invoice();
    echo "<p>✅ نموذج الفواتير: تم تحميله بنجاح</p>";
    
    $customerModel = new Customer();
    echo "<p>✅ نموذج العملاء: تم تحميله بنجاح</p>";
    
    $warehouseModel = new Warehouse();
    echo "<p>✅ نموذج المخازن: تم تحميله بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في تحميل النماذج: " . $e->getMessage() . "</p>";
}

// اختبار الدوال
try {
    echo "<h2>اختبار الدوال:</h2>";
    
    if (function_exists('sanitizeInput')) {
        echo "<p>✅ دالة sanitizeInput: موجودة</p>";
    } else {
        echo "<p>❌ دالة sanitizeInput: غير موجودة</p>";
    }
    
    if (function_exists('formatMoney')) {
        echo "<p>✅ دالة formatMoney: موجودة</p>";
    } else {
        echo "<p>❌ دالة formatMoney: غير موجودة</p>";
    }
    
    if (function_exists('formatDate')) {
        echo "<p>✅ دالة formatDate: موجودة</p>";
    } else {
        echo "<p>❌ دالة formatDate: غير موجودة</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في اختبار الدوال: " . $e->getMessage() . "</p>";
}

// اختبار قاعدة البيانات
try {
    echo "<h2>اختبار قاعدة البيانات:</h2>";
    
    $db = Database::getInstance();
    echo "<p>✅ الاتصال بقاعدة البيانات: نجح</p>";
    
    // اختبار جدول الفواتير
    $invoices = $db->select("SELECT COUNT(*) as count FROM invoices");
    echo "<p>✅ جدول الفواتير: موجود (" . $invoices[0]['count'] . " فاتورة)</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار الجلسة
echo "<h2>اختبار الجلسة:</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p>✅ المستخدم مسجل دخول: " . htmlspecialchars($_SESSION['full_name'] ?? 'غير محدد') . "</p>";
} else {
    echo "<p>❌ المستخدم غير مسجل دخول</p>";
}

echo "<h2>روابط الاختبار:</h2>";
echo "<ul>";
echo "<li><a href='invoices.php'>قائمة الفواتير</a></li>";
echo "<li><a href='invoices.php?action=view&id=1'>عرض فاتورة رقم 1</a></li>";
echo "<li><a href='invoices.php?action=add'>إضافة فاتورة جديدة</a></li>";
echo "</ul>";

echo "<p><a href='index.php'>العودة للرئيسية</a></p>";
?>
